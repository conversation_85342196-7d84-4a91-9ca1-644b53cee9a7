import 'dart:convert';
import 'package:http/http.dart' as http;

/// 测试API端点的简单脚本
void main() async {
  print('🔍 开始测试API端点...');
  
  final baseUrl = 'https://api.dznovel.top/api';
  
  // 测试健康检查
  await testEndpoint('健康检查', '$baseUrl/health?_api_path=health');
  
  // 测试套餐获取
  await testEndpoint('获取套餐', '$baseUrl/packages?_api_path=packages');
  
  // 测试订单端点（需要认证，预期401）
  await testEndpoint('获取用户订单', '$baseUrl/orders/my?_api_path=orders/my');
  
  print('\n📋 测试总结:');
  print('- 健康检查端点应该返回200');
  print('- 套餐端点应该返回200');
  print('- 订单端点应该返回401（未认证）');
}

Future<void> testEndpoint(String name, String url) async {
  try {
    print('\n🔍 测试 $name: $url');
    
    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
      },
    ).timeout(Duration(seconds: 10));
    
    print('   状态码: ${response.statusCode}');
    
    if (response.body.length < 500) {
      print('   响应: ${response.body}');
    } else {
      print('   响应长度: ${response.body.length} 字符');
    }
    
    if (response.statusCode == 200) {
      print('   ✅ 成功');
    } else if (response.statusCode == 401 && url.contains('orders')) {
      print('   ✅ 预期的401错误（需要认证）');
    } else {
      print('   ❌ 意外的状态码');
    }
    
  } catch (e) {
    print('   ❌ 请求失败: $e');
  }
}
