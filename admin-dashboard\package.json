{"name": "novel-app-admin", "version": "1.0.0", "description": "小说应用后台管理系统", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:prod": "vue-tsc && vite build --mode production", "preview": "vite preview", "serve": "vite preview --port 8080", "serve:prod": "vite preview --port 8080 --mode production"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^4.5.2", "typescript": "^5.3.3", "vite": "^5.0.10", "vue-tsc": "^1.8.25", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}}