const jsonServer = require('json-server');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

// 确保数据目录存在
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 确保db.json文件存在
const dbPath = path.join(dataDir, 'db.json');
if (!fs.existsSync(dbPath)) {
  // 复制原始db.json文件
  fs.copyFileSync(path.join(__dirname, 'db.json'), dbPath);
}

const server = jsonServer.create();
const router = jsonServer.router(dbPath);
const middlewares = jsonServer.defaults();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB限制
});

const SECRET_KEY = 'your-secret-key';
const db = router.db; // 获取数据库实例

// 密码哈希函数（与Flutter端保持一致）
function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

// 启用CORS
server.use(cors());
server.use(middlewares);
server.use(jsonServer.bodyParser);

// 添加请求日志中间件
server.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
  next();
});

// 生成JWT Token
function generateToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '24h' });
}

// 生成刷新Token
function generateRefreshToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '30d' });
}

// 验证Token中间件
function verifyToken(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ success: false, message: '未提供认证令牌' });
  }

  const token = authHeader.split(' ')[1];
  try {
    const decoded = jwt.verify(token, SECRET_KEY);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    return res.status(401).json({ success: false, message: '无效的认证令牌' });
  }
}

// 发送验证码
server.post('/auth/send-code', (req, res) => {
  const { phoneNumber } = req.body;
  
  // 模拟发送验证码
  console.log(`发送验证码到 ${phoneNumber}: 123456`);
  
  res.json({
    success: true,
    message: '验证码发送成功'
  });
});

// 验证验证码
server.post('/auth/verify-code', (req, res) => {
  const { phoneNumber, code } = req.body;
  
  // 模拟验证码验证（固定为123456）
  if (code === '123456') {
    res.json({
      success: true,
      message: '验证码验证成功'
    });
  } else {
    res.status(400).json({
      success: false,
      message: '验证码错误'
    });
  }
});

// 用户注册
server.post('/auth/register', (req, res) => {
  const { username, password, phoneNumber, verificationCode, memberCode } = req.body;
  
  // 检查用户是否已存在
  const existingUser = db.get('users').find({ username }).value() || 
                      db.get('users').find({ phoneNumber }).value();
  
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: '用户名或手机号已存在'
    });
  }
  
  // 验证会员码
  let memberInfo = null;
  if (memberCode) {
    const code = db.get('memberCodes').find({ code: memberCode, isUsed: false }).value();
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '会员码无效'
      });
    }
    memberInfo = code;
  }
  
  // 创建新用户
  const userId = uuidv4();
  const newUser = {
    id: userId,
    username,
    phoneNumber,
    email: null,
    avatar: null,
    passwordHash: password, // 存储密码哈希（Flutter端已经哈希过了）
    isMember: !!memberInfo,
    memberExpireTime: memberInfo ? null : null,
    membershipType: memberInfo ? 'permanent' : 'none',
    isPermanentMember: !!memberInfo,
    memberCode: memberCode || null,
    isDataSyncEnabled: true,
    settings: {
      enableBiometric: false,
      autoSync: true,
      enableNotification: true,
      theme: 'system',
      language: 'zh-CN'
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  db.get('users').push(newUser).write();
  
  // 如果使用了会员码，标记为已使用
  if (memberInfo) {
    db.get('memberCodes')
      .find({ code: memberCode })
      .assign({
        isUsed: true,
        usedBy: userId,
        usedAt: new Date().toISOString()
      })
      .write();
  }
  
  // 生成Token
  const token = generateToken(userId);
  const refreshToken = generateRefreshToken(userId);
  
  res.json({
    success: true,
    data: {
      token,
      refreshToken,
      user: newUser,
      expiresIn: 86400
    }
  });
});

// 用户登录
server.post('/auth/login', (req, res) => {
  const { username, password } = req.body;

  // 查找用户
  const user = db.get('users').find({ username }).value();

  if (!user) {
    return res.status(400).json({
      success: false,
      message: '用户名或密码错误'
    });
  }

  // 验证密码（检查哈希值）
  console.log(`用户 ${username} 尝试登录，密码哈希: ${password}`);

  // 从数据库获取用户的密码哈希
  const storedPasswordHash = user.passwordHash;
  console.log(`数据库中的密码哈希: ${storedPasswordHash}`);

  // 如果数据库中没有密码哈希，使用默认密码进行兼容
  if (!storedPasswordHash) {
    const validPasswordHashes = [
      hashPassword('test123'),
      hashPassword('password'),
      hashPassword('123456')
    ];

    if (!validPasswordHashes.includes(password)) {
      console.log(`密码验证失败（使用默认密码）`);
      return res.status(400).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
  } else {
    // 使用数据库中存储的密码哈希验证
    if (password !== storedPasswordHash) {
      console.log(`密码验证失败（数据库密码不匹配）`);
      return res.status(400).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
  }

  console.log(`用户 ${username} 登录成功`);

  // 生成Token
  const token = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  res.json({
    success: true,
    data: {
      token,
      refreshToken,
      user,
      expiresIn: 86400
    }
  });
});

// 获取会员套餐
server.get('/packages', (req, res) => {
  const packages = db.get('packages').filter({ isActive: true }).value();
  res.json({
    success: true,
    data: packages
  });
});

// 验证会员码
server.post('/member-code/validate', (req, res) => {
  const { code } = req.body;

  const memberCode = db.get('memberCodes').find({ code, isUsed: false }).value();

  if (memberCode && (!memberCode.expireAt || new Date(memberCode.expireAt) > new Date())) {
    res.json({
      success: true,
      message: '会员码有效'
    });
  } else {
    res.status(400).json({
      success: false,
      message: '会员码无效或已过期'
    });
  }
});

// 生成会员码工具函数
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 创建单个会员码
server.post('/admin/member-code/create', (req, res) => {
  const { packageId, expireAt, batchId, customCode } = req.body;

  // 验证套餐是否存在
  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }

  // 生成或使用自定义会员码
  const code = customCode || generateMemberCode();

  // 检查会员码是否已存在
  const existingCode = db.get('memberCodes').find({ code }).value();
  if (existingCode) {
    return res.status(400).json({
      success: false,
      message: '会员码已存在'
    });
  }

  const newMemberCode = {
    code,
    packageId,
    isUsed: false,
    usedBy: null,
    usedAt: null,
    expireAt: expireAt || null,
    batchId: batchId || `batch_${Date.now()}`,
    createdAt: new Date().toISOString()
  };

  db.get('memberCodes').push(newMemberCode).write();

  res.json({
    success: true,
    data: newMemberCode,
    message: '会员码创建成功'
  });
});

// 批量创建会员码
server.post('/admin/member-code/batch-create', (req, res) => {
  const { packageId, count, expireAt, batchId, prefix } = req.body;

  // 验证套餐是否存在
  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }

  const createdCodes = [];
  const currentBatchId = batchId || `batch_${Date.now()}`;

  for (let i = 0; i < count; i++) {
    let code;
    let attempts = 0;

    // 生成唯一的会员码
    do {
      code = generateMemberCode(prefix || 'VIP');
      attempts++;
      if (attempts > 100) {
        return res.status(500).json({
          success: false,
          message: '生成唯一会员码失败，请重试'
        });
      }
    } while (db.get('memberCodes').find({ code }).value());

    const newMemberCode = {
      code,
      packageId,
      isUsed: false,
      usedBy: null,
      usedAt: null,
      expireAt: expireAt || null,
      batchId: currentBatchId,
      createdAt: new Date().toISOString()
    };

    db.get('memberCodes').push(newMemberCode).write();
    createdCodes.push(newMemberCode);
  }

  res.json({
    success: true,
    data: {
      batchId: currentBatchId,
      count: createdCodes.length,
      codes: createdCodes
    },
    message: `成功创建 ${createdCodes.length} 个会员码`
  });
});

// 获取会员码列表
server.get('/admin/member-code/list', (req, res) => {
  const { page = 1, limit = 20, isUsed, packageId, batchId } = req.query;

  let memberCodes = db.get('memberCodes').value();

  // 过滤条件
  if (isUsed !== undefined) {
    memberCodes = memberCodes.filter(code => code.isUsed === (isUsed === 'true'));
  }

  if (packageId) {
    memberCodes = memberCodes.filter(code => code.packageId === packageId);
  }

  if (batchId) {
    memberCodes = memberCodes.filter(code => code.batchId === batchId);
  }

  // 分页
  const total = memberCodes.length;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedCodes = memberCodes.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      codes: paginatedCodes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  });
});

// 删除会员码
server.delete('/admin/member-code/:code', (req, res) => {
  const { code } = req.params;

  const memberCode = db.get('memberCodes').find({ code }).value();
  if (!memberCode) {
    return res.status(404).json({
      success: false,
      message: '会员码不存在'
    });
  }

  if (memberCode.isUsed) {
    return res.status(400).json({
      success: false,
      message: '已使用的会员码不能删除'
    });
  }

  db.get('memberCodes').remove({ code }).write();

  res.json({
    success: true,
    message: '会员码删除成功'
  });
});

// 获取会员码统计信息
server.get('/admin/member-code/stats', (req, res) => {
  const memberCodes = db.get('memberCodes').value();

  const stats = {
    total: memberCodes.length,
    used: memberCodes.filter(code => code.isUsed).length,
    unused: memberCodes.filter(code => !code.isUsed).length,
    expired: memberCodes.filter(code =>
      code.expireAt && new Date(code.expireAt) < new Date()
    ).length,
    byPackage: {},
    byBatch: {}
  };

  // 按套餐统计
  memberCodes.forEach(code => {
    if (!stats.byPackage[code.packageId]) {
      stats.byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
    }
    stats.byPackage[code.packageId].total++;
    if (code.isUsed) {
      stats.byPackage[code.packageId].used++;
    } else {
      stats.byPackage[code.packageId].unused++;
    }
  });

  // 按批次统计
  memberCodes.forEach(code => {
    if (!stats.byBatch[code.batchId]) {
      stats.byBatch[code.batchId] = { total: 0, used: 0, unused: 0 };
    }
    stats.byBatch[code.batchId].total++;
    if (code.isUsed) {
      stats.byBatch[code.batchId].used++;
    } else {
      stats.byBatch[code.batchId].unused++;
    }
  });

  res.json({
    success: true,
    data: stats
  });
});

// 创建订单
server.post('/orders/create', verifyToken, (req, res) => {
  const { packageId } = req.body;
  const userId = req.userId;

  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }

  const orderId = uuidv4();
  const newOrder = {
    id: orderId,
    userId,
    packageId,
    packageName: package.name,
    amount: package.price,
    status: 'pending',
    paymentMethod: null,
    transactionId: null,
    memberCode: null,
    expireAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30分钟后过期
    paidAt: null,
    metadata: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  db.get('orders').push(newOrder).write();

  res.json({
    success: true,
    data: newOrder
  });
});

// 获取用户订单
server.get('/orders/my', verifyToken, (req, res) => {
  const userId = req.userId;

  try {
    console.log(`用户 ${userId} 请求获取订单列表`);

    // 查找用户的所有订单
    const userOrders = db.get('orders').filter({ userId }).value();

    // 按创建时间倒序排列
    userOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    console.log(`✅ 找到用户 ${userId} 的 ${userOrders.length} 个订单`);

    res.json({
      success: true,
      data: userOrders
    });

  } catch (error) {
    console.error(`❌ 获取用户 ${userId} 订单失败:`, error);
    res.status(500).json({
      success: false,
      message: '获取订单失败: ' + error.message
    });
  }
});

// 会员码支付
server.post('/payment/member-code', verifyToken, (req, res) => {
  const { orderId, memberCode } = req.body;
  const userId = req.userId;

  const order = db.get('orders').find({ id: orderId, userId }).value();
  if (!order) {
    return res.status(400).json({
      success: false,
      message: '订单不存在'
    });
  }

  const code = db.get('memberCodes').find({ code: memberCode, isUsed: false }).value();
  if (!code) {
    return res.status(400).json({
      success: false,
      message: '会员码无效'
    });
  }

  // 更新订单状态
  db.get('orders')
    .find({ id: orderId })
    .assign({
      status: 'paid',
      paymentMethod: 'member_code',
      memberCode,
      paidAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    .write();

  // 标记会员码为已使用
  db.get('memberCodes')
    .find({ code: memberCode })
    .assign({
      isUsed: true,
      usedBy: userId,
      usedAt: new Date().toISOString()
    })
    .write();

  // 更新用户会员状态
  const package = db.get('packages').find({ id: order.packageId }).value();
  const membershipData = {
    isMember: true,
    isPermanent: package.durationDays === -1,
    membershipType: package.durationDays === -1 ? 'permanent' : 'monthly',
    expireTime: package.durationDays === -1 ? null : new Date(Date.now() + package.durationDays * 24 * 60 * 60 * 1000).toISOString()
  };

  db.get('users')
    .find({ id: userId })
    .assign({
      isMember: true,
      isPermanentMember: membershipData.isPermanent,
      membershipType: membershipData.membershipType,
      memberExpireTime: membershipData.expireTime,
      updatedAt: new Date().toISOString()
    })
    .write();

  res.json({
    success: true,
    data: membershipData
  });
});

// 数据同步上传 - 支持分块上传
server.post('/sync/upload', verifyToken, (req, res) => {
  const { data, timestamp, chunkInfo } = req.body;
  const userId = req.userId;

  try {
    // 计算数据大小
    const dataString = JSON.stringify(data);
    const dataSizeKB = (dataString.length / 1024).toFixed(2);

    if (chunkInfo) {
      console.log(`用户 ${userId} 上传数据块 ${chunkInfo.index}/${chunkInfo.total}:`, chunkInfo.type);
      console.log(`块大小: ${dataSizeKB} KB`);
    } else {
      console.log(`用户 ${userId} 上传同步数据:`, Object.keys(data));
      console.log(`数据大小: ${dataSizeKB} KB`);
    }

    // 获取或创建用户同步数据
    let syncData = db.get('syncData').find({ userId }).value();

    if (!syncData) {
      // 创建新的同步数据记录
      syncData = {
        id: uuidv4(),
        userId,
        data: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      db.get('syncData').push(syncData).write();
    }

    // 处理分块上传
    if (chunkInfo) {
      // 合并数据块到现有数据中
      const currentData = syncData.data || {};

      // 根据数据类型合并
      if (chunkInfo.type === 'userSettings') {
        Object.assign(currentData, data);
      } else if (chunkInfo.type === 'knowledgeDocuments') {
        currentData.knowledgeDocuments = data.knowledgeDocuments;
      } else if (chunkInfo.type.startsWith('novels_batch_')) {
        // 合并小说批次
        if (!currentData.novels) {
          currentData.novels = [];
        }
        currentData.novels = currentData.novels.concat(data.novels);
      } else if (chunkInfo.type === 'characterCards') {
        currentData.characterCards = data.characterCards;
      } else if (chunkInfo.type === 'characterTypes') {
        currentData.characterTypes = data.characterTypes;
      }

      // 更新数据
      db.get('syncData')
        .find({ userId })
        .assign({
          data: currentData,
          timestamp: timestamp || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastChunk: chunkInfo
        })
        .write();

      console.log(`✅ 用户 ${userId} 数据块 ${chunkInfo.index}/${chunkInfo.total} 同步成功`);

    } else {
      // 传统的完整数据上传
      db.get('syncData')
        .find({ userId })
        .assign({
          data: data,
          timestamp: timestamp || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          dataSize: dataSizeKB + ' KB'
        })
        .write();

      console.log(`✅ 用户 ${userId} 数据同步成功，大小: ${dataSizeKB} KB`);
    }

    res.json({
      success: true,
      message: chunkInfo ? `数据块 ${chunkInfo.index}/${chunkInfo.total} 同步成功` : '数据同步成功',
      dataSize: dataSizeKB + ' KB',
      timestamp: timestamp || new Date().toISOString(),
      chunkInfo: chunkInfo
    });

  } catch (error) {
    console.error(`❌ 用户 ${userId} 数据同步失败:`, error);
    res.status(500).json({
      success: false,
      message: '数据同步失败: ' + error.message
    });
  }
});

// 数据同步下载
server.get('/sync/download', verifyToken, (req, res) => {
  const userId = req.userId;

  try {
    console.log(`用户 ${userId} 请求下载同步数据`);

    // 查找用户的同步数据
    const userSyncData = db.get('syncData').find({ userId }).value();

    if (userSyncData && userSyncData.data) {
      console.log(`✅ 找到用户 ${userId} 的同步数据，大小: ${userSyncData.dataSize || '未知'}`);

      res.json({
        success: true,
        data: userSyncData.data,
        timestamp: userSyncData.timestamp,
        dataSize: userSyncData.dataSize,
        lastUpdated: userSyncData.updatedAt
      });
    } else {
      console.log(`ℹ️ 用户 ${userId} 暂无同步数据`);

      res.json({
        success: true,
        data: null,
        message: '暂无同步数据'
      });
    }

  } catch (error) {
    console.error(`❌ 用户 ${userId} 数据下载失败:`, error);
    res.status(500).json({
      success: false,
      message: '数据下载失败: ' + error.message
    });
  }
});

// 健康检查API
server.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'CloudBase Novel App API',
    version: '1.0.0'
  });
});

// 上传头像 (UserController调用的路径)
server.post('/user/avatar', verifyToken, upload.single('avatar'), (req, res) => {
  const userId = req.userId;
  const file = req.file;

  // 模拟头像上传 (支持FormData)
  console.log(`用户 ${userId} 上传头像，文件大小: ${file ? file.size : 0} 字节`);

  if (!file) {
    return res.status(400).json({
      success: false,
      message: '未找到上传的文件'
    });
  }

  // 生成模拟头像URL - 提供多种选择
  const user = db.get('users').find({ id: userId }).value();
  const username = user ? user.username : 'User';
  const userInitials = username.slice(0, 2).toUpperCase();

  // 随机选择头像样式
  const avatarStyles = [
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=4CAF50&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=2196F3&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=FF9800&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=9C27B0&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=F44336&color=fff&bold=true`,
    `https://robohash.org/${userId}?size=150x150&set=set4`,
    `https://avatars.dicebear.com/api/avataaars/${userId}.svg?width=150&height=150`,
    `https://avatars.dicebear.com/api/bottts/${userId}.svg?width=150&height=150`
  ];

  const randomIndex = Math.floor(Math.random() * avatarStyles.length);
  const avatarUrl = avatarStyles[randomIndex];

  console.log(`为用户 ${username}(${userId}) 生成头像: ${avatarUrl}`);

  // 更新用户头像
  db.get('users')
    .find({ id: userId })
    .assign({
      avatar: avatarUrl,
      updatedAt: new Date().toISOString()
    })
    .write();

  // 获取更新后的用户信息
  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    message: '头像上传成功',
    data: {
      avatarUrl: avatarUrl,
      user: updatedUser
    }
  });
});

// 更新用户密码
server.put('/user/password', verifyToken, (req, res) => {
  const userId = req.userId;
  const { oldPassword, newPassword } = req.body;

  console.log(`用户 ${userId} 尝试更新密码，旧密码: ${oldPassword}`);

  if (!oldPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: '请提供当前密码和新密码'
    });
  }

  // 获取用户信息
  const user = db.get('users').find({ id: userId }).value();
  if (!user) {
    return res.status(404).json({
      success: false,
      message: '用户不存在'
    });
  }

  // 验证当前密码（检查哈希值）
  console.log(`接收到的旧密码哈希: ${oldPassword}`);

  // 从数据库获取用户当前的密码哈希
  const storedPasswordHash = user.passwordHash;
  console.log(`数据库中的密码哈希: ${storedPasswordHash}`);

  // 验证当前密码
  let isCurrentPasswordValid = false;

  if (storedPasswordHash) {
    // 使用数据库中存储的密码哈希验证
    isCurrentPasswordValid = (oldPassword === storedPasswordHash);
  } else {
    // 如果数据库中没有密码哈希，使用默认密码进行兼容
    const validPasswordHashes = [
      hashPassword('test123'),
      hashPassword('password'),
      hashPassword('123456')
    ];
    isCurrentPasswordValid = validPasswordHashes.includes(oldPassword);
  }

  if (!isCurrentPasswordValid) {
    console.log(`用户 ${userId} 旧密码验证失败`);
    return res.status(400).json({
      success: false,
      message: '当前密码错误'
    });
  }

  console.log(`用户 ${userId} 密码验证成功，更新密码`);
  console.log(`新密码哈希: ${newPassword}`);

  // 更新密码哈希到数据库
  db.get('users')
    .find({ id: userId })
    .assign({
      passwordHash: newPassword, // 存储新密码的哈希值
      lastPasswordChange: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    .write();

  console.log(`用户 ${userId} 密码已成功更新到数据库`);

  res.json({
    success: true,
    message: '密码更新成功'
  });
});

// 更新用户设置
server.put('/user/settings', verifyToken, (req, res) => {
  const userId = req.userId;
  const settings = req.body;

  console.log(`用户 ${userId} 更新设置:`, settings);

  // 更新用户设置
  const user = db.get('users').find({ id: userId }).value();
  if (!user) {
    return res.status(404).json({
      success: false,
      message: '用户不存在'
    });
  }

  db.get('users')
    .find({ id: userId })
    .assign({
      settings: { ...user.settings, ...settings },
      updatedAt: new Date().toISOString()
    })
    .write();

  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    data: updatedUser
  });
});

// 更新用户信息
server.put('/user/profile', verifyToken, (req, res) => {
  const userId = req.userId;
  const { username, email } = req.body;

  // 检查用户名是否已被其他用户使用
  if (username) {
    const existingUser = db.get('users').find({ username, id: { $ne: userId } }).value();
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已被使用'
      });
    }
  }

  // 更新用户信息
  const updateData = {
    updatedAt: new Date().toISOString()
  };

  if (username) updateData.username = username;
  if (email) updateData.email = email;

  db.get('users')
    .find({ id: userId })
    .assign(updateData)
    .write();

  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    data: updatedUser
  });
});

// 使用默认路由
server.use(router);

// 导出服务器实例
module.exports = server;
