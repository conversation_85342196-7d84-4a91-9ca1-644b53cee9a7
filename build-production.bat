@echo off
setlocal enabledelayedexpansion

REM 构建生产环境Flutter应用
REM 连接到腾讯云CloudBase: novel-app-2gywkgnn15cbd6a8

echo ========================================
echo 构建生产环境Flutter应用
echo 目标环境: 腾讯云CloudBase
echo 环境ID: novel-app-2gywkgnn15cbd6a8
echo ========================================
echo.

REM 1. 检查Flutter环境
echo [STEP 1] 检查Flutter环境...

where flutter >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Flutter 未安装或未添加到PATH
    echo 请先安装Flutter并添加到系统PATH
    pause
    exit /b 1
)

echo [INFO] Flutter环境检查完成

REM 2. 清理之前的构建
echo [STEP 2] 清理之前的构建...

if exist "build" (
    echo 正在清理build目录...
    rmdir /s /q build
)

flutter clean
if %errorlevel% neq 0 (
    echo [WARNING] Flutter clean 执行失败，继续构建...
)

echo [INFO] 清理完成

REM 3. 获取依赖
echo [STEP 3] 获取Flutter依赖...

flutter pub get
if %errorlevel% neq 0 (
    echo [ERROR] 获取依赖失败
    pause
    exit /b 1
)

echo [INFO] 依赖获取完成

REM 4. 构建生产版本
echo [STEP 4] 构建生产版本...

echo 正在构建Windows生产版本...
echo 使用生产环境API: https://api.dznovel.top

flutter build windows --dart-define=PRODUCTION=true --release
if %errorlevel% neq 0 (
    echo [ERROR] Windows版本构建失败
    pause
    exit /b 1
)

echo [SUCCESS] Windows版本构建完成！

REM 5. 创建发布包
echo [STEP 5] 创建发布包...

set RELEASE_DIR=release_production_%date:~0,4%%date:~5,2%%date:~8,2%
if exist "%RELEASE_DIR%" (
    rmdir /s /q "%RELEASE_DIR%"
)

mkdir "%RELEASE_DIR%"

REM 复制构建文件
echo 正在复制构建文件...
xcopy "build\windows\runner\Release\*" "%RELEASE_DIR%\" /E /I /H /Y >nul

REM 创建启动脚本
echo @echo off > "%RELEASE_DIR%\启动小说应用.bat"
echo echo 启动岱宗文脉小说应用... >> "%RELEASE_DIR%\启动小说应用.bat"
echo start novel_app.exe >> "%RELEASE_DIR%\启动小说应用.bat"

REM 创建说明文件
echo 岱宗文脉小说应用 - 生产版本 > "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 版本信息: >> "%RELEASE_DIR%\README.txt"
echo 构建时间: %date% %time% >> "%RELEASE_DIR%\README.txt"
echo 环境: 腾讯云CloudBase生产环境 >> "%RELEASE_DIR%\README.txt"
echo 环境ID: novel-app-2gywkgnn15cbd6a8 >> "%RELEASE_DIR%\README.txt"
echo. >> "%RELEASE_DIR%\README.txt"
echo 使用说明: >> "%RELEASE_DIR%\README.txt"
echo 1. 双击"启动小说应用.bat"启动应用 >> "%RELEASE_DIR%\README.txt"
echo 2. 或直接运行novel_app.exe >> "%RELEASE_DIR%\README.txt"
echo 3. 首次使用需要注册账号 >> "%RELEASE_DIR%\README.txt"
echo 4. 支持会员码激活会员功能 >> "%RELEASE_DIR%\README.txt"

echo [INFO] 发布包创建完成: %RELEASE_DIR%

REM 6. 显示构建结果
echo.
echo ==========================================
echo 生产环境构建完成！
echo.
echo 构建信息:
echo 目标平台: Windows
echo 构建模式: Release (生产环境)
echo API地址: https://api.dznovel.top
echo 发布包: %RELEASE_DIR%
echo.
echo 文件说明:
echo - novel_app.exe: 主程序
echo - data\: 程序数据文件
echo - 启动小说应用.bat: 快速启动脚本
echo - README.txt: 使用说明
echo.
echo 下一步操作:
echo 1. 测试发布包中的应用是否正常运行
echo 2. 验证与CloudBase服务器的连接
echo 3. 测试用户注册、登录、同步等功能
echo 4. 打包分发给用户使用
echo ==========================================
echo.

echo [INFO] 构建脚本执行完成！
echo 发布包位置: %cd%\%RELEASE_DIR%

pause
