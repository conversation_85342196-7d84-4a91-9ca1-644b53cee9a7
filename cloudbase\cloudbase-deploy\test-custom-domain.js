const https = require('https');

// 测试自定义域名的路由结构 - 根据CloudBase控制台配置
const testUrls = [
  'https://api.dznovel.top',           // 静态托管根路径
  'https://api.dznovel.top/api',       // 云函数根路径
  'https://api.dznovel.top/api/health', // 健康检查
  'https://api.dznovel.top/api/auth/login', // 登录接口
  'https://api.dznovel.top/api/packages',   // 套餐接口
  'https://api.dznovel.top/api/member-code/validate' // 会员码验证
];

console.log('🚀 测试自定义域名路由结构...\n');

async function testUrl(url) {
  return new Promise((resolve) => {
    const req = https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          url,
          status: res.statusCode,
          data: data.substring(0, 300) // 显示前300个字符
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        url,
        status: 'ERROR',
        data: error.message
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        url,
        status: 'TIMEOUT',
        data: '请求超时'
      });
    });
  });
}

async function runTests() {
  console.log('📋 测试不同的路由路径以确定正确的API结构：\n');
  
  for (const url of testUrls) {
    console.log(`🔍 测试: ${url}`);
    const result = await testUrl(url);
    
    if (result.status === 200) {
      console.log(`✅ 成功 - 状态码: ${result.status}`);
      console.log(`📄 响应: ${result.data}`);
    } else if (result.status === 404) {
      console.log(`❌ 404 - 路径不存在`);
    } else {
      console.log(`❌ 失败 - 状态: ${result.status}`);
      console.log(`📄 错误: ${result.data}`);
    }
    console.log('');
  }

  console.log('🏁 测试完成！');
  console.log('\n💡 分析结果：');
  console.log('- 如果根路径(/)返回200，说明域名配置正确');
  console.log('- 如果/api/health返回200，说明需要使用/api前缀');
  console.log('- 如果/health返回200，说明不需要/api前缀');
  console.log('- 根据测试结果调整Flutter应用的API配置');
}

runTests().catch(console.error);
