import{_ as Zo}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                        *//* empty css                   *//* empty css                        *//* empty css               */import{c as ft,e as M,V as vn,m as Y,a as W,b as Xt,i as st,r as bt,s as $o,d as Z,f as Tt,g as cn,h as qo,D as Ko,j as Ta,S as jo,k as Jo,l as dr,n as X,o as nt,p as or,M as Rt,q as Qo,t as q,u as ts,v as ie,w as oa,x as es,C as rs,y as Ia,z as lt,A as as,B as is,E as Nt,F as Q,G as ns,H as os,I as ss,J as ls,K as at,L as sa,N as fn,O as us,_ as G,P as hs,Q as dn,R as it,T as gn,U as Ma,W as vs,X as De,Y as cs,Z as pt,$ as pn,a0 as mn,a1 as je,a2 as Pa,a3 as Br,a4 as fs,a5 as ds,a6 as ne,a7 as yn,a8 as _n,a9 as xn,aa as bn,ab as Sn,ac as wn,ad as ka,ae as gs,af as ps,ag as ms,ah as ys,ai as _s,aj as xs,ak as bs,al as Ss,am as ws,an as Gr,ao as zr,ap as Bt,aq as Ft,ar as qt,as as oe,at as Wt,au as Lt,av as As,aw as Cs,ax as Ds,ay as Ls,az as Ts,aA as Is,aB as Ms,aC as Ps,aD as ks,aE as Os,aF as Es,aG as Rs,aH as Ns,aI as Bs,aJ as Gs,aK as An,aL as zs,aM as Vr,aN as rt,aO as Oa,aP as Cn,aQ as tr,aR as Ea,aS as gr,aT as Fr,aU as Dn,aV as Hr,aW as Vs,aX as Ln,aY as Fs,aZ as Hs,a_ as Ws,a$ as Us,b0 as Xs,b1 as Ge,b2 as Ys,b3 as Zs,b4 as ut,b5 as ct,b6 as $s,b7 as ze,b8 as yt,b9 as Ie,ba as Ra,bb as qs,bc as Ks,bd as ht,be as la,bf as mt,bg as Ee,bh as Tn,bi as js,bj as Re,bk as Me,bl as j,bm as Na,bn as Js,bo as In,bp as Qs,bq as Ba,br as Ga,bs as sr,bt as St,bu as Ne,bv as se,bw as er,bx as Wr,by as za,bz as _t,bA as tl,bB as Mn,bC as el,bD as rl,bE as Pn,bF as Va,bG as Ur,bH as al,bI as il,bJ as nl,bK as le,bL as ol,bM as sl,bN as ll,bO as ul,bP as hl,bQ as vl,bR as cl,bS as kn,bT as On,bU as fl,bV as ue,bW as Pe,bX as En,bY as he,bZ as ua,b_ as rr,b$ as ar,c0 as dl,c1 as Rn,c2 as Fa,c3 as ir,c4 as gl,c5 as pl,c6 as Nn,c7 as Bn,c8 as Xr,c9 as ml,ca as yl,cb as _l,cc as xl,cd as ha,ce as Gn,cf as Ha,cg as bl,ch as Sl,ci as zn,cj as wl,ck as Al,cl as Cl,cm as Wa,cn as Dl,co as pr,cp as Ua,cq as Xa,cr as Ll,cs as Tl,ct as Il,cu as Ml,cv as Ya,cw as Za,cx as Pl,cy as kl,cz as Ol,cA as $a,cB as Le,cC as El,cD as Rl,cE as qa}from"./index.esm.min-Cv8CkJd6.js";import{d as Nl,r as ee,y as Ka,o as Bl,c as mr,b as H,e as V,w as U,W as Gl,q as zl,i as It,F as ja,G as Ja,X as Vl,Y as Fl,h as dt,j as Qa,Z as Hl,_ as Wl,$ as Ul,t as At,E as Xl,B as Yl,a0 as Zl,H as me,a1 as $l,K as yr,M as ti}from"./index-CAzH2L69.js";function ye(a){return a==null?0:a.length||1}function ei(a){return a}var ql=function(){function a(e,t,r,i,n,o){this._old=e,this._new=t,this._oldKeyGetter=r||ei,this._newKeyGetter=i||ei,this.context=n,this._diffModeMultiple=o==="multiple"}return a.prototype.add=function(e){return this._add=e,this},a.prototype.update=function(e){return this._update=e,this},a.prototype.updateManyToOne=function(e){return this._updateManyToOne=e,this},a.prototype.updateOneToMany=function(e){return this._updateOneToMany=e,this},a.prototype.updateManyToMany=function(e){return this._updateManyToMany=e,this},a.prototype.remove=function(e){return this._remove=e,this},a.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},a.prototype._executeOneToOne=function(){var e=this._old,t=this._new,r={},i=new Array(e.length),n=new Array(t.length);this._initIndexMap(e,null,i,"_oldKeyGetter"),this._initIndexMap(t,r,n,"_newKeyGetter");for(var o=0;o<e.length;o++){var s=i[o],l=r[s],u=ye(l);if(u>1){var h=l.shift();l.length===1&&(r[s]=l[0]),this._update&&this._update(h,o)}else u===1?(r[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(n,r)},a.prototype._executeMultiple=function(){var e=this._old,t=this._new,r={},i={},n=[],o=[];this._initIndexMap(e,r,n,"_oldKeyGetter"),this._initIndexMap(t,i,o,"_newKeyGetter");for(var s=0;s<n.length;s++){var l=n[s],u=r[l],h=i[l],v=ye(u),f=ye(h);if(v>1&&f===1)this._updateManyToOne&&this._updateManyToOne(h,u),i[l]=null;else if(v===1&&f>1)this._updateOneToMany&&this._updateOneToMany(h,u),i[l]=null;else if(v===1&&f===1)this._update&&this._update(h,u),i[l]=null;else if(v>1&&f>1)this._updateManyToMany&&this._updateManyToMany(h,u),i[l]=null;else if(v>1)for(var c=0;c<v;c++)this._remove&&this._remove(u[c]);else this._remove&&this._remove(u)}this._performRestAdd(o,i)},a.prototype._performRestAdd=function(e,t){for(var r=0;r<e.length;r++){var i=e[r],n=t[i],o=ye(n);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(n[s]);else o===1&&this._add&&this._add(n);t[i]=null}},a.prototype._initIndexMap=function(e,t,r,i){for(var n=this._diffModeMultiple,o=0;o<e.length;o++){var s="_ec_"+this[i](e[o],o);if(n||(r[o]=s),!!t){var l=t[s],u=ye(l);u===0?(t[s]=o,n&&r.push(s)):u===1?t[s]=[l,o]:l.push(o)}}},a}(),Kl=function(){function a(e,t){this._encode=e,this._schema=t}return a.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},a.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},a}();function jl(a,e){var t={},r=t.encode={},i=ft(),n=[],o=[],s={};M(a.dimensions,function(f){var c=a.getDimensionInfo(f),d=c.coordDim;if(d){var g=c.coordDimIndex;_r(r,d)[g]=f,c.isExtraCoord||(i.set(d,1),Ql(c.type)&&(n[0]=f),_r(s,d)[g]=a.getDimensionIndex(c.name)),c.defaultTooltip&&o.push(f)}vn.each(function(m,p){var y=_r(r,p),_=c.otherDims[p];_!=null&&_!==!1&&(y[_]=c.name)})});var l=[],u={};i.each(function(f,c){var d=r[c];u[c]=d[0],l=l.concat(d)}),t.dataDimsOnCoord=l,t.dataDimIndicesOnCoord=Y(l,function(f){return a.getDimensionInfo(f).storeDimIndex}),t.encodeFirstDimNotExtra=u;var h=r.label;h&&h.length&&(n=h.slice());var v=r.tooltip;return v&&v.length?o=v.slice():o.length||(o=n.slice()),r.defaultedLabel=n,r.defaultedTooltip=o,t.userOutput=new Kl(s,e),t}function _r(a,e){return a.hasOwnProperty(e)||(a[e]=[]),a[e]}function Jl(a){return a==="category"?"ordinal":a==="time"?"time":"float"}function Ql(a){return!(a==="ordinal"||a==="time")}var Je=function(){function a(e){this.otherDims={},e!=null&&W(this,e)}return a}(),tu=Xt(),eu={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Vn=function(){function a(e){this.dimensions=e.dimensions,this._dimOmitted=e.dimensionOmitted,this.source=e.source,this._fullDimCount=e.fullDimensionCount,this._updateDimOmitted(e.dimensionOmitted)}return a.prototype.isDimensionOmitted=function(){return this._dimOmitted},a.prototype._updateDimOmitted=function(e){this._dimOmitted=e,e&&(this._dimNameMap||(this._dimNameMap=Wn(this.source)))},a.prototype.getSourceDimensionIndex=function(e){return bt(this._dimNameMap.get(e),-1)},a.prototype.getSourceDimension=function(e){var t=this.source.dimensionsDefine;if(t)return t[e]},a.prototype.makeStoreSchema=function(){for(var e=this._fullDimCount,t=$o(this.source),r=!Un(e),i="",n=[],o=0,s=0;o<e;o++){var l=void 0,u=void 0,h=void 0,v=this.dimensions[s];if(v&&v.storeDimIndex===o)l=t?v.name:null,u=v.type,h=v.ordinalMeta,s++;else{var f=this.getSourceDimension(o);f&&(l=t?f.name:null,u=f.type)}n.push({property:l,type:u,ordinalMeta:h}),t&&l!=null&&(!v||!v.isCalculationCoord)&&(i+=r?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i+="$",i+=eu[u]||"f",h&&(i+=h.uid),i+="$"}var c=this.source,d=[c.seriesLayoutBy,c.startIndex,i].join("$$");return{dimensions:n,hash:d}},a.prototype.makeOutputDimensionNames=function(){for(var e=[],t=0,r=0;t<this._fullDimCount;t++){var i=void 0,n=this.dimensions[r];if(n&&n.storeDimIndex===t)n.isCalculationCoord||(i=n.name),r++;else{var o=this.getSourceDimension(t);o&&(i=o.name)}e.push(i)}return e},a.prototype.appendCalculationDimension=function(e){this.dimensions.push(e),e.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},a}();function Fn(a){return a instanceof Vn}function Hn(a){for(var e=ft(),t=0;t<(a||[]).length;t++){var r=a[t],i=st(r)?r.name:r;i!=null&&e.get(i)==null&&e.set(i,t)}return e}function Wn(a){var e=tu(a);return e.dimNameMap||(e.dimNameMap=Hn(a.dimensionsDefine))}function Un(a){return a>30}var _e=st,Mt=Y,ru=typeof Int32Array>"u"?Array:Int32Array,au="e\0\0",ri=-1,iu=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],nu=["_approximateExtent"],ai,Ve,xe,be,xr,Se,br,Xn=function(){function a(e,t){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var r,i=!1;Fn(e)?(r=e.dimensions,this._dimOmitted=e.isDimensionOmitted(),this._schema=e):(i=!0,r=e),r=r||["x","y"];for(var n={},o=[],s={},l=!1,u={},h=0;h<r.length;h++){var v=r[h],f=Z(v)?new Je({name:v}):v instanceof Je?v:new Je(v),c=f.name;f.type=f.type||"float",f.coordDim||(f.coordDim=c,f.coordDimIndex=0);var d=f.otherDims=f.otherDims||{};o.push(c),n[c]=f,u[c]!=null&&(l=!0),f.createInvertedIndices&&(s[c]=[]),d.itemName===0&&(this._nameDimIdx=h),d.itemId===0&&(this._idDimIdx=h),i&&(f.storeDimIndex=h)}if(this.dimensions=o,this._dimInfos=n,this._initGetDimensionInfo(l),this.hostModel=t,this._invertedIndicesMap=s,this._dimOmitted){var g=this._dimIdxToName=ft();M(o,function(m){g.set(n[m].storeDimIndex,m)})}}return a.prototype.getDimension=function(e){var t=this._recognizeDimIndex(e);if(t==null)return e;if(t=e,!this._dimOmitted)return this.dimensions[t];var r=this._dimIdxToName.get(t);if(r!=null)return r;var i=this._schema.getSourceDimension(t);if(i)return i.name},a.prototype.getDimensionIndex=function(e){var t=this._recognizeDimIndex(e);if(t!=null)return t;if(e==null)return-1;var r=this._getDimInfo(e);return r?r.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(e):-1},a.prototype._recognizeDimIndex=function(e){if(Tt(e)||e!=null&&!isNaN(e)&&!this._getDimInfo(e)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(e)<0))return+e},a.prototype._getStoreDimIndex=function(e){var t=this.getDimensionIndex(e);return t},a.prototype.getDimensionInfo=function(e){return this._getDimInfo(this.getDimension(e))},a.prototype._initGetDimensionInfo=function(e){var t=this._dimInfos;this._getDimInfo=e?function(r){return t.hasOwnProperty(r)?t[r]:void 0}:function(r){return t[r]}},a.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},a.prototype.mapDimension=function(e,t){var r=this._dimSummary;if(t==null)return r.encodeFirstDimNotExtra[e];var i=r.encode[e];return i?i[t]:null},a.prototype.mapDimensionsAll=function(e){var t=this._dimSummary,r=t.encode[e];return(r||[]).slice()},a.prototype.getStore=function(){return this._store},a.prototype.initData=function(e,t,r){var i=this,n;if(e instanceof Ta&&(n=e),!n){var o=this.dimensions,s=cn(e)||qo(e)?new Ko(e,o.length):e;n=new Ta;var l=Mt(o,function(u){return{type:i._dimInfos[u].type,property:u}});n.initData(s,l,r)}this._store=n,this._nameList=(t||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,n.count()),this._dimSummary=jl(this,this._schema),this.userOutput=this._dimSummary.userOutput},a.prototype.appendData=function(e){var t=this._store.appendData(e);this._doInit(t[0],t[1])},a.prototype.appendValues=function(e,t){var r=this._store.appendValues(e,t&&t.length),i=r.start,n=r.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),t)for(var s=i;s<n;s++){var l=s-i;this._nameList[s]=t[l],o&&br(this,s)}},a.prototype._updateOrdinalMeta=function(){for(var e=this._store,t=this.dimensions,r=0;r<t.length;r++){var i=this._dimInfos[t[r]];i.ordinalMeta&&e.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},a.prototype._shouldMakeIdFromName=function(){var e=this._store.getProvider();return this._idDimIdx==null&&e.getSource().sourceFormat!==jo&&!e.fillStorage},a.prototype._doInit=function(e,t){if(!(e>=t)){var r=this._store,i=r.getProvider();this._updateOrdinalMeta();var n=this._nameList,o=this._idList,s=i.getSource().sourceFormat,l=s===oa;if(l&&!i.pure)for(var u=[],h=e;h<t;h++){var v=i.getItem(h,u);if(!this.hasItemOption&&Jo(v)&&(this.hasItemOption=!0),v){var f=v.name;n[h]==null&&f!=null&&(n[h]=dr(f,null));var c=v.id;o[h]==null&&c!=null&&(o[h]=dr(c,null))}}if(this._shouldMakeIdFromName())for(var h=e;h<t;h++)br(this,h);ai(this)}},a.prototype.getApproximateExtent=function(e){return this._approximateExtent[e]||this._store.getDataExtent(this._getStoreDimIndex(e))},a.prototype.setApproximateExtent=function(e,t){t=this.getDimension(t),this._approximateExtent[t]=e.slice()},a.prototype.getCalculationInfo=function(e){return this._calculationInfo[e]},a.prototype.setCalculationInfo=function(e,t){_e(e)?W(this._calculationInfo,e):this._calculationInfo[e]=t},a.prototype.getName=function(e){var t=this.getRawIndex(e),r=this._nameList[t];return r==null&&this._nameDimIdx!=null&&(r=xe(this,this._nameDimIdx,t)),r==null&&(r=""),r},a.prototype._getCategory=function(e,t){var r=this._store.get(e,t),i=this._store.getOrdinalMeta(e);return i?i.categories[r]:r},a.prototype.getId=function(e){return Ve(this,this.getRawIndex(e))},a.prototype.count=function(){return this._store.count()},a.prototype.get=function(e,t){var r=this._store,i=this._dimInfos[e];if(i)return r.get(i.storeDimIndex,t)},a.prototype.getByRawIndex=function(e,t){var r=this._store,i=this._dimInfos[e];if(i)return r.getByRawIndex(i.storeDimIndex,t)},a.prototype.getIndices=function(){return this._store.getIndices()},a.prototype.getDataExtent=function(e){return this._store.getDataExtent(this._getStoreDimIndex(e))},a.prototype.getSum=function(e){return this._store.getSum(this._getStoreDimIndex(e))},a.prototype.getMedian=function(e){return this._store.getMedian(this._getStoreDimIndex(e))},a.prototype.getValues=function(e,t){var r=this,i=this._store;return X(e)?i.getValues(Mt(e,function(n){return r._getStoreDimIndex(n)}),t):i.getValues(e)},a.prototype.hasValue=function(e){for(var t=this._dimSummary.dataDimIndicesOnCoord,r=0,i=t.length;r<i;r++)if(isNaN(this._store.get(t[r],e)))return!1;return!0},a.prototype.indexOfName=function(e){for(var t=0,r=this._store.count();t<r;t++)if(this.getName(t)===e)return t;return-1},a.prototype.getRawIndex=function(e){return this._store.getRawIndex(e)},a.prototype.indexOfRawIndex=function(e){return this._store.indexOfRawIndex(e)},a.prototype.rawIndexOf=function(e,t){var r=e&&this._invertedIndicesMap[e],i=r&&r[t];return i==null||isNaN(i)?ri:i},a.prototype.indicesOfNearest=function(e,t,r){return this._store.indicesOfNearest(this._getStoreDimIndex(e),t,r)},a.prototype.each=function(e,t,r){q(e)&&(r=t,t=e,e=[]);var i=r||this,n=Mt(be(e),this._getStoreDimIndex,this);this._store.each(n,i?nt(t,i):t)},a.prototype.filterSelf=function(e,t,r){q(e)&&(r=t,t=e,e=[]);var i=r||this,n=Mt(be(e),this._getStoreDimIndex,this);return this._store=this._store.filter(n,i?nt(t,i):t),this},a.prototype.selectRange=function(e){var t=this,r={},i=or(e);return M(i,function(n){var o=t._getStoreDimIndex(n);r[o]=e[n]}),this._store=this._store.selectRange(r),this},a.prototype.mapArray=function(e,t,r){q(e)&&(r=t,t=e,e=[]),r=r||this;var i=[];return this.each(e,function(){i.push(t&&t.apply(this,arguments))},r),i},a.prototype.map=function(e,t,r,i){var n=r||i||this,o=Mt(be(e),this._getStoreDimIndex,this),s=Se(this);return s._store=this._store.map(o,n?nt(t,n):t),s},a.prototype.modify=function(e,t,r,i){var n=r||i||this,o=Mt(be(e),this._getStoreDimIndex,this);this._store.modify(o,n?nt(t,n):t)},a.prototype.downSample=function(e,t,r,i){var n=Se(this);return n._store=this._store.downSample(this._getStoreDimIndex(e),t,r,i),n},a.prototype.minmaxDownSample=function(e,t){var r=Se(this);return r._store=this._store.minmaxDownSample(this._getStoreDimIndex(e),t),r},a.prototype.lttbDownSample=function(e,t){var r=Se(this);return r._store=this._store.lttbDownSample(this._getStoreDimIndex(e),t),r},a.prototype.getRawDataItem=function(e){return this._store.getRawDataItem(e)},a.prototype.getItemModel=function(e){var t=this.hostModel,r=this.getRawDataItem(e);return new Rt(r,t,t&&t.ecModel)},a.prototype.diff=function(e){var t=this;return new ql(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(r){return Ve(e,r)},function(r){return Ve(t,r)})},a.prototype.getVisual=function(e){var t=this._visual;return t&&t[e]},a.prototype.setVisual=function(e,t){this._visual=this._visual||{},_e(e)?W(this._visual,e):this._visual[e]=t},a.prototype.getItemVisual=function(e,t){var r=this._itemVisuals[e],i=r&&r[t];return i??this.getVisual(t)},a.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},a.prototype.ensureUniqueItemVisual=function(e,t){var r=this._itemVisuals,i=r[e];i||(i=r[e]={});var n=i[t];return n==null&&(n=this.getVisual(t),X(n)?n=n.slice():_e(n)&&(n=W({},n)),i[t]=n),n},a.prototype.setItemVisual=function(e,t,r){var i=this._itemVisuals[e]||{};this._itemVisuals[e]=i,_e(t)?W(i,t):i[t]=r},a.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},a.prototype.setLayout=function(e,t){_e(e)?W(this._layout,e):this._layout[e]=t},a.prototype.getLayout=function(e){return this._layout[e]},a.prototype.getItemLayout=function(e){return this._itemLayouts[e]},a.prototype.setItemLayout=function(e,t,r){this._itemLayouts[e]=r?W(this._itemLayouts[e]||{},t):t},a.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},a.prototype.setItemGraphicEl=function(e,t){var r=this.hostModel&&this.hostModel.seriesIndex;Qo(r,this.dataType,e,t),this._graphicEls[e]=t},a.prototype.getItemGraphicEl=function(e){return this._graphicEls[e]},a.prototype.eachItemGraphicEl=function(e,t){M(this._graphicEls,function(r,i){r&&e&&e.call(t,r,i)})},a.prototype.cloneShallow=function(e){return e||(e=new a(this._schema?this._schema:Mt(this.dimensions,this._getDimInfo,this),this.hostModel)),xr(e,this),e._store=this._store,e},a.prototype.wrapMethod=function(e,t){var r=this[e];q(r)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(e),this[e]=function(){var i=r.apply(this,arguments);return t.apply(this,[i].concat(ts(arguments)))})},a.internalField=function(){ai=function(e){var t=e._invertedIndicesMap;M(t,function(r,i){var n=e._dimInfos[i],o=n.ordinalMeta,s=e._store;if(o){r=t[i]=new ru(o.categories.length);for(var l=0;l<r.length;l++)r[l]=ri;for(var l=0;l<s.count();l++)r[s.get(n.storeDimIndex,l)]=l}})},xe=function(e,t,r){return dr(e._getCategory(t,r),null)},Ve=function(e,t){var r=e._idList[t];return r==null&&e._idDimIdx!=null&&(r=xe(e,e._idDimIdx,t)),r==null&&(r=au+t),r},be=function(e){return X(e)||(e=e!=null?[e]:[]),e},Se=function(e){var t=new a(e._schema?e._schema:Mt(e.dimensions,e._getDimInfo,e),e.hostModel);return xr(t,e),t},xr=function(e,t){M(iu.concat(t.__wrappedMethods||[]),function(r){t.hasOwnProperty(r)&&(e[r]=t[r])}),e.__wrappedMethods=t.__wrappedMethods,M(nu,function(r){e[r]=ie(t[r])}),e._calculationInfo=W({},t._calculationInfo)},br=function(e,t){var r=e._nameList,i=e._idList,n=e._nameDimIdx,o=e._idDimIdx,s=r[t],l=i[t];if(s==null&&n!=null&&(r[t]=s=xe(e,n,t)),l==null&&o!=null&&(i[t]=l=xe(e,o,t)),l==null&&s!=null){var u=e._nameRepeatCount,h=u[s]=(u[s]||0)+1;l=s,h>1&&(l+="__ec__"+h),i[t]=l}}}(),a}();function Yn(a,e){cn(a)||(a=es(a)),e=e||{};var t=e.coordDimensions||[],r=e.dimensionsDefine||a.dimensionsDefine||[],i=ft(),n=[],o=su(a,t,r,e.dimensionsCount),s=e.canOmitUnusedDimensions&&Un(o),l=r===a.dimensionsDefine,u=l?Wn(a):Hn(r),h=e.encodeDefine;!h&&e.encodeDefaulter&&(h=e.encodeDefaulter(a,o));for(var v=ft(h),f=new rs(o),c=0;c<f.length;c++)f[c]=-1;function d(L){var D=f[L];if(D<0){var C=r[L],I=st(C)?C:{name:C},T=new Je,k=I.name;k!=null&&u.get(k)!=null&&(T.name=T.displayName=k),I.type!=null&&(T.type=I.type),I.displayName!=null&&(T.displayName=I.displayName);var P=n.length;return f[L]=P,T.storeDimIndex=L,n.push(T),T}return n[D]}if(!s)for(var c=0;c<o;c++)d(c);v.each(function(L,D){var C=Ia(L).slice();if(C.length===1&&!Z(C[0])&&C[0]<0){v.set(D,!1);return}var I=v.set(D,[]);M(C,function(T,k){var P=Z(T)?u.get(T):T;P!=null&&P<o&&(I[k]=P,m(d(P),D,k))})});var g=0;M(t,function(L){var D,C,I,T;if(Z(L))D=L,T={};else{T=L,D=T.name;var k=T.ordinalMeta;T.ordinalMeta=null,T=W({},T),T.ordinalMeta=k,C=T.dimsDef,I=T.otherDims,T.name=T.coordDim=T.coordDimIndex=T.dimsDef=T.otherDims=null}var P=v.get(D);if(P!==!1){if(P=Ia(P),!P.length)for(var N=0;N<(C&&C.length||1);N++){for(;g<o&&d(g).coordDim!=null;)g++;g<o&&P.push(g++)}M(P,function(z,O){var E=d(z);if(l&&T.type!=null&&(E.type=T.type),m(lt(E,T),D,O),E.name==null&&C){var R=C[O];!st(R)&&(R={name:R}),E.name=E.displayName=R.name,E.defaultTooltip=R.defaultTooltip}I&&lt(E.otherDims,I)})}});function m(L,D,C){vn.get(D)!=null?L.otherDims[D]=C:(L.coordDim=D,L.coordDimIndex=C,i.set(D,!0))}var p=e.generateCoord,y=e.generateCoordCount,_=y!=null;y=p?y||1:0;var x=p||"value";function w(L){L.name==null&&(L.name=L.coordDim)}if(s)M(n,function(L){w(L)}),n.sort(function(L,D){return L.storeDimIndex-D.storeDimIndex});else for(var b=0;b<o;b++){var S=d(b),A=S.coordDim;A==null&&(S.coordDim=lu(x,i,_),S.coordDimIndex=0,(!p||y<=0)&&(S.isExtraCoord=!0),y--),w(S),S.type==null&&(as(a,b)===is.Must||S.isExtraCoord&&(S.otherDims.itemName!=null||S.otherDims.seriesName!=null))&&(S.type="ordinal")}return ou(n),new Vn({source:a,dimensions:n,fullDimensionCount:o,dimensionOmitted:s})}function ou(a){for(var e=ft(),t=0;t<a.length;t++){var r=a[t],i=r.name,n=e.get(i)||0;n>0&&(r.name=i+(n-1)),n++,e.set(i,n)}}function su(a,e,t,r){var i=Math.max(a.dimensionsDetectedCount||1,e.length,t.length,r||0);return M(e,function(n){var o;st(n)&&(o=n.dimsDef)&&(i=Math.max(i,o.length))}),i}function lu(a,e,t){if(t||e.hasKey(a)){for(var r=0;e.hasKey(a+r);)r++;a+=r}return e.set(a,!0),a}var uu=function(){function a(e){this.coordSysDims=[],this.axisMap=ft(),this.categoryAxisMap=ft(),this.coordSysName=e}return a}();function hu(a){var e=a.get("coordinateSystem"),t=new uu(e),r=vu[e];if(r)return r(a,t,t.axisMap,t.categoryAxisMap),t}var vu={cartesian2d:function(a,e,t,r){var i=a.getReferringComponents("xAxis",Nt).models[0],n=a.getReferringComponents("yAxis",Nt).models[0];e.coordSysDims=["x","y"],t.set("x",i),t.set("y",n),re(i)&&(r.set("x",i),e.firstCategoryDimIndex=0),re(n)&&(r.set("y",n),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},singleAxis:function(a,e,t,r){var i=a.getReferringComponents("singleAxis",Nt).models[0];e.coordSysDims=["single"],t.set("single",i),re(i)&&(r.set("single",i),e.firstCategoryDimIndex=0)},polar:function(a,e,t,r){var i=a.getReferringComponents("polar",Nt).models[0],n=i.findAxisModel("radiusAxis"),o=i.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],t.set("radius",n),t.set("angle",o),re(n)&&(r.set("radius",n),e.firstCategoryDimIndex=0),re(o)&&(r.set("angle",o),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},geo:function(a,e,t,r){e.coordSysDims=["lng","lat"]},parallel:function(a,e,t,r){var i=a.ecModel,n=i.getComponent("parallel",a.get("parallelIndex")),o=e.coordSysDims=n.dimensions.slice();M(n.parallelAxisIndex,function(s,l){var u=i.getComponent("parallelAxis",s),h=o[l];t.set(h,u),re(u)&&(r.set(h,u),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=l))})}};function re(a){return a.get("type")==="category"}function cu(a,e,t){t=t||{};var r=t.byIndex,i=t.stackedCoordDimension,n,o,s;fu(e)?n=e:(o=e.schema,n=o.dimensions,s=e.store);var l=!!(a&&a.get("stack")),u,h,v,f;if(M(n,function(y,_){Z(y)&&(n[_]=y={name:y}),l&&!y.isExtraCoord&&(!r&&!u&&y.ordinalMeta&&(u=y),!h&&y.type!=="ordinal"&&y.type!=="time"&&(!i||i===y.coordDim)&&(h=y))}),h&&!r&&!u&&(r=!0),h){v="__\0ecstackresult_"+a.id,f="__\0ecstackedover_"+a.id,u&&(u.createInvertedIndices=!0);var c=h.coordDim,d=h.type,g=0;M(n,function(y){y.coordDim===c&&g++});var m={name:v,coordDim:c,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:n.length},p={name:f,coordDim:f,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:n.length+1};o?(s&&(m.storeDimIndex=s.ensureCalculationDimension(f,d),p.storeDimIndex=s.ensureCalculationDimension(v,d)),o.appendCalculationDimension(m),o.appendCalculationDimension(p)):(n.push(m),n.push(p))}return{stackedDimension:h&&h.name,stackedByDimension:u&&u.name,isStackedByIndex:r,stackedOverDimension:f,stackResultDimension:v}}function fu(a){return!Fn(a.schema)}function ve(a,e){return!!e&&e===a.getCalculationInfo("stackedDimension")}function du(a,e){return ve(a,e)?a.getCalculationInfo("stackResultDimension"):e}function gu(a,e){var t=a.get("coordinateSystem"),r=os.get(t),i;return e&&e.coordSysDims&&(i=Y(e.coordSysDims,function(n){var o={name:n},s=e.axisMap.get(n);if(s){var l=s.get("type");o.type=Jl(l)}return o})),i||(i=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),i}function pu(a,e,t){var r,i;return t&&M(a,function(n,o){var s=n.coordDim,l=t.categoryAxisMap.get(s);l&&(r==null&&(r=o),n.ordinalMeta=l.getOrdinalMeta(),e&&(n.createInvertedIndices=!0)),n.otherDims.itemName!=null&&(i=!0)}),!i&&r!=null&&(a[r].otherDims.itemName=0),r}function va(a,e,t){t=t||{};var r=e.getSourceManager(),i,n=!1;i=r.getSource(),n=i.sourceFormat===oa;var o=hu(e),s=gu(e,o),l=t.useEncodeDefaulter,u=q(l)?l:l?Q(ns,s,e):null,h={coordDimensions:s,generateCoord:t.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!n},v=Yn(i,h),f=pu(v.dimensions,t.createInvertedIndices,o),c=n?null:r.getSharedDataStore(v),d=cu(e,{schema:v,store:c}),g=new Xn(v,e);g.setCalculationInfo(d);var m=f!=null&&mu(i)?function(p,y,_,x){return x===f?_:this.defaultDimValueGetter(p,y,_,x)}:null;return g.hasItemOption=!1,g.initData(n?i:c,null,m),g}function mu(a){if(a.sourceFormat===oa){var e=yu(a.data||[]);return!X(ss(e))}}function yu(a){for(var e=0;e<a.length&&a[e]==null;)e++;return a[e]}var wt=function(){function a(e){this._setting=e||{},this._extent=[1/0,-1/0]}return a.prototype.getSetting=function(e){return this._setting[e]},a.prototype.unionExtent=function(e){var t=this._extent;e[0]<t[0]&&(t[0]=e[0]),e[1]>t[1]&&(t[1]=e[1])},a.prototype.unionExtentFromData=function(e,t){this.unionExtent(e.getApproximateExtent(t))},a.prototype.getExtent=function(){return this._extent.slice()},a.prototype.setExtent=function(e,t){var r=this._extent;isNaN(e)||(r[0]=e),isNaN(t)||(r[1]=t)},a.prototype.isInExtentRange=function(e){return this._extent[0]<=e&&this._extent[1]>=e},a.prototype.isBlank=function(){return this._isBlank},a.prototype.setBlank=function(e){this._isBlank=e},a}();ls(wt);var _u=0,Yr=function(){function a(e){this.categories=e.categories||[],this._needCollect=e.needCollect,this._deduplication=e.deduplication,this.uid=++_u}return a.createByAxisModel=function(e){var t=e.option,r=t.data,i=r&&Y(r,xu);return new a({categories:i,needCollect:!i,deduplication:t.dedplication!==!1})},a.prototype.getOrdinal=function(e){return this._getOrCreateMap().get(e)},a.prototype.parseAndCollect=function(e){var t,r=this._needCollect;if(!Z(e)&&!r)return e;if(r&&!this._deduplication)return t=this.categories.length,this.categories[t]=e,t;var i=this._getOrCreateMap();return t=i.get(e),t==null&&(r?(t=this.categories.length,this.categories[t]=e,i.set(e,t)):t=NaN),t},a.prototype._getOrCreateMap=function(){return this._map||(this._map=ft(this.categories))},a}();function xu(a){return st(a)&&a.value!=null?a.value:a+""}function Zr(a){return a.type==="interval"||a.type==="log"}function bu(a,e,t,r){var i={},n=a[1]-a[0],o=i.interval=fn(n/e);t!=null&&o<t&&(o=i.interval=t),r!=null&&o>r&&(o=i.interval=r);var s=i.intervalPrecision=Zn(o),l=i.niceTickExtent=[at(Math.ceil(a[0]/o)*o,s),at(Math.floor(a[1]/o)*o,s)];return Su(l,a),i}function Sr(a){var e=Math.pow(10,us(a)),t=a/e;return t?t===2?t=3:t===3?t=5:t*=2:t=1,at(t*e)}function Zn(a){return sa(a)+2}function ii(a,e,t){a[e]=Math.max(Math.min(a[e],t[1]),t[0])}function Su(a,e){!isFinite(a[0])&&(a[0]=e[0]),!isFinite(a[1])&&(a[1]=e[1]),ii(a,0,e),ii(a,1,e),a[0]>a[1]&&(a[0]=a[1])}function lr(a,e){return a>=e[0]&&a<=e[1]}function ur(a,e){return e[1]===e[0]?.5:(a-e[0])/(e[1]-e[0])}function hr(a,e){return a*(e[1]-e[0])+e[0]}var ca=function(a){G(e,a);function e(t){var r=a.call(this,t)||this;r.type="ordinal";var i=r.getSetting("ordinalMeta");return i||(i=new Yr({})),X(i)&&(i=new Yr({categories:Y(i,function(n){return st(n)?n.value:n})})),r._ordinalMeta=i,r._extent=r.getSetting("extent")||[0,i.categories.length-1],r}return e.prototype.parse=function(t){return t==null?NaN:Z(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return t=this.parse(t),lr(t,this._extent)&&this._ordinalMeta.categories[t]!=null},e.prototype.normalize=function(t){return t=this._getTickNumber(this.parse(t)),ur(t,this._extent)},e.prototype.scale=function(t){return t=Math.round(hr(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],r=this._extent,i=r[0];i<=r[1];)t.push({value:i}),i++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(t==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var r=t.ordinalNumbers,i=this._ordinalNumbersByTick=[],n=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,l=Math.min(s,r.length);o<l;++o){var u=r[o];i[o]=u,n[u]=o}for(var h=0;o<s;++o){for(;n[h]!=null;)h++;i.push(h),n[h]=o}},e.prototype._getTickNumber=function(t){var r=this._ticksByOrdinalNumber;return r&&t>=0&&t<r.length?r[t]:t},e.prototype.getRawOrdinalNumber=function(t){var r=this._ordinalNumbersByTick;return r&&t>=0&&t<r.length?r[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var r=this.getRawOrdinalNumber(t.value),i=this._ordinalMeta.categories[r];return i==null?"":i+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,r){this.unionExtent(t.getApproximateExtent(r))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(wt);wt.registerClass(ca);var Yt=at,ce=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}return e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return lr(t,this._extent)},e.prototype.normalize=function(t){return ur(t,this._extent)},e.prototype.scale=function(t){return hr(t,this._extent)},e.prototype.setExtent=function(t,r){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(r)||(i[1]=parseFloat(r))},e.prototype.unionExtent=function(t){var r=this._extent;t[0]<r[0]&&(r[0]=t[0]),t[1]>r[1]&&(r[1]=t[1]),this.setExtent(r[0],r[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Zn(t)},e.prototype.getTicks=function(t){var r=this._interval,i=this._extent,n=this._niceExtent,o=this._intervalPrecision,s=[];if(!r)return s;var l=1e4;i[0]<n[0]&&(t?s.push({value:Yt(n[0]-r,o)}):s.push({value:i[0]}));for(var u=n[0];u<=n[1]&&(s.push({value:u}),u=Yt(u+r,o),u!==s[s.length-1].value);)if(s.length>l)return[];var h=s.length?s[s.length-1].value:n[1];return i[1]>h&&(t?s.push({value:Yt(h+r,o)}):s.push({value:i[1]})),s},e.prototype.getMinorTicks=function(t){for(var r=this.getTicks(!0),i=[],n=this.getExtent(),o=1;o<r.length;o++){for(var s=r[o],l=r[o-1],u=0,h=[],v=s.value-l.value,f=v/t;u<t-1;){var c=Yt(l.value+(u+1)*f);c>n[0]&&c<n[1]&&h.push(c),u++}i.push(h)}return i},e.prototype.getLabel=function(t,r){if(t==null)return"";var i=r&&r.precision;i==null?i=sa(t.value)||0:i==="auto"&&(i=this._intervalPrecision);var n=Yt(t.value,i,!0);return hs(n)},e.prototype.calcNiceTicks=function(t,r,i){t=t||5;var n=this._extent,o=n[1]-n[0];if(isFinite(o)){o<0&&(o=-o,n.reverse());var s=bu(n,t,r,i);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var r=this._extent;if(r[0]===r[1])if(r[0]!==0){var i=Math.abs(r[0]);t.fixMax||(r[1]+=i/2),r[0]-=i/2}else r[1]=1;var n=r[1]-r[0];isFinite(n)||(r[0]=0,r[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var o=this._interval;t.fixMin||(r[0]=Yt(Math.floor(r[0]/o)*o)),t.fixMax||(r[1]=Yt(Math.ceil(r[1]/o)*o))},e.prototype.setNiceExtent=function(t,r){this._niceExtent=[t,r]},e.type="interval",e}(wt);wt.registerClass(ce);var $n=typeof Float32Array<"u",wu=$n?Float32Array:Array;function Ct(a){return X(a)?$n?new Float32Array(a):a:new wu(a)}var Au="__ec_stack_";function qn(a){return a.get("stack")||Au+a.seriesIndex}function fa(a){return a.dim+a.index}function Kn(a,e){var t=[];return e.eachSeriesByType(a,function(r){Jn(r)&&t.push(r)}),t}function Cu(a){var e={};M(a,function(l){var u=l.coordinateSystem,h=u.getBaseAxis();if(!(h.type!=="time"&&h.type!=="value"))for(var v=l.getData(),f=h.dim+"_"+h.index,c=v.getDimensionIndex(v.mapDimension(h.dim)),d=v.getStore(),g=0,m=d.count();g<m;++g){var p=d.get(c,g);e[f]?e[f].push(p):e[f]=[p]}});var t={};for(var r in e)if(e.hasOwnProperty(r)){var i=e[r];if(i){i.sort(function(l,u){return l-u});for(var n=null,o=1;o<i.length;++o){var s=i[o]-i[o-1];s>0&&(n=n===null?s:Math.min(n,s))}t[r]=n}}return t}function jn(a){var e=Cu(a),t=[];return M(a,function(r){var i=r.coordinateSystem,n=i.getBaseAxis(),o=n.getExtent(),s;if(n.type==="category")s=n.getBandWidth();else if(n.type==="value"||n.type==="time"){var l=n.dim+"_"+n.index,u=e[l],h=Math.abs(o[1]-o[0]),v=n.scale.getExtent(),f=Math.abs(v[1]-v[0]);s=u?h/f*u:h}else{var c=r.getData();s=Math.abs(o[1]-o[0])/c.count()}var d=it(r.get("barWidth"),s),g=it(r.get("barMaxWidth"),s),m=it(r.get("barMinWidth")||(Qn(r)?.5:1),s),p=r.get("barGap"),y=r.get("barCategoryGap");t.push({bandWidth:s,barWidth:d,barMaxWidth:g,barMinWidth:m,barGap:p,barCategoryGap:y,axisKey:fa(n),stackId:qn(r)})}),Du(t)}function Du(a){var e={};M(a,function(r,i){var n=r.axisKey,o=r.bandWidth,s=e[n]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},l=s.stacks;e[n]=s;var u=r.stackId;l[u]||s.autoWidthCount++,l[u]=l[u]||{width:0,maxWidth:0};var h=r.barWidth;h&&!l[u].width&&(l[u].width=h,h=Math.min(s.remainedWidth,h),s.remainedWidth-=h);var v=r.barMaxWidth;v&&(l[u].maxWidth=v);var f=r.barMinWidth;f&&(l[u].minWidth=f);var c=r.barGap;c!=null&&(s.gap=c);var d=r.barCategoryGap;d!=null&&(s.categoryGap=d)});var t={};return M(e,function(r,i){t[i]={};var n=r.stacks,o=r.bandWidth,s=r.categoryGap;if(s==null){var l=or(n).length;s=Math.max(35-l*4,15)+"%"}var u=it(s,o),h=it(r.gap,1),v=r.remainedWidth,f=r.autoWidthCount,c=(v-u)/(f+(f-1)*h);c=Math.max(c,0),M(n,function(p){var y=p.maxWidth,_=p.minWidth;if(p.width){var x=p.width;y&&(x=Math.min(x,y)),_&&(x=Math.max(x,_)),p.width=x,v-=x+h*x,f--}else{var x=c;y&&y<x&&(x=Math.min(y,v)),_&&_>x&&(x=_),x!==c&&(p.width=x,v-=x+h*x,f--)}}),c=(v-u)/(f+(f-1)*h),c=Math.max(c,0);var d=0,g;M(n,function(p,y){p.width||(p.width=c),g=p,d+=p.width*(1+h)}),g&&(d-=g.width*h);var m=-d/2;M(n,function(p,y){t[i][y]=t[i][y]||{bandWidth:o,offset:m,width:p.width},m+=p.width*(1+h)})}),t}function Lu(a,e,t){if(a&&e){var r=a[fa(e)];return r}}function Tu(a,e){var t=Kn(a,e),r=jn(t);M(t,function(i){var n=i.getData(),o=i.coordinateSystem,s=o.getBaseAxis(),l=qn(i),u=r[fa(s)][l],h=u.offset,v=u.width;n.setLayout({bandWidth:u.bandWidth,offset:h,size:v})})}function Iu(a){return{seriesType:a,plan:dn(),reset:function(e){if(Jn(e)){var t=e.getData(),r=e.coordinateSystem,i=r.getBaseAxis(),n=r.getOtherAxis(i),o=t.getDimensionIndex(t.mapDimension(n.dim)),s=t.getDimensionIndex(t.mapDimension(i.dim)),l=e.get("showBackground",!0),u=t.mapDimension(n.dim),h=t.getCalculationInfo("stackResultDimension"),v=ve(t,u)&&!!t.getCalculationInfo("stackedOnSeries"),f=n.isHorizontal(),c=Mu(i,n),d=Qn(e),g=e.get("barMinHeight")||0,m=h&&t.getDimensionIndex(h),p=t.getLayout("size"),y=t.getLayout("offset");return{progress:function(_,x){for(var w=_.count,b=d&&Ct(w*3),S=d&&l&&Ct(w*3),A=d&&Ct(w),L=r.master.getRect(),D=f?L.width:L.height,C,I=x.getStore(),T=0;(C=_.next())!=null;){var k=I.get(v?m:o,C),P=I.get(s,C),N=c,z=void 0;v&&(z=+k-I.get(o,C));var O=void 0,E=void 0,R=void 0,B=void 0;if(f){var F=r.dataToPoint([k,P]);if(v){var $=r.dataToPoint([z,P]);N=$[0]}O=N,E=F[1]+y,R=F[0]-N,B=p,Math.abs(R)<g&&(R=(R<0?-1:1)*g)}else{var F=r.dataToPoint([P,k]);if(v){var $=r.dataToPoint([P,z]);N=$[1]}O=F[0]+y,E=N,R=p,B=F[1]-N,Math.abs(B)<g&&(B=(B<=0?-1:1)*g)}d?(b[T]=O,b[T+1]=E,b[T+2]=f?R:B,S&&(S[T]=f?L.x:O,S[T+1]=f?E:L.y,S[T+2]=D),A[C]=C):x.setItemLayout(C,{x:O,y:E,width:R,height:B}),T+=3}d&&x.setLayout({largePoints:b,largeDataIndices:A,largeBackgroundPoints:S,valueAxisHorizontal:f})}}}}}}function Jn(a){return a.coordinateSystem&&a.coordinateSystem.type==="cartesian2d"}function Qn(a){return a.pipelineContext&&a.pipelineContext.large}function Mu(a,e){var t=e.model.get("startValue");return t||(t=0),e.toGlobalCoord(e.dataToCoord(e.type==="log"?t>0?t:1:t))}var Pu=function(a,e,t,r){for(;t<r;){var i=t+r>>>1;a[i][1]<e?t=i+1:r=i}return t},to=function(a){G(e,a);function e(t){var r=a.call(this,t)||this;return r.type="time",r}return e.prototype.getLabel=function(t){var r=this.getSetting("useUTC");return gn(t.value,Ma[vs(De(this._minLevelUnit))]||Ma.second,r,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,r,i){var n=this.getSetting("useUTC"),o=this.getSetting("locale");return cs(t,r,i,o,n)},e.prototype.getTicks=function(){var t=this._interval,r=this._extent,i=[];if(!t)return i;i.push({value:r[0],level:0});var n=this.getSetting("useUTC"),o=Gu(this._minLevelUnit,this._approxInterval,n,r);return i=i.concat(o),i.push({value:r[1],level:0}),i},e.prototype.calcNiceExtent=function(t){var r=this._extent;if(r[0]===r[1]&&(r[0]-=pt,r[1]+=pt),r[1]===-1/0&&r[0]===1/0){var i=new Date;r[1]=+new Date(i.getFullYear(),i.getMonth(),i.getDate()),r[0]=r[1]-pt}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,r,i){t=t||10;var n=this._extent,o=n[1]-n[0];this._approxInterval=o/t,r!=null&&this._approxInterval<r&&(this._approxInterval=r),i!=null&&this._approxInterval>i&&(this._approxInterval=i);var s=Fe.length,l=Math.min(Pu(Fe,this._approxInterval,0,s),s-1);this._interval=Fe[l][1],this._minLevelUnit=Fe[Math.max(l-1,0)][0]},e.prototype.parse=function(t){return Tt(t)?t:+Br(t)},e.prototype.contain=function(t){return lr(this.parse(t),this._extent)},e.prototype.normalize=function(t){return ur(this.parse(t),this._extent)},e.prototype.scale=function(t){return hr(t,this._extent)},e.type="time",e}(ce),Fe=[["second",pn],["minute",mn],["hour",je],["quarter-day",je*6],["half-day",je*12],["day",pt*1.2],["half-week",pt*3.5],["week",pt*7],["month",pt*31],["quarter",pt*95],["half-year",Pa/2],["year",Pa]];function ku(a,e,t,r){var i=Br(e),n=Br(t),o=function(d){return ka(i,d,r)===ka(n,d,r)},s=function(){return o("year")},l=function(){return s()&&o("month")},u=function(){return l()&&o("day")},h=function(){return u()&&o("hour")},v=function(){return h()&&o("minute")},f=function(){return v()&&o("second")},c=function(){return f()&&o("millisecond")};switch(a){case"year":return s();case"month":return l();case"day":return u();case"hour":return h();case"minute":return v();case"second":return f();case"millisecond":return c()}}function Ou(a,e){return a/=pt,a>16?16:a>7.5?7:a>3.5?4:a>1.5?2:1}function Eu(a){var e=30*pt;return a/=e,a>6?6:a>3?3:a>2?2:1}function Ru(a){return a/=je,a>12?12:a>6?6:a>3.5?4:a>2?2:1}function ni(a,e){return a/=e?mn:pn,a>30?30:a>20?20:a>15?15:a>10?10:a>5?5:a>2?2:1}function Nu(a){return fn(a)}function Bu(a,e,t){var r=new Date(a);switch(De(e)){case"year":case"month":r[wn(t)](0);case"day":r[Sn(t)](1);case"hour":r[bn(t)](0);case"minute":r[xn(t)](0);case"second":r[yn(t)](0),r[_n(t)](0)}return r.getTime()}function Gu(a,e,t,r){var i=1e4,n=fs,o=0;function s(D,C,I,T,k,P,N){for(var z=new Date(C),O=C,E=z[T]();O<I&&O<=r[1];)N.push({value:O}),E+=D,z[k](E),O=z.getTime();N.push({value:O,notAdd:!0})}function l(D,C,I){var T=[],k=!C.length;if(!ku(De(D),r[0],r[1],t)){k&&(C=[{value:Bu(new Date(r[0]),D,t)},{value:r[1]}]);for(var P=0;P<C.length-1;P++){var N=C[P].value,z=C[P+1].value;if(N!==z){var O=void 0,E=void 0,R=void 0,B=!1;switch(D){case"year":O=Math.max(1,Math.round(e/pt/365)),E=bs(t),R=Ss(t);break;case"half-year":case"quarter":case"month":O=Eu(e),E=xs(t),R=wn(t);break;case"week":case"half-week":case"day":O=Ou(e),E=_s(t),R=Sn(t),B=!0;break;case"half-day":case"quarter-day":case"hour":O=Ru(e),E=ys(t),R=bn(t);break;case"minute":O=ni(e,!0),E=ms(t),R=xn(t);break;case"second":O=ni(e,!1),E=ps(t),R=yn(t);break;case"millisecond":O=Nu(e),E=gs(t),R=_n(t);break}s(O,N,z,E,R,B,T),D==="year"&&I.length>1&&P===0&&I.unshift({value:I[0].value-O})}}for(var P=0;P<T.length;P++)I.push(T[P]);return T}}for(var u=[],h=[],v=0,f=0,c=0;c<n.length&&o++<i;++c){var d=De(n[c]);if(ds(n[c])){l(n[c],u[u.length-1]||[],h);var g=n[c+1]?De(n[c+1]):null;if(d!==g){if(h.length){f=v,h.sort(function(D,C){return D.value-C.value});for(var m=[],p=0;p<h.length;++p){var y=h[p].value;(p===0||h[p-1].value!==y)&&(m.push(h[p]),y>=r[0]&&y<=r[1]&&v++)}var _=(r[1]-r[0])/e;if(v>_*1.5&&f>_/1.5||(u.push(m),v>_||a===n[c]))break}h=[]}}}for(var x=ne(Y(u,function(D){return ne(D,function(C){return C.value>=r[0]&&C.value<=r[1]&&!C.notAdd})}),function(D){return D.length>0}),w=[],b=x.length-1,c=0;c<x.length;++c)for(var S=x[c],A=0;A<S.length;++A)w.push({value:S[A].value,level:b-c});w.sort(function(D,C){return D.value-C.value});for(var L=[],c=0;c<w.length;++c)(c===0||w[c].value!==w[c-1].value)&&L.push(w[c]);return L}wt.registerClass(to);var oi=wt.prototype,Te=ce.prototype,zu=at,Vu=Math.floor,Fu=Math.ceil,He=Math.pow,gt=Math.log,da=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new ce,t._interval=0,t}return e.prototype.getTicks=function(t){var r=this._originalScale,i=this._extent,n=r.getExtent(),o=Te.getTicks.call(this,t);return Y(o,function(s){var l=s.value,u=at(He(this.base,l));return u=l===i[0]&&this._fixMin?We(u,n[0]):u,u=l===i[1]&&this._fixMax?We(u,n[1]):u,{value:u}},this)},e.prototype.setExtent=function(t,r){var i=gt(this.base);t=gt(Math.max(0,t))/i,r=gt(Math.max(0,r))/i,Te.setExtent.call(this,t,r)},e.prototype.getExtent=function(){var t=this.base,r=oi.getExtent.call(this);r[0]=He(t,r[0]),r[1]=He(t,r[1]);var i=this._originalScale,n=i.getExtent();return this._fixMin&&(r[0]=We(r[0],n[0])),this._fixMax&&(r[1]=We(r[1],n[1])),r},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var r=this.base;t[0]=gt(t[0])/gt(r),t[1]=gt(t[1])/gt(r),oi.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,r){this.unionExtent(t.getApproximateExtent(r))},e.prototype.calcNiceTicks=function(t){t=t||10;var r=this._extent,i=r[1]-r[0];if(!(i===1/0||i<=0)){var n=ws(i),o=t/i*n;for(o<=.5&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var s=[at(Fu(r[0]/n)*n),at(Vu(r[1]/n)*n)];this._interval=n,this._niceExtent=s}},e.prototype.calcNiceExtent=function(t){Te.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return t=gt(t)/gt(this.base),lr(t,this._extent)},e.prototype.normalize=function(t){return t=gt(t)/gt(this.base),ur(t,this._extent)},e.prototype.scale=function(t){return t=hr(t,this._extent),He(this.base,t)},e.type="log",e}(wt),eo=da.prototype;eo.getMinorTicks=Te.getMinorTicks;eo.getLabel=Te.getLabel;function We(a,e){return zu(a,sa(e))}wt.registerClass(da);var Hu=function(){function a(e,t,r){this._prepareParams(e,t,r)}return a.prototype._prepareParams=function(e,t,r){r[1]<r[0]&&(r=[NaN,NaN]),this._dataMin=r[0],this._dataMax=r[1];var i=this._isOrdinal=e.type==="ordinal";this._needCrossZero=e.type==="interval"&&t.getNeedCrossZero&&t.getNeedCrossZero();var n=t.get("min",!0);n==null&&(n=t.get("startValue",!0));var o=this._modelMinRaw=n;q(o)?this._modelMinNum=Ue(e,o({min:r[0],max:r[1]})):o!=="dataMin"&&(this._modelMinNum=Ue(e,o));var s=this._modelMaxRaw=t.get("max",!0);if(q(s)?this._modelMaxNum=Ue(e,s({min:r[0],max:r[1]})):s!=="dataMax"&&(this._modelMaxNum=Ue(e,s)),i)this._axisDataLen=t.getCategories().length;else{var l=t.get("boundaryGap"),u=X(l)?l:[l||0,l||0];typeof u[0]=="boolean"||typeof u[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Gr(u[0],1),Gr(u[1],1)]}},a.prototype.calculate=function(){var e=this._isOrdinal,t=this._dataMin,r=this._dataMax,i=this._axisDataLen,n=this._boundaryGapInner,o=e?null:r-t||Math.abs(t),s=this._modelMinRaw==="dataMin"?t:this._modelMinNum,l=this._modelMaxRaw==="dataMax"?r:this._modelMaxNum,u=s!=null,h=l!=null;s==null&&(s=e?i?0:NaN:t-n[0]*o),l==null&&(l=e?i?i-1:NaN:r+n[1]*o),(s==null||!isFinite(s))&&(s=NaN),(l==null||!isFinite(l))&&(l=NaN);var v=zr(s)||zr(l)||e&&!i;this._needCrossZero&&(s>0&&l>0&&!u&&(s=0),s<0&&l<0&&!h&&(l=0));var f=this._determinedMin,c=this._determinedMax;return f!=null&&(s=f,u=!0),c!=null&&(l=c,h=!0),{min:s,max:l,minFixed:u,maxFixed:h,isBlank:v}},a.prototype.modifyDataMinMax=function(e,t){this[Uu[e]]=t},a.prototype.setDeterminedMinMax=function(e,t){var r=Wu[e];this[r]=t},a.prototype.freeze=function(){this.frozen=!0},a}(),Wu={min:"_determinedMin",max:"_determinedMax"},Uu={min:"_dataMin",max:"_dataMax"};function Xu(a,e,t){var r=a.rawExtentInfo;return r||(r=new Hu(a,e,t),a.rawExtentInfo=r,r)}function Ue(a,e){return e==null?null:zr(e)?NaN:a.parse(e)}function ro(a,e){var t=a.type,r=Xu(a,e,a.getExtent()).calculate();a.setBlank(r.isBlank);var i=r.min,n=r.max,o=e.ecModel;if(o&&t==="time"){var s=Kn("bar",o),l=!1;if(M(s,function(v){l=l||v.getBaseAxis()===e.axis}),l){var u=jn(s),h=Yu(i,n,e,u);i=h.min,n=h.max}}return{extent:[i,n],fixMin:r.minFixed,fixMax:r.maxFixed}}function Yu(a,e,t,r){var i=t.axis.getExtent(),n=Math.abs(i[1]-i[0]),o=Lu(r,t.axis);if(o===void 0)return{min:a,max:e};var s=1/0;M(o,function(c){s=Math.min(c.offset,s)});var l=-1/0;M(o,function(c){l=Math.max(c.offset+c.width,l)}),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-a,v=1-(s+l)/n,f=h/v-h;return e+=f*(l/u),a-=f*(s/u),{min:a,max:e}}function si(a,e){var t=e,r=ro(a,t),i=r.extent,n=t.get("splitNumber");a instanceof da&&(a.base=t.get("logBase"));var o=a.type,s=t.get("interval"),l=o==="interval"||o==="time";a.setExtent(i[0],i[1]),a.calcNiceExtent({splitNumber:n,fixMin:r.fixMin,fixMax:r.fixMax,minInterval:l?t.get("minInterval"):null,maxInterval:l?t.get("maxInterval"):null}),s!=null&&a.setInterval&&a.setInterval(s)}function Zu(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new ca({ordinalMeta:a.getOrdinalMeta?a.getOrdinalMeta():a.getCategories(),extent:[1/0,-1/0]});case"time":return new to({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new(wt.getClass(e)||ce)}}function $u(a){var e=a.scale.getExtent(),t=e[0],r=e[1];return!(t>0&&r>0||t<0&&r<0)}function fe(a){var e=a.getLabelModel().get("formatter"),t=a.type==="category"?a.scale.getExtent()[0]:null;return a.scale.type==="time"?function(r){return function(i,n){return a.scale.getFormattedLabel(i,n,r)}}(e):Z(e)?function(r){return function(i){var n=a.scale.getLabel(i),o=r.replace("{value}",n??"");return o}}(e):q(e)?function(r){return function(i,n){return t!=null&&(n=i.value-t),r(ga(a,i),n,i.level!=null?{level:i.level}:null)}}(e):function(r){return a.scale.getLabel(r)}}function ga(a,e){return a.type==="category"?a.scale.getLabel(e):e.value}function qu(a){var e=a.model,t=a.scale;if(!(!e.get(["axisLabel","show"])||t.isBlank())){var r,i,n=t.getExtent();t instanceof ca?i=t.count():(r=t.getTicks(),i=r.length);var o=a.getLabelModel(),s=fe(a),l,u=1;i>40&&(u=Math.ceil(i/40));for(var h=0;h<i;h+=u){var v=r?r[h]:{value:n[0]+h},f=s(v,h),c=o.getTextRect(f),d=Ku(c,o.get("rotate")||0);l?l.union(d):l=d}return l}}function Ku(a,e){var t=e*Math.PI/180,r=a.width,i=a.height,n=r*Math.abs(Math.cos(t))+Math.abs(i*Math.sin(t)),o=r*Math.abs(Math.sin(t))+Math.abs(i*Math.cos(t)),s=new Bt(a.x,a.y,n,o);return s}function pa(a){var e=a.get("interval");return e??"auto"}function ao(a){return a.type==="category"&&pa(a.getLabelModel())===0}function ju(a,e){var t={};return M(a.mapDimensionsAll(e),function(r){t[du(a,r)]=!0}),or(t)}var Ju=function(){function a(){}return a.prototype.getNeedCrossZero=function(){var e=this.option;return!e.scale},a.prototype.getCoordSysModel=function(){},a}(),li=[],Qu={registerPreprocessor:Bs,registerProcessor:Ns,registerPostInit:Rs,registerPostUpdate:Es,registerUpdateLifecycle:Os,registerAction:ks,registerCoordinateSystem:Ps,registerLayout:Ms,registerVisual:Is,registerTransform:Ts,registerLoading:Ls,registerMap:Ds,registerImpl:Cs,PRIORITY:As,ComponentModel:Lt,ComponentView:Wt,SeriesModel:oe,ChartView:qt,registerComponentModel:function(a){Lt.registerClass(a)},registerComponentView:function(a){Wt.registerClass(a)},registerSeriesModel:function(a){oe.registerClass(a)},registerChartView:function(a){qt.registerClass(a)},registerSubTypeDefaulter:function(a,e){Lt.registerSubTypeDefaulter(a,e)},registerPainter:function(a,e){Gs(a,e)}};function Ut(a){if(X(a)){M(a,function(e){Ut(e)});return}Ft(li,a)>=0||(li.push(a),q(a)&&(a={install:a}),a.install(Qu))}var ke=Xt();function io(a,e){var t=Y(e,function(r){return a.scale.parse(r)});return a.type==="time"&&t.length>0&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function th(a){var e=a.getLabelModel().get("customValues");if(e){var t=fe(a),r=a.scale.getExtent(),i=io(a,e),n=ne(i,function(o){return o>=r[0]&&o<=r[1]});return{labels:Y(n,function(o){var s={value:o};return{formattedLabel:t(s),rawLabel:a.scale.getLabel(s),tickValue:o}})}}return a.type==="category"?rh(a):ih(a)}function eh(a,e){var t=a.getTickModel().get("customValues");if(t){var r=a.scale.getExtent(),i=io(a,t);return{ticks:ne(i,function(n){return n>=r[0]&&n<=r[1]})}}return a.type==="category"?ah(a,e):{ticks:Y(a.scale.getTicks(),function(n){return n.value})}}function rh(a){var e=a.getLabelModel(),t=no(a,e);return!e.get("show")||a.scale.isBlank()?{labels:[],labelCategoryInterval:t.labelCategoryInterval}:t}function no(a,e){var t=oo(a,"labels"),r=pa(e),i=so(t,r);if(i)return i;var n,o;return q(r)?n=ho(a,r):(o=r==="auto"?nh(a):r,n=uo(a,o)),lo(t,r,{labels:n,labelCategoryInterval:o})}function ah(a,e){var t=oo(a,"ticks"),r=pa(e),i=so(t,r);if(i)return i;var n,o;if((!e.get("show")||a.scale.isBlank())&&(n=[]),q(r))n=ho(a,r,!0);else if(r==="auto"){var s=no(a,a.getLabelModel());o=s.labelCategoryInterval,n=Y(s.labels,function(l){return l.tickValue})}else o=r,n=uo(a,o,!0);return lo(t,r,{ticks:n,tickCategoryInterval:o})}function ih(a){var e=a.scale.getTicks(),t=fe(a);return{labels:Y(e,function(r,i){return{level:r.level,formattedLabel:t(r,i),rawLabel:a.scale.getLabel(r),tickValue:r.value}})}}function oo(a,e){return ke(a)[e]||(ke(a)[e]=[])}function so(a,e){for(var t=0;t<a.length;t++)if(a[t].key===e)return a[t].value}function lo(a,e,t){return a.push({key:e,value:t}),t}function nh(a){var e=ke(a).autoInterval;return e??(ke(a).autoInterval=a.calculateCategoryInterval())}function oh(a){var e=sh(a),t=fe(a),r=(e.axisRotate-e.labelRotate)/180*Math.PI,i=a.scale,n=i.getExtent(),o=i.count();if(n[1]-n[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=n[0],u=a.dataToCoord(l+1)-a.dataToCoord(l),h=Math.abs(u*Math.cos(r)),v=Math.abs(u*Math.sin(r)),f=0,c=0;l<=n[1];l+=s){var d=0,g=0,m=An(t({value:l}),e.font,"center","top");d=m.width*1.3,g=m.height*1.3,f=Math.max(f,d,7),c=Math.max(c,g,7)}var p=f/h,y=c/v;isNaN(p)&&(p=1/0),isNaN(y)&&(y=1/0);var _=Math.max(0,Math.floor(Math.min(p,y))),x=ke(a.model),w=a.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return b!=null&&S!=null&&Math.abs(b-_)<=1&&Math.abs(S-o)<=1&&b>_&&x.axisExtent0===w[0]&&x.axisExtent1===w[1]?_=b:(x.lastTickCount=o,x.lastAutoInterval=_,x.axisExtent0=w[0],x.axisExtent1=w[1]),_}function sh(a){var e=a.getLabelModel();return{axisRotate:a.getRotate?a.getRotate():a.isHorizontal&&!a.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function uo(a,e,t){var r=fe(a),i=a.scale,n=i.getExtent(),o=a.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=n[0],h=i.count();u!==0&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var v=ao(a),f=o.get("showMinLabel")||v,c=o.get("showMaxLabel")||v;f&&u!==n[0]&&g(n[0]);for(var d=u;d<=n[1];d+=l)g(d);c&&d-l!==n[1]&&g(n[1]);function g(m){var p={value:m};s.push(t?m:{formattedLabel:r(p),rawLabel:i.getLabel(p),tickValue:m})}return s}function ho(a,e,t){var r=a.scale,i=fe(a),n=[];return M(r.getTicks(),function(o){var s=r.getLabel(o),l=o.value;e(o.value,s)&&n.push(t?l:{formattedLabel:i(o),rawLabel:s,tickValue:l})}),n}var ui=[0,1],lh=function(){function a(e,t,r){this.onBand=!1,this.inverse=!1,this.dim=e,this.scale=t,this._extent=r||[0,0]}return a.prototype.contain=function(e){var t=this._extent,r=Math.min(t[0],t[1]),i=Math.max(t[0],t[1]);return e>=r&&e<=i},a.prototype.containData=function(e){return this.scale.contain(e)},a.prototype.getExtent=function(){return this._extent.slice()},a.prototype.getPixelPrecision=function(e){return zs(e||this.scale.getExtent(),this._extent)},a.prototype.setExtent=function(e,t){var r=this._extent;r[0]=e,r[1]=t},a.prototype.dataToCoord=function(e,t){var r=this._extent,i=this.scale;return e=i.normalize(e),this.onBand&&i.type==="ordinal"&&(r=r.slice(),hi(r,i.count())),Vr(e,ui,r,t)},a.prototype.coordToData=function(e,t){var r=this._extent,i=this.scale;this.onBand&&i.type==="ordinal"&&(r=r.slice(),hi(r,i.count()));var n=Vr(e,r,ui,t);return this.scale.scale(n)},a.prototype.pointToData=function(e,t){},a.prototype.getTicksCoords=function(e){e=e||{};var t=e.tickModel||this.getTickModel(),r=eh(this,t),i=r.ticks,n=Y(i,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=t.get("alignWithLabel");return uh(this,n,o,e.clamp),n},a.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var e=this.model.getModel("minorTick"),t=e.get("splitNumber");t>0&&t<100||(t=5);var r=this.scale.getMinorTicks(t),i=Y(r,function(n){return Y(n,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return i},a.prototype.getViewLabels=function(){return th(this).labels},a.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},a.prototype.getTickModel=function(){return this.model.getModel("axisTick")},a.prototype.getBandWidth=function(){var e=this._extent,t=this.scale.getExtent(),r=t[1]-t[0]+(this.onBand?1:0);r===0&&(r=1);var i=Math.abs(e[1]-e[0]);return Math.abs(i)/r},a.prototype.calculateCategoryInterval=function(){return oh(this)},a}();function hi(a,e){var t=a[1]-a[0],r=e,i=t/r/2;a[0]+=i,a[1]-=i}function uh(a,e,t,r){var i=e.length;if(!a.onBand||t||!i)return;var n=a.getExtent(),o,s;if(i===1)e[0].coord=n[0],o=e[1]={coord:n[1],tickValue:e[0].tickValue};else{var l=e[i-1].tickValue-e[0].tickValue,u=(e[i-1].coord-e[0].coord)/l;M(e,function(c){c.coord-=u/2});var h=a.scale.getExtent();s=1+h[1]-e[i-1].tickValue,o={coord:e[i-1].coord+u*s,tickValue:h[1]+1},e.push(o)}var v=n[0]>n[1];f(e[0].coord,n[0])&&(r?e[0].coord=n[0]:e.shift()),r&&f(n[0],e[0].coord)&&e.unshift({coord:n[0]}),f(n[1],o.coord)&&(r?o.coord=n[1]:e.pop()),r&&f(o.coord,n[1])&&e.push({coord:n[1]});function f(c,d){return c=at(c),d=at(d),v?c>d:c<d}}function vo(a,e,t,r,i,n,o,s){var l=i-a,u=n-e,h=t-a,v=r-e,f=Math.sqrt(h*h+v*v);h/=f,v/=f;var c=l*h+u*v,d=c/f;d*=f;var g=o[0]=a+d*h,m=o[1]=e+d*v;return Math.sqrt((g-i)*(g-i)+(m-n)*(m-n))}var Gt=new rt,K=new rt,et=new rt,zt=new rt,xt=new rt,nr=[],ot=new rt;function hh(a,e){if(e<=180&&e>0){e=e/180*Math.PI,Gt.fromArray(a[0]),K.fromArray(a[1]),et.fromArray(a[2]),rt.sub(zt,Gt,K),rt.sub(xt,et,K);var t=zt.len(),r=xt.len();if(!(t<.001||r<.001)){zt.scale(1/t),xt.scale(1/r);var i=zt.dot(xt),n=Math.cos(e);if(n<i){var o=vo(K.x,K.y,et.x,et.y,Gt.x,Gt.y,nr);ot.fromArray(nr),ot.scaleAndAdd(xt,o/Math.tan(Math.PI-e));var s=et.x!==K.x?(ot.x-K.x)/(et.x-K.x):(ot.y-K.y)/(et.y-K.y);if(isNaN(s))return;s<0?rt.copy(ot,K):s>1&&rt.copy(ot,et),ot.toArray(a[1])}}}}function vh(a,e,t){if(t<=180&&t>0){t=t/180*Math.PI,Gt.fromArray(a[0]),K.fromArray(a[1]),et.fromArray(a[2]),rt.sub(zt,K,Gt),rt.sub(xt,et,K);var r=zt.len(),i=xt.len();if(!(r<.001||i<.001)){zt.scale(1/r),xt.scale(1/i);var n=zt.dot(e),o=Math.cos(t);if(n<o){var s=vo(K.x,K.y,et.x,et.y,Gt.x,Gt.y,nr);ot.fromArray(nr);var l=Math.PI/2,u=Math.acos(xt.dot(e)),h=l+u-t;if(h>=l)rt.copy(ot,et);else{ot.scaleAndAdd(xt,s/Math.tan(Math.PI/2-h));var v=et.x!==K.x?(ot.x-K.x)/(et.x-K.x):(ot.y-K.y)/(et.y-K.y);if(isNaN(v))return;v<0?rt.copy(ot,K):v>1&&rt.copy(ot,et)}ot.toArray(a[1])}}}}function wr(a,e,t,r){var i=t==="normal",n=i?a:a.ensureState(t);n.ignore=e;var o=r.get("smooth");o&&o===!0&&(o=.3),n.shape=n.shape||{},o>0&&(n.shape.smooth=o);var s=r.getModel("lineStyle").getLineStyle();i?a.useStyle(s):n.style=s}function ch(a,e){var t=e.smooth,r=e.points;if(r)if(a.moveTo(r[0][0],r[0][1]),t>0&&r.length>=3){var i=Ea(r[0],r[1]),n=Ea(r[1],r[2]);if(!i||!n){a.lineTo(r[1][0],r[1][1]),a.lineTo(r[2][0],r[2][1]);return}var o=Math.min(i,n)*t,s=gr([],r[1],r[0],o/i),l=gr([],r[1],r[2],o/n),u=gr([],s,l,.5);a.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),a.bezierCurveTo(l[0],l[1],l[0],l[1],r[2][0],r[2][1])}else for(var h=1;h<r.length;h++)a.lineTo(r[h][0],r[h][1])}function fh(a,e,t){var r=a.getTextGuideLine(),i=a.getTextContent();if(!i){r&&a.removeTextGuideLine();return}for(var n=e.normal,o=n.get("show"),s=i.ignore,l=0;l<Oa.length;l++){var u=Oa[l],h=e[u],v=u==="normal";if(h){var f=h.get("show"),c=v?s:bt(i.states[u]&&i.states[u].ignore,s);if(c||!bt(f,o)){var d=v?r:r&&r.states[u];d&&(d.ignore=!0),r&&wr(r,!0,u,h);continue}r||(r=new Cn,a.setTextGuideLine(r),!v&&(s||!o)&&wr(r,!0,"normal",e.normal),a.stateProxy&&(r.stateProxy=a.stateProxy)),wr(r,!1,u,h)}}if(r){lt(r.style,t),r.style.fill=null;var g=n.get("showAbove"),m=a.textGuideLineConfig=a.textGuideLineConfig||{};m.showAbove=g||!1,r.buildPath=ch}}function dh(a,e){e=e||"labelLine";for(var t={normal:a.getModel(e)},r=0;r<tr.length;r++){var i=tr[r];t[i]=a.getModel([i,e])}return t}function gh(a){for(var e=[],t=0;t<a.length;t++){var r=a[t];if(!r.defaultAttr.ignore){var i=r.label,n=i.getComputedTransform(),o=i.getBoundingRect(),s=!n||n[1]<1e-5&&n[2]<1e-5,l=i.style.margin||0,u=o.clone();u.applyTransform(n),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var h=s?new Fr(o,n):null;e.push({label:i,labelLine:r.labelLine,rect:u,localRect:o,obb:h,priority:r.priority,defaultAttr:r.defaultAttr,layoutOption:r.computedLayoutOption,axisAligned:s,transform:n})}}return e}function ph(a,e,t,r,i,n){var o=a.length;if(o<2)return;a.sort(function(b,S){return b.rect[e]-S.rect[e]});for(var s=0,l,u=!1,h=0;h<o;h++){var v=a[h],f=v.rect;l=f[e]-s,l<0&&(f[e]-=l,v.label[e]-=l,u=!0),s=f[e]+f[t]}var c=a[0],d=a[o-1],g,m;p(),g<0&&x(-g,.8),m<0&&x(m,.8),p(),y(g,m,1),y(m,g,-1),p(),g<0&&w(-g),m<0&&w(m);function p(){g=c.rect[e]-r,m=i-d.rect[e]-d.rect[t]}function y(b,S,A){if(b<0){var L=Math.min(S,-b);if(L>0){_(L*A,0,o);var D=L+b;D<0&&x(-D*A,1)}else x(-b*A,1)}}function _(b,S,A){b!==0&&(u=!0);for(var L=S;L<A;L++){var D=a[L],C=D.rect;C[e]+=b,D.label[e]+=b}}function x(b,S){for(var A=[],L=0,D=1;D<o;D++){var C=a[D-1].rect,I=Math.max(a[D].rect[e]-C[e]-C[t],0);A.push(I),L+=I}if(L){var T=Math.min(Math.abs(b)/L,S);if(b>0)for(var D=0;D<o-1;D++){var k=A[D]*T;_(k,0,D+1)}else for(var D=o-1;D>0;D--){var k=A[D-1]*T;_(-k,D,o)}}}function w(b){var S=b<0?-1:1;b=Math.abs(b);for(var A=Math.ceil(b/(o-1)),L=0;L<o-1;L++)if(S>0?_(A,0,L+1):_(-A,o-L-1,o),b-=A,b<=0)return}return u}function mh(a,e,t,r){return ph(a,"y","height",e,t)}function yh(a){var e=[];a.sort(function(g,m){return m.priority-g.priority});var t=new Bt(0,0,0,0);function r(g){if(!g.ignore){var m=g.ensureState("emphasis");m.ignore==null&&(m.ignore=!1)}g.ignore=!0}for(var i=0;i<a.length;i++){var n=a[i],o=n.axisAligned,s=n.localRect,l=n.transform,u=n.label,h=n.labelLine;t.copy(n.rect),t.width-=.1,t.height-=.1,t.x+=.05,t.y+=.05;for(var v=n.obb,f=!1,c=0;c<e.length;c++){var d=e[c];if(t.intersect(d.rect)){if(o&&d.axisAligned){f=!0;break}if(d.obb||(d.obb=new Fr(d.localRect,d.transform)),v||(v=new Fr(s,l)),v.intersect(d.obb)){f=!0;break}}}f?(r(u),h&&r(h)):(u.attr("ignore",n.defaultAttr.ignore),h&&h.attr("ignore",n.defaultAttr.labelGuideIgnore),e.push(n))}}function vi(a,e,t){var r=Fs.createCanvas(),i=e.getWidth(),n=e.getHeight(),o=r.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=i+"px",o.height=n+"px",r.setAttribute("data-zr-dom-id",a)),r.width=i*t,r.height=n*t,r}var Ar=function(a){G(e,a);function e(t,r,i){var n=a.call(this)||this;n.motionBlur=!1,n.lastFrameAlpha=.7,n.dpr=1,n.virtual=!1,n.config={},n.incremental=!1,n.zlevel=0,n.maxRepaintRectCount=5,n.__dirty=!0,n.__firstTimePaint=!0,n.__used=!1,n.__drawIndex=0,n.__startIndex=0,n.__endIndex=0,n.__prevStartIndex=null,n.__prevEndIndex=null;var o;i=i||Ln,typeof t=="string"?o=vi(t,r,i):st(t)&&(o=t,t=o.id),n.id=t,n.dom=o;var s=o.style;return s&&(Dn(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),n.painter=r,n.dpr=i,n}return e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=vi("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),t!==1&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,r,i,n){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,l=!1,u=new Bt(0,0,0,0);function h(y){if(!(!y.isFinite()||y.isZero()))if(o.length===0){var _=new Bt(0,0,0,0);_.copy(y),o.push(_)}else{for(var x=!1,w=1/0,b=0,S=0;S<o.length;++S){var A=o[S];if(A.intersect(y)){var L=new Bt(0,0,0,0);L.copy(A),L.union(y),o[S]=L,x=!0;break}else if(l){u.copy(y),u.union(A);var D=y.width*y.height,C=A.width*A.height,I=u.width*u.height,T=I-D-C;T<w&&(w=T,b=S)}}if(l&&(o[b].union(y),x=!0),!x){var _=new Bt(0,0,0,0);_.copy(y),o.push(_)}l||(l=o.length>=s)}}for(var v=this.__startIndex;v<this.__endIndex;++v){var f=t[v];if(f){var c=f.shouldBePainted(i,n,!0,!0),d=f.__isRendered&&(f.__dirty&Hr||!c)?f.getPrevPaintRect():null;d&&h(d);var g=c&&(f.__dirty&Hr||!f.__isRendered)?f.getPaintRect():null;g&&h(g)}}for(var v=this.__prevStartIndex;v<this.__prevEndIndex;++v){var f=r[v],c=f&&f.shouldBePainted(i,n,!0,!0);if(f&&(!c||!f.__zr)&&f.__isRendered){var d=f.getPrevPaintRect();d&&h(d)}}var m;do{m=!1;for(var v=0;v<o.length;){if(o[v].isZero()){o.splice(v,1);continue}for(var p=v+1;p<o.length;)o[v].intersect(o[p])?(m=!0,o[v].union(o[p]),o.splice(p,1)):p++;v++}}while(m);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,r){var i=this.dpr,n=this.dom,o=n.style,s=this.domBack;o&&(o.width=t+"px",o.height=r+"px"),n.width=t*i,n.height=r*i,s&&(s.width=t*i,s.height=r*i,i!==1&&this.ctxBack.scale(i,i))},e.prototype.clear=function(t,r,i){var n=this.dom,o=this.ctx,s=n.width,l=n.height;r=r||this.clearColor;var u=this.motionBlur&&!t,h=this.lastFrameAlpha,v=this.dpr,f=this;u&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,s/v,l/v));var c=this.domBack;function d(g,m,p,y){if(o.clearRect(g,m,p,y),r&&r!=="transparent"){var _=void 0;if(Hs(r)){var x=r.global||r.__width===p&&r.__height===y;_=x&&r.__canvasGradient||Ws(o,r,{x:0,y:0,width:p,height:y}),r.__canvasGradient=_,r.__width=p,r.__height=y}else Us(r)&&(r.scaleX=r.scaleX||v,r.scaleY=r.scaleY||v,_=Xs(o,r,{dirty:function(){f.setUnpainted(),f.painter.refresh()}}));o.save(),o.fillStyle=_||r,o.fillRect(g,m,p,y),o.restore()}u&&(o.save(),o.globalAlpha=h,o.drawImage(c,g,m,p,y),o.restore())}!i||u?d(0,0,s,l):i.length&&M(i,function(g){d(g.x*v,g.y*v,g.width*v,g.height*v)})},e}(Vs),ci=1e5,Zt=314159,Xe=.01,_h=.001;function xh(a){return a?a.__builtin__?!0:!(typeof a.resize!="function"||typeof a.refresh!="function"):!1}function bh(a,e){var t=document.createElement("div");return t.style.cssText=["position:relative","width:"+a+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",t}var Sh=function(){function a(e,t,r,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var n=!e.nodeName||e.nodeName.toUpperCase()==="CANVAS";this._opts=r=W({},r||{}),this.dpr=r.devicePixelRatio||Ln,this._singleCanvas=n,this.root=e;var o=e.style;o&&(Dn(e),e.innerHTML=""),this.storage=t;var s=this._zlevelList;this._prevDisplayList=[];var l=this._layers;if(n){var h=e,v=h.width,f=h.height;r.width!=null&&(v=r.width),r.height!=null&&(f=r.height),this.dpr=r.devicePixelRatio||1,h.width=v*this.dpr,h.height=f*this.dpr,this._width=v,this._height=f;var c=new Ar(h,this,this.dpr);c.__builtin__=!0,c.initContext(),l[Zt]=c,c.zlevel=Zt,s.push(Zt),this._domRoot=e}else{this._width=ze(e,0,r),this._height=ze(e,1,r);var u=this._domRoot=bh(this._width,this._height);e.appendChild(u)}}return a.prototype.getType=function(){return"canvas"},a.prototype.isSingleCanvas=function(){return this._singleCanvas},a.prototype.getViewportRoot=function(){return this._domRoot},a.prototype.getViewportRootOffset=function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},a.prototype.refresh=function(e){var t=this.storage.getDisplayList(!0),r=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(t,r,e,this._redrawId);for(var n=0;n<i.length;n++){var o=i[n],s=this._layers[o];if(!s.__builtin__&&s.refresh){var l=n===0?this._backgroundColor:null;s.refresh(l)}}return this._opts.useDirtyRect&&(this._prevDisplayList=t.slice()),this},a.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},a.prototype._paintHoverList=function(e){var t=e.length,r=this._hoverlayer;if(r&&r.clear(),!!t){for(var i={inHover:!0,viewWidth:this._width,viewHeight:this._height},n,o=0;o<t;o++){var s=e[o];s.__inHover&&(r||(r=this._hoverlayer=this.getLayer(ci)),n||(n=r.ctx,n.save()),Ge(n,s,i,o===t-1))}n&&n.restore()}},a.prototype.getHoverLayer=function(){return this.getLayer(ci)},a.prototype.paintOne=function(e,t){Ys(e,t)},a.prototype._paintList=function(e,t,r,i){if(this._redrawId===i){r=r||!1,this._updateLayerStatus(e);var n=this._doPaintList(e,t,r),o=n.finished,s=n.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(e),o)this.eachLayer(function(u){u.afterBrush&&u.afterBrush()});else{var l=this;Zs(function(){l._paintList(e,t,r,i)})}}},a.prototype._compositeManually=function(){var e=this.getLayer(Zt).ctx,t=this._domRoot.width,r=this._domRoot.height;e.clearRect(0,0,t,r),this.eachBuiltinLayer(function(i){i.virtual&&e.drawImage(i.dom,0,0,t,r)})},a.prototype._doPaintList=function(e,t,r){for(var i=this,n=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||r)&&n.push(u)}for(var h=!0,v=!1,f=function(g){var m=n[g],p=m.ctx,y=o&&m.createRepaintRects(e,t,c._width,c._height),_=r?m.__startIndex:m.__drawIndex,x=!r&&m.incremental&&Date.now,w=x&&Date.now(),b=m.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(m.__startIndex===m.__endIndex)m.clear(!1,b,y);else if(_===m.__startIndex){var S=e[_];(!S.incremental||!S.notClear||r)&&m.clear(!1,b,y)}_===-1&&(console.error("For some unknown reason. drawIndex is -1"),_=m.__startIndex);var A,L=function(T){var k={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(A=_;A<m.__endIndex;A++){var P=e[A];if(P.__inHover&&(v=!0),i._doPaintEl(P,m,o,T,k,A===m.__endIndex-1),x){var N=Date.now()-w;if(N>15)break}}k.prevElClipPaths&&p.restore()};if(y)if(y.length===0)A=m.__endIndex;else for(var D=c.dpr,C=0;C<y.length;++C){var I=y[C];p.save(),p.beginPath(),p.rect(I.x*D,I.y*D,I.width*D,I.height*D),p.clip(),L(I),p.restore()}else p.save(),L(),p.restore();m.__drawIndex=A,m.__drawIndex<m.__endIndex&&(h=!1)},c=this,d=0;d<n.length;d++)f(d);return ut.wxa&&M(this._layers,function(g){g&&g.ctx&&g.ctx.draw&&g.ctx.draw()}),{finished:h,needsRefreshHover:v}},a.prototype._doPaintEl=function(e,t,r,i,n,o){var s=t.ctx;if(r){var l=e.getPaintRect();(!i||l&&l.intersect(i))&&(Ge(s,e,n,o),e.setPrevPaintRect(l))}else Ge(s,e,n,o)},a.prototype.getLayer=function(e,t){this._singleCanvas&&!this._needsManuallyCompositing&&(e=Zt);var r=this._layers[e];return r||(r=new Ar("zr_"+e,this,this.dpr),r.zlevel=e,r.__builtin__=!0,this._layerConfig[e]?ct(r,this._layerConfig[e],!0):this._layerConfig[e-Xe]&&ct(r,this._layerConfig[e-Xe],!0),t&&(r.virtual=t),this.insertLayer(e,r),r.initContext()),r},a.prototype.insertLayer=function(e,t){var r=this._layers,i=this._zlevelList,n=i.length,o=this._domRoot,s=null,l=-1;if(!r[e]&&xh(t)){if(n>0&&e>i[0]){for(l=0;l<n-1&&!(i[l]<e&&i[l+1]>e);l++);s=r[i[l]]}if(i.splice(l+1,0,e),r[e]=t,!t.virtual)if(s){var u=s.dom;u.nextSibling?o.insertBefore(t.dom,u.nextSibling):o.appendChild(t.dom)}else o.firstChild?o.insertBefore(t.dom,o.firstChild):o.appendChild(t.dom);t.painter||(t.painter=this)}},a.prototype.eachLayer=function(e,t){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i];e.call(t,this._layers[n],n)}},a.prototype.eachBuiltinLayer=function(e,t){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__&&e.call(t,o,n)}},a.prototype.eachOtherLayer=function(e,t){for(var r=this._zlevelList,i=0;i<r.length;i++){var n=r[i],o=this._layers[n];o.__builtin__||e.call(t,o,n)}},a.prototype.getLayers=function(){return this._layers},a.prototype._updateLayerStatus=function(e){this.eachBuiltinLayer(function(v,f){v.__dirty=v.__used=!1});function t(v){n&&(n.__endIndex!==v&&(n.__dirty=!0),n.__endIndex=v)}if(this._singleCanvas)for(var r=1;r<e.length;r++){var i=e[r];if(i.zlevel!==e[r-1].zlevel||i.incremental){this._needsManuallyCompositing=!0;break}}var n=null,o=0,s,l;for(l=0;l<e.length;l++){var i=e[l],u=i.zlevel,h=void 0;s!==u&&(s=u,o=0),i.incremental?(h=this.getLayer(u+_h,this._needsManuallyCompositing),h.incremental=!0,o=1):h=this.getLayer(u+(o>0?Xe:0),this._needsManuallyCompositing),h.__builtin__||$s("ZLevel "+u+" has been used by unkown layer "+h.id),h!==n&&(h.__used=!0,h.__startIndex!==l&&(h.__dirty=!0),h.__startIndex=l,h.incremental?h.__drawIndex=-1:h.__drawIndex=l,t(l),n=h),i.__dirty&Hr&&!i.__inHover&&(h.__dirty=!0,h.incremental&&h.__drawIndex<0&&(h.__drawIndex=l))}t(l),this.eachBuiltinLayer(function(v,f){!v.__used&&v.getElementCount()>0&&(v.__dirty=!0,v.__startIndex=v.__endIndex=v.__drawIndex=0),v.__dirty&&v.__drawIndex<0&&(v.__drawIndex=v.__startIndex)})},a.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},a.prototype._clearLayer=function(e){e.clear()},a.prototype.setBackgroundColor=function(e){this._backgroundColor=e,M(this._layers,function(t){t.setUnpainted()})},a.prototype.configLayer=function(e,t){if(t){var r=this._layerConfig;r[e]?ct(r[e],t,!0):r[e]=t;for(var i=0;i<this._zlevelList.length;i++){var n=this._zlevelList[i];if(n===e||n===e+Xe){var o=this._layers[n];ct(o,r[e],!0)}}}},a.prototype.delLayer=function(e){var t=this._layers,r=this._zlevelList,i=t[e];i&&(i.dom.parentNode.removeChild(i.dom),delete t[e],r.splice(Ft(r,e),1))},a.prototype.resize=function(e,t){if(this._domRoot.style){var r=this._domRoot;r.style.display="none";var i=this._opts,n=this.root;if(e!=null&&(i.width=e),t!=null&&(i.height=t),e=ze(n,0,i),t=ze(n,1,i),r.style.display="",this._width!==e||t!==this._height){r.style.width=e+"px",r.style.height=t+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(e,t);this.refresh(!0)}this._width=e,this._height=t}else{if(e==null||t==null)return;this._width=e,this._height=t,this.getLayer(Zt).resize(e,t)}return this},a.prototype.clearLayer=function(e){var t=this._layers[e];t&&t.clear()},a.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},a.prototype.getRenderedCanvas=function(e){if(e=e||{},this._singleCanvas&&!this._compositeManually)return this._layers[Zt].dom;var t=new Ar("image",this,e.pixelRatio||this.dpr);t.initContext(),t.clear(!1,e.backgroundColor||this._backgroundColor);var r=t.ctx;if(e.pixelRatio<=this.dpr){this.refresh();var i=t.dom.width,n=t.dom.height;this.eachLayer(function(v){v.__builtin__?r.drawImage(v.dom,0,0,i,n):v.renderToCanvas&&(r.save(),v.renderToCanvas(r),r.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),l=0,u=s.length;l<u;l++){var h=s[l];Ge(r,h,o,l===u-1)}return t.dom},a.prototype.getWidth=function(){return this._width},a.prototype.getHeight=function(){return this._height},a}();function wh(a){a.registerPainter("canvas",Sh)}var Ah=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t){return va(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var r=new yt,i=Ie("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);r.add(i),i.setStyle(t.lineStyle);var n=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=n==="none"?"circle":n,l=t.itemHeight*.8,u=Ie(s,(t.itemWidth-l)/2,(t.itemHeight-l)/2,l,l,t.itemStyle.fill);r.add(u),u.setStyle(t.itemStyle);var h=t.iconRotate==="inherit"?o:t.iconRotate||0;return u.rotation=h*Math.PI/180,u.setOrigin([t.itemWidth/2,t.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),r},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(oe);function ma(a,e){var t=a.mapDimensionsAll("defaultedLabel"),r=t.length;if(r===1){var i=Ra(a,e,t[0]);return i!=null?i+"":null}else if(r){for(var n=[],o=0;o<t.length;o++)n.push(Ra(a,e,t[o]));return n.join(" ")}}function co(a,e){var t=a.mapDimensionsAll("defaultedLabel");if(!X(e))return e+"";for(var r=[],i=0;i<t.length;i++){var n=a.getDimensionIndex(t[i]);n>=0&&r.push(e[n])}return r.join(" ")}var ya=function(a){G(e,a);function e(t,r,i,n){var o=a.call(this)||this;return o.updateData(t,r,i,n),o}return e.prototype._createSymbol=function(t,r,i,n,o){this.removeAll();var s=Ie(t,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:n[0]/2,scaleY:n[1]/2}),s.drift=Ch,this._symbolType=t,this.add(s)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){qs(this.childAt(0))},e.prototype.downplay=function(){Ks(this.childAt(0))},e.prototype.setZ=function(t,r){var i=this.childAt(0);i.zlevel=t,i.z=r},e.prototype.setDraggable=function(t,r){var i=this.childAt(0);i.draggable=t,i.cursor=!r&&t?"move":i.cursor},e.prototype.updateData=function(t,r,i,n){this.silent=!1;var o=t.getItemVisual(r,"symbol")||"circle",s=t.hostModel,l=e.getSymbolSize(t,r),u=o!==this._symbolType,h=n&&n.disableAnimation;if(u){var v=t.getItemVisual(r,"symbolKeepAspect");this._createSymbol(o,t,r,l,v)}else{var f=this.childAt(0);f.silent=!1;var c={scaleX:l[0]/2,scaleY:l[1]/2};h?f.attr(c):ht(f,c,s,r),la(f)}if(this._updateCommon(t,r,l,i,n),u){var f=this.childAt(0);if(!h){var c={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:f.style.opacity}};f.scaleX=f.scaleY=0,f.style.opacity=0,mt(f,c,s,r)}}h&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,r,i,n,o){var s=this.childAt(0),l=t.hostModel,u,h,v,f,c,d,g,m,p;if(n&&(u=n.emphasisItemStyle,h=n.blurItemStyle,v=n.selectItemStyle,f=n.focus,c=n.blurScope,g=n.labelStatesModels,m=n.hoverScale,p=n.cursorStyle,d=n.emphasisDisabled),!n||t.hasItemOption){var y=n&&n.itemModel?n.itemModel:t.getItemModel(r),_=y.getModel("emphasis");u=_.getModel("itemStyle").getItemStyle(),v=y.getModel(["select","itemStyle"]).getItemStyle(),h=y.getModel(["blur","itemStyle"]).getItemStyle(),f=_.get("focus"),c=_.get("blurScope"),d=_.get("disabled"),g=Ee(y),m=_.getShallow("scale"),p=y.getShallow("cursor")}var x=t.getItemVisual(r,"symbolRotate");s.attr("rotation",(x||0)*Math.PI/180||0);var w=Tn(t.getItemVisual(r,"symbolOffset"),i);w&&(s.x=w[0],s.y=w[1]),p&&s.attr("cursor",p);var b=t.getItemVisual(r,"style"),S=b.fill;if(s instanceof js){var A=s.style;s.useStyle(W({image:A.image,x:A.x,y:A.y,width:A.width,height:A.height},b))}else s.__isEmptyBrush?s.useStyle(W({},b)):s.useStyle(b),s.style.decal=null,s.setColor(S,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var L=t.getItemVisual(r,"liftZ"),D=this._z2;L!=null?D==null&&(this._z2=s.z2,s.z2+=L):D!=null&&(s.z2=D,this._z2=null);var C=o&&o.useNameLabel;Re(s,g,{labelFetcher:l,labelDataIndex:r,defaultText:I,inheritColor:S,defaultOpacity:b.opacity});function I(P){return C?t.getName(P):ma(t,P)}this._sizeX=i[0]/2,this._sizeY=i[1]/2;var T=s.ensureState("emphasis");T.style=u,s.ensureState("select").style=v,s.ensureState("blur").style=h;var k=m==null||m===!0?Math.max(1.1,3/this._sizeY):isFinite(m)&&m>0?+m:1;T.scaleX=this._sizeX*k,T.scaleY=this._sizeY*k,this.setSymbolScale(1),Me(this,f,c,d)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,r,i){var n=this.childAt(0),o=j(this).dataIndex,s=i&&i.animation;if(this.silent=n.silent=!0,i&&i.fadeLabel){var l=n.getTextContent();l&&Na(l,{style:{opacity:0}},r,{dataIndex:o,removeOpt:s,cb:function(){n.removeTextContent()}})}else n.removeTextContent();Na(n,{style:{opacity:0},scaleX:0,scaleY:0},r,{dataIndex:o,cb:t,removeOpt:s})},e.getSymbolSize=function(t,r){return Js(t.getItemVisual(r,"symbolSize"))},e}(yt);function Ch(a,e){this.parent.drift(a,e)}function Cr(a,e,t,r){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(r.isIgnore&&r.isIgnore(t))&&!(r.clipShape&&!r.clipShape.contain(e[0],e[1]))&&a.getItemVisual(t,"symbol")!=="none"}function fi(a){return a!=null&&!st(a)&&(a={isIgnore:a}),a||{}}function di(a){var e=a.hostModel,t=e.getModel("emphasis");return{emphasisItemStyle:t.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:t.get("focus"),blurScope:t.get("blurScope"),emphasisDisabled:t.get("disabled"),hoverScale:t.get("scale"),labelStatesModels:Ee(e),cursorStyle:e.get("cursor")}}var Dh=function(){function a(e){this.group=new yt,this._SymbolCtor=e||ya}return a.prototype.updateData=function(e,t){this._progressiveEls=null,t=fi(t);var r=this.group,i=e.hostModel,n=this._data,o=this._SymbolCtor,s=t.disableAnimation,l=di(e),u={disableAnimation:s},h=t.getSymbolPoint||function(v){return e.getItemLayout(v)};n||r.removeAll(),e.diff(n).add(function(v){var f=h(v);if(Cr(e,f,v,t)){var c=new o(e,v,l,u);c.setPosition(f),e.setItemGraphicEl(v,c),r.add(c)}}).update(function(v,f){var c=n.getItemGraphicEl(f),d=h(v);if(!Cr(e,d,v,t)){r.remove(c);return}var g=e.getItemVisual(v,"symbol")||"circle",m=c&&c.getSymbolType&&c.getSymbolType();if(!c||m&&m!==g)r.remove(c),c=new o(e,v,l,u),c.setPosition(d);else{c.updateData(e,v,l,u);var p={x:d[0],y:d[1]};s?c.attr(p):ht(c,p,i)}r.add(c),e.setItemGraphicEl(v,c)}).remove(function(v){var f=n.getItemGraphicEl(v);f&&f.fadeOut(function(){r.remove(f)},i)}).execute(),this._getSymbolPoint=h,this._data=e},a.prototype.updateLayout=function(){var e=this,t=this._data;t&&t.eachItemGraphicEl(function(r,i){var n=e._getSymbolPoint(i);r.setPosition(n),r.markRedraw()})},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=di(e),this._data=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t,r){this._progressiveEls=[],r=fi(r);function i(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var n=e.start;n<e.end;n++){var o=t.getItemLayout(n);if(Cr(t,o,n,r)){var s=new this._SymbolCtor(t,n,this._seriesScope);s.traverse(i),s.setPosition(o),this.group.add(s),t.setItemGraphicEl(n,s),this._progressiveEls.push(s)}}},a.prototype.eachRendered=function(e){In(this._progressiveEls||this.group,e)},a.prototype.remove=function(e){var t=this.group,r=this._data;r&&e?r.eachItemGraphicEl(function(i){i.fadeOut(function(){t.remove(i)},r.hostModel)}):t.removeAll()},a}();function fo(a,e,t){var r=a.getBaseAxis(),i=a.getOtherAxis(r),n=Lh(i,t),o=r.dim,s=i.dim,l=e.mapDimension(s),u=e.mapDimension(o),h=s==="x"||s==="radius"?1:0,v=Y(a.dimensions,function(d){return e.mapDimension(d)}),f=!1,c=e.getCalculationInfo("stackResultDimension");return ve(e,v[0])&&(f=!0,v[0]=c),ve(e,v[1])&&(f=!0,v[1]=c),{dataDimsForPoint:v,valueStart:n,valueAxisDim:s,baseAxisDim:o,stacked:!!f,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Lh(a,e){var t=0,r=a.scale.getExtent();return e==="start"?t=r[0]:e==="end"?t=r[1]:Tt(e)&&!isNaN(e)?t=e:r[0]>0?t=r[0]:r[1]<0&&(t=r[1]),t}function go(a,e,t,r){var i=NaN;a.stacked&&(i=t.get(t.getCalculationInfo("stackedOverDimension"),r)),isNaN(i)&&(i=a.valueStart);var n=a.baseDataOffset,o=[];return o[n]=t.get(a.baseDim,r),o[1-n]=i,e.dataToPoint(o)}function Th(a,e){var t=[];return e.diff(a).add(function(r){t.push({cmd:"+",idx:r})}).update(function(r,i){t.push({cmd:"=",idx:i,idx1:r})}).remove(function(r){t.push({cmd:"-",idx:r})}).execute(),t}function Ih(a,e,t,r,i,n,o,s){for(var l=Th(a,e),u=[],h=[],v=[],f=[],c=[],d=[],g=[],m=fo(i,e,o),p=a.getLayout("points")||[],y=e.getLayout("points")||[],_=0;_<l.length;_++){var x=l[_],w=!0,b=void 0,S=void 0;switch(x.cmd){case"=":b=x.idx*2,S=x.idx1*2;var A=p[b],L=p[b+1],D=y[S],C=y[S+1];(isNaN(A)||isNaN(L))&&(A=D,L=C),u.push(A,L),h.push(D,C),v.push(t[b],t[b+1]),f.push(r[S],r[S+1]),g.push(e.getRawIndex(x.idx1));break;case"+":var I=x.idx,T=m.dataDimsForPoint,k=i.dataToPoint([e.get(T[0],I),e.get(T[1],I)]);S=I*2,u.push(k[0],k[1]),h.push(y[S],y[S+1]);var P=go(m,i,e,I);v.push(P[0],P[1]),f.push(r[S],r[S+1]),g.push(e.getRawIndex(I));break;case"-":w=!1}w&&(c.push(x),d.push(d.length))}d.sort(function(J,jt){return g[J]-g[jt]});for(var N=u.length,z=Ct(N),O=Ct(N),E=Ct(N),R=Ct(N),B=[],_=0;_<d.length;_++){var F=d[_],$=_*2,tt=F*2;z[$]=u[tt],z[$+1]=u[tt+1],O[$]=h[tt],O[$+1]=h[tt+1],E[$]=v[tt],E[$+1]=v[tt+1],R[$]=f[tt],R[$+1]=f[tt+1],B[_]=c[F]}return{current:z,next:O,stackedOnCurrent:E,stackedOnNext:R,status:B}}var Pt=Math.min,kt=Math.max;function Kt(a,e){return isNaN(a)||isNaN(e)}function $r(a,e,t,r,i,n,o,s,l){for(var u,h,v,f,c,d,g=t,m=0;m<r;m++){var p=e[g*2],y=e[g*2+1];if(g>=i||g<0)break;if(Kt(p,y)){if(l){g+=n;continue}break}if(g===t)a[n>0?"moveTo":"lineTo"](p,y),v=p,f=y;else{var _=p-u,x=y-h;if(_*_+x*x<.5){g+=n;continue}if(o>0){for(var w=g+n,b=e[w*2],S=e[w*2+1];b===p&&S===y&&m<r;)m++,w+=n,g+=n,b=e[w*2],S=e[w*2+1],p=e[g*2],y=e[g*2+1],_=p-u,x=y-h;var A=m+1;if(l)for(;Kt(b,S)&&A<r;)A++,w+=n,b=e[w*2],S=e[w*2+1];var L=.5,D=0,C=0,I=void 0,T=void 0;if(A>=r||Kt(b,S))c=p,d=y;else{D=b-u,C=S-h;var k=p-u,P=b-p,N=y-h,z=S-y,O=void 0,E=void 0;if(s==="x"){O=Math.abs(k),E=Math.abs(P);var R=D>0?1:-1;c=p-R*O*o,d=y,I=p+R*E*o,T=y}else if(s==="y"){O=Math.abs(N),E=Math.abs(z);var B=C>0?1:-1;c=p,d=y-B*O*o,I=p,T=y+B*E*o}else O=Math.sqrt(k*k+N*N),E=Math.sqrt(P*P+z*z),L=E/(E+O),c=p-D*o*(1-L),d=y-C*o*(1-L),I=p+D*o*L,T=y+C*o*L,I=Pt(I,kt(b,p)),T=Pt(T,kt(S,y)),I=kt(I,Pt(b,p)),T=kt(T,Pt(S,y)),D=I-p,C=T-y,c=p-D*O/E,d=y-C*O/E,c=Pt(c,kt(u,p)),d=Pt(d,kt(h,y)),c=kt(c,Pt(u,p)),d=kt(d,Pt(h,y)),D=p-c,C=y-d,I=p+D*E/O,T=y+C*E/O}a.bezierCurveTo(v,f,c,d,p,y),v=I,f=T}else a.lineTo(p,y)}u=p,h=y,g+=n}return m}var po=function(){function a(){this.smooth=0,this.smoothConstraint=!0}return a}(),Mh=function(a){G(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-polyline",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new po},e.prototype.buildPath=function(t,r){var i=r.points,n=0,o=i.length/2;if(r.connectNulls){for(;o>0&&Kt(i[o*2-2],i[o*2-1]);o--);for(;n<o&&Kt(i[n*2],i[n*2+1]);n++);}for(;n<o;)n+=$r(t,i,n,o,o,1,r.smooth,r.smoothMonotone,r.connectNulls)+1},e.prototype.getPointOn=function(t,r){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var i=this.path,n=i.data,o=Qs.CMD,s,l,u=r==="x",h=[],v=0;v<n.length;){var f=n[v++],c=void 0,d=void 0,g=void 0,m=void 0,p=void 0,y=void 0,_=void 0;switch(f){case o.M:s=n[v++],l=n[v++];break;case o.L:if(c=n[v++],d=n[v++],_=u?(t-s)/(c-s):(t-l)/(d-l),_<=1&&_>=0){var x=u?(d-l)*_+l:(c-s)*_+s;return u?[t,x]:[x,t]}s=c,l=d;break;case o.C:c=n[v++],d=n[v++],g=n[v++],m=n[v++],p=n[v++],y=n[v++];var w=u?Ba(s,c,g,p,t,h):Ba(l,d,m,y,t,h);if(w>0)for(var b=0;b<w;b++){var S=h[b];if(S<=1&&S>=0){var x=u?Ga(l,d,m,y,S):Ga(s,c,g,p,S);return u?[t,x]:[x,t]}}s=p,l=y;break}}},e}(sr),Ph=function(a){G(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e}(po),kh=function(a){G(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-polygon",r}return e.prototype.getDefaultShape=function(){return new Ph},e.prototype.buildPath=function(t,r){var i=r.points,n=r.stackedOnPoints,o=0,s=i.length/2,l=r.smoothMonotone;if(r.connectNulls){for(;s>0&&Kt(i[s*2-2],i[s*2-1]);s--);for(;o<s&&Kt(i[o*2],i[o*2+1]);o++);}for(;o<s;){var u=$r(t,i,o,s,s,1,r.smooth,l,r.connectNulls);$r(t,n,o+u-1,u,s,-1,r.stackedOnSmooth,l,r.connectNulls),o+=u+1,t.closePath()}},e}(sr);function mo(a,e,t,r,i){var n=a.getArea(),o=n.x,s=n.y,l=n.width,u=n.height,h=t.get(["lineStyle","width"])||0;o-=h/2,s-=h/2,l+=h,u+=h,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var v=new St({shape:{x:o,y:s,width:l,height:u}});if(e){var f=a.getBaseAxis(),c=f.isHorizontal(),d=f.inverse;c?(d&&(v.shape.x+=l),v.shape.width=0):(d||(v.shape.y+=u),v.shape.height=0);var g=q(i)?function(m){i(m,v)}:null;mt(v,{shape:{width:l,height:u,x:o,y:s}},t,null,r,g)}return v}function yo(a,e,t){var r=a.getArea(),i=at(r.r0,1),n=at(r.r,1),o=new Ne({shape:{cx:at(a.cx,1),cy:at(a.cy,1),r0:i,r:n,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}});if(e){var s=a.getBaseAxis().dim==="angle";s?o.shape.endAngle=r.startAngle:o.shape.r=i,mt(o,{shape:{endAngle:r.endAngle,r:n}},t)}return o}function Oh(a,e,t,r,i){if(a){if(a.type==="polar")return yo(a,e,t);if(a.type==="cartesian2d")return mo(a,e,t,r,i)}else return null;return null}function _a(a,e){return a.type===e}function gi(a,e){if(a.length===e.length){for(var t=0;t<a.length;t++)if(a[t]!==e[t])return;return!0}}function pi(a){for(var e=1/0,t=1/0,r=-1/0,i=-1/0,n=0;n<a.length;){var o=a[n++],s=a[n++];isNaN(o)||(e=Math.min(o,e),r=Math.max(o,r)),isNaN(s)||(t=Math.min(s,t),i=Math.max(s,i))}return[[e,t],[r,i]]}function mi(a,e){var t=pi(a),r=t[0],i=t[1],n=pi(e),o=n[0],s=n[1];return Math.max(Math.abs(r[0]-o[0]),Math.abs(r[1]-o[1]),Math.abs(i[0]-s[0]),Math.abs(i[1]-s[1]))}function yi(a){return Tt(a)?a:a?.5:0}function Eh(a,e,t){if(!t.valueDim)return[];for(var r=e.count(),i=Ct(r*2),n=0;n<r;n++){var o=go(t,a,e,n);i[n*2]=o[0],i[n*2+1]=o[1]}return i}function Ot(a,e,t,r,i){var n=t.getBaseAxis(),o=n.dim==="x"||n.dim==="radius"?0:1,s=[],l=0,u=[],h=[],v=[],f=[];if(i){for(l=0;l<a.length;l+=2){var c=e||a;!isNaN(c[l])&&!isNaN(c[l+1])&&f.push(a[l],a[l+1])}a=f}for(l=0;l<a.length-2;l+=2)switch(v[0]=a[l+2],v[1]=a[l+3],h[0]=a[l],h[1]=a[l+1],s.push(h[0],h[1]),r){case"end":u[o]=v[o],u[1-o]=h[1-o],s.push(u[0],u[1]);break;case"middle":var d=(h[o]+v[o])/2,g=[];u[o]=g[o]=d,u[1-o]=h[1-o],g[1-o]=v[1-o],s.push(u[0],u[1]),s.push(g[0],g[1]);break;default:u[o]=h[o],u[1-o]=v[1-o],s.push(u[0],u[1])}return s.push(a[l++],a[l++]),s}function Rh(a,e){var t=[],r=a.length,i,n;function o(h,v,f){var c=h.coord,d=(f-c)/(v.coord-c),g=rl(d,[h.color,v.color]);return{coord:f,color:g}}for(var s=0;s<r;s++){var l=a[s],u=l.coord;if(u<0)i=l;else if(u>e){n?t.push(o(n,l,e)):i&&t.push(o(i,l,0),o(i,l,e));break}else i&&(t.push(o(i,l,0)),i=null),t.push(l),n=l}return t}function Nh(a,e,t){var r=a.getVisual("visualMeta");if(!(!r||!r.length||!a.count())&&e.type==="cartesian2d"){for(var i,n,o=r.length-1;o>=0;o--){var s=a.getDimensionInfo(r[o].dimension);if(i=s&&s.coordDim,i==="x"||i==="y"){n=r[o];break}}if(n){var l=e.getAxis(i),u=Y(n.stops,function(_){return{coord:l.toGlobalCoord(l.dataToCoord(_.value)),color:_.color}}),h=u.length,v=n.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),v.reverse());var f=Rh(u,i==="x"?t.getWidth():t.getHeight()),c=f.length;if(!c&&h)return u[0].coord<0?v[1]?v[1]:u[h-1].color:v[0]?v[0]:u[0].color;var d=10,g=f[0].coord-d,m=f[c-1].coord+d,p=m-g;if(p<.001)return"transparent";M(f,function(_){_.offset=(_.coord-g)/p}),f.push({offset:c?f[c-1].offset:.5,color:v[1]||"transparent"}),f.unshift({offset:c?f[0].offset:.5,color:v[0]||"transparent"});var y=new el(0,0,0,0,f,!0);return y[i]=g,y[i+"2"]=m,y}}}function Bh(a,e,t){var r=a.get("showAllSymbol"),i=r==="auto";if(!(r&&!i)){var n=t.getAxesByScale("ordinal")[0];if(n&&!(i&&Gh(n,e))){var o=e.mapDimension(n.dim),s={};return M(n.getViewLabels(),function(l){var u=n.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(e.get(o,l))}}}}function Gh(a,e){var t=a.getExtent(),r=Math.abs(t[1]-t[0])/a.scale.count();isNaN(r)&&(r=0);for(var i=e.count(),n=Math.max(1,Math.round(i/5)),o=0;o<i;o+=n)if(ya.getSymbolSize(e,o)[a.isHorizontal()?1:0]*1.5>r)return!1;return!0}function zh(a,e){return isNaN(a)||isNaN(e)}function Vh(a){for(var e=a.length/2;e>0&&zh(a[e*2-2],a[e*2-1]);e--);return e-1}function _i(a,e){return[a[e*2],a[e*2+1]]}function Fh(a,e,t){for(var r=a.length/2,i=t==="x"?0:1,n,o,s=0,l=-1,u=0;u<r;u++)if(o=a[u*2+i],!(isNaN(o)||isNaN(a[u*2+1-i]))){if(u===0){n=o;continue}if(n<=e&&o>=e||n>=e&&o<=e){l=u;break}s=u,n=o}return{range:[s,l],t:(e-n)/(o-n)}}function _o(a){if(a.get(["endLabel","show"]))return!0;for(var e=0;e<tr.length;e++)if(a.get([tr[e],"endLabel","show"]))return!0;return!1}function Dr(a,e,t,r){if(_a(e,"cartesian2d")){var i=r.getModel("endLabel"),n=i.get("valueAnimation"),o=r.getData(),s={lastFrameIndex:0},l=_o(r)?function(c,d){a._endLabelOnDuring(c,d,o,s,n,i,e)}:null,u=e.getBaseAxis().isHorizontal(),h=mo(e,t,r,function(){var c=a._endLabel;c&&t&&s.originalX!=null&&c.attr({x:s.originalX,y:s.originalY})},l);if(!r.get("clip",!0)){var v=h.shape,f=Math.max(v.width,v.height);u?(v.y-=f,v.height+=f*2):(v.x-=f,v.width+=f*2)}return l&&l(1,h),h}else return yo(e,t,r)}function Hh(a,e){var t=e.getBaseAxis(),r=t.isHorizontal(),i=t.inverse,n=r?i?"right":"left":"center",o=r?"middle":i?"top":"bottom";return{normal:{align:a.get("align")||n,verticalAlign:a.get("verticalAlign")||o}}}var Wh=function(a){G(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.init=function(){var t=new yt,r=new Dh;this.group.add(r.group),this._symbolDraw=r,this._lineGroup=t,this._changePolyState=nt(this._changePolyState,this)},e.prototype.render=function(t,r,i){var n=t.coordinateSystem,o=this.group,s=t.getData(),l=t.getModel("lineStyle"),u=t.getModel("areaStyle"),h=s.getLayout("points")||[],v=n.type==="polar",f=this._coordSys,c=this._symbolDraw,d=this._polyline,g=this._polygon,m=this._lineGroup,p=!r.ssr&&t.get("animation"),y=!u.isEmpty(),_=u.get("origin"),x=fo(n,s,_),w=y&&Eh(n,s,x),b=t.get("showSymbol"),S=t.get("connectNulls"),A=b&&!v&&Bh(t,s,n),L=this._data;L&&L.eachItemGraphicEl(function(J,jt){J.__temp&&(o.remove(J),L.setItemGraphicEl(jt,null))}),b||c.remove(),o.add(m);var D=v?!1:t.get("step"),C;n&&n.getArea&&t.get("clip",!0)&&(C=n.getArea(),C.width!=null?(C.x-=.1,C.y-=.1,C.width+=.2,C.height+=.2):C.r0&&(C.r0-=.5,C.r+=.5)),this._clipShapeForSymbol=C;var I=Nh(s,n,i)||s.getVisual("style")[s.getVisual("drawType")];if(!(d&&f.type===n.type&&D===this._step))b&&c.updateData(s,{isIgnore:A,clipShape:C,disableAnimation:!0,getSymbolPoint:function(J){return[h[J*2],h[J*2+1]]}}),p&&this._initSymbolLabelAnimation(s,n,C),D&&(w&&(w=Ot(w,h,n,D,S)),h=Ot(h,null,n,D,S)),d=this._newPolyline(h),y?g=this._newPolygon(h,w):g&&(m.remove(g),g=this._polygon=null),v||this._initOrUpdateEndLabel(t,n,se(I)),m.setClipPath(Dr(this,n,!0,t));else{y&&!g?g=this._newPolygon(h,w):g&&!y&&(m.remove(g),g=this._polygon=null),v||this._initOrUpdateEndLabel(t,n,se(I));var T=m.getClipPath();if(T){var k=Dr(this,n,!1,t);mt(T,{shape:k.shape},t)}else m.setClipPath(Dr(this,n,!0,t));b&&c.updateData(s,{isIgnore:A,clipShape:C,disableAnimation:!0,getSymbolPoint:function(J){return[h[J*2],h[J*2+1]]}}),(!gi(this._stackedOnPoints,w)||!gi(this._points,h))&&(p?this._doUpdateAnimation(s,w,n,i,D,_,S):(D&&(w&&(w=Ot(w,h,n,D,S)),h=Ot(h,null,n,D,S)),d.setShape({points:h}),g&&g.setShape({points:h,stackedOnPoints:w})))}var P=t.getModel("emphasis"),N=P.get("focus"),z=P.get("blurScope"),O=P.get("disabled");if(d.useStyle(lt(l.getLineStyle(),{fill:"none",stroke:I,lineJoin:"bevel"})),er(d,t,"lineStyle"),d.style.lineWidth>0&&t.get(["emphasis","lineStyle","width"])==="bolder"){var E=d.getState("emphasis").style;E.lineWidth=+d.style.lineWidth+1}j(d).seriesIndex=t.seriesIndex,Me(d,N,z,O);var R=yi(t.get("smooth")),B=t.get("smoothMonotone");if(d.setShape({smooth:R,smoothMonotone:B,connectNulls:S}),g){var F=s.getCalculationInfo("stackedOnSeries"),$=0;g.useStyle(lt(u.getAreaStyle(),{fill:I,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),F&&($=yi(F.get("smooth"))),g.setShape({smooth:R,stackedOnSmooth:$,smoothMonotone:B,connectNulls:S}),er(g,t,"areaStyle"),j(g).seriesIndex=t.seriesIndex,Me(g,N,z,O)}var tt=this._changePolyState;s.eachItemGraphicEl(function(J){J&&(J.onHoverStateChange=tt)}),this._polyline.onHoverStateChange=tt,this._data=s,this._coordSys=n,this._stackedOnPoints=w,this._points=h,this._step=D,this._valueOrigin=_,t.get("triggerLineEvent")&&(this.packEventData(t,d),g&&this.packEventData(t,g))},e.prototype.packEventData=function(t,r){j(r).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,r,i,n){var o=t.getData(),s=Wr(o,n);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var h=l[s*2],v=l[s*2+1];if(isNaN(h)||isNaN(v)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(h,v))return;var f=t.get("zlevel")||0,c=t.get("z")||0;u=new ya(o,s),u.x=h,u.y=v,u.setZ(f,c);var d=u.getSymbolPath().getTextContent();d&&(d.zlevel=f,d.z=c,d.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else qt.prototype.highlight.call(this,t,r,i,n)},e.prototype.downplay=function(t,r,i,n){var o=t.getData(),s=Wr(o,n);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else qt.prototype.downplay.call(this,t,r,i,n)},e.prototype._changePolyState=function(t){var r=this._polygon;za(this._polyline,t),r&&za(r,t)},e.prototype._newPolyline=function(t){var r=this._polyline;return r&&this._lineGroup.remove(r),r=new Mh({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(r),this._polyline=r,r},e.prototype._newPolygon=function(t,r){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new kh({shape:{points:t,stackedOnPoints:r},segmentIgnoreThreshold:2}),this._lineGroup.add(i),this._polygon=i,i},e.prototype._initSymbolLabelAnimation=function(t,r,i){var n,o,s=r.getBaseAxis(),l=s.inverse;r.type==="cartesian2d"?(n=s.isHorizontal(),o=!1):r.type==="polar"&&(n=s.dim==="angle",o=!0);var u=t.hostModel,h=u.get("animationDuration");q(h)&&(h=h(null));var v=u.get("animationDelay")||0,f=q(v)?v(null):v;t.eachItemGraphicEl(function(c,d){var g=c;if(g){var m=[c.x,c.y],p=void 0,y=void 0,_=void 0;if(i)if(o){var x=i,w=r.pointToCoord(m);n?(p=x.startAngle,y=x.endAngle,_=-w[1]/180*Math.PI):(p=x.r0,y=x.r,_=w[0])}else{var b=i;n?(p=b.x,y=b.x+b.width,_=c.x):(p=b.y+b.height,y=b.y,_=c.y)}var S=y===p?0:(_-p)/(y-p);l&&(S=1-S);var A=q(v)?v(d):h*S+f,L=g.getSymbolPath(),D=L.getTextContent();g.attr({scaleX:0,scaleY:0}),g.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:A}),D&&D.animateFrom({style:{opacity:0}},{duration:300,delay:A}),L.disableLabelAnimation=!0}})},e.prototype._initOrUpdateEndLabel=function(t,r,i){var n=t.getModel("endLabel");if(_o(t)){var o=t.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new _t({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var h=Vh(l);h>=0&&(Re(s,Ee(t,"endLabel"),{inheritColor:i,labelFetcher:t,labelDataIndex:h,defaultText:function(v,f,c){return c!=null?co(o,c):ma(o,v)},enableTextSetter:!0},Hh(n,r)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,r,i,n,o,s,l){var u=this._endLabel,h=this._polyline;if(u){t<1&&n.originalX==null&&(n.originalX=u.x,n.originalY=u.y);var v=i.getLayout("points"),f=i.hostModel,c=f.get("connectNulls"),d=s.get("precision"),g=s.get("distance")||0,m=l.getBaseAxis(),p=m.isHorizontal(),y=m.inverse,_=r.shape,x=y?p?_.x:_.y+_.height:p?_.x+_.width:_.y,w=(p?g:0)*(y?-1:1),b=(p?0:-g)*(y?-1:1),S=p?"x":"y",A=Fh(v,x,S),L=A.range,D=L[1]-L[0],C=void 0;if(D>=1){if(D>1&&!c){var I=_i(v,L[0]);u.attr({x:I[0]+w,y:I[1]+b}),o&&(C=f.getRawValue(L[0]))}else{var I=h.getPointOn(x,S);I&&u.attr({x:I[0]+w,y:I[1]+b});var T=f.getRawValue(L[0]),k=f.getRawValue(L[1]);o&&(C=tl(i,d,T,k,A.t))}n.lastFrameIndex=L[0]}else{var P=t===1||n.lastFrameIndex>0?L[0]:0,I=_i(v,P);o&&(C=f.getRawValue(P)),u.attr({x:I[0]+w,y:I[1]+b})}if(o){var N=Mn(u);typeof N.setLabelText=="function"&&N.setLabelText(C)}}},e.prototype._doUpdateAnimation=function(t,r,i,n,o,s,l){var u=this._polyline,h=this._polygon,v=t.hostModel,f=Ih(this._data,t,this._stackedOnPoints,r,this._coordSys,i,this._valueOrigin),c=f.current,d=f.stackedOnCurrent,g=f.next,m=f.stackedOnNext;if(o&&(d=Ot(f.stackedOnCurrent,f.current,i,o,l),c=Ot(f.current,null,i,o,l),m=Ot(f.stackedOnNext,f.next,i,o,l),g=Ot(f.next,null,i,o,l)),mi(c,g)>3e3||h&&mi(d,m)>3e3){u.stopAnimation(),u.setShape({points:g}),h&&(h.stopAnimation(),h.setShape({points:g,stackedOnPoints:m}));return}u.shape.__points=f.current,u.shape.points=c;var p={shape:{points:g}};f.current!==c&&(p.shape.__points=f.next),u.stopAnimation(),ht(u,p,v),h&&(h.setShape({points:c,stackedOnPoints:d}),h.stopAnimation(),ht(h,{shape:{stackedOnPoints:m}},v),u.shape.points!==h.shape.points&&(h.shape.points=u.shape.points));for(var y=[],_=f.status,x=0;x<_.length;x++){var w=_[x].cmd;if(w==="="){var b=t.getItemGraphicEl(_[x].idx1);b&&y.push({el:b,ptIdx:x})}}u.animators&&u.animators.length&&u.animators[0].during(function(){h&&h.dirtyShape();for(var S=u.shape.__points,A=0;A<y.length;A++){var L=y[A].el,D=y[A].ptIdx*2;L.x=S[D],L.y=S[D+1],L.markRedraw()}})},e.prototype.remove=function(t){var r=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(n,o){n.__temp&&(r.remove(n),i.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(qt);function Uh(a,e){return{seriesType:a,plan:dn(),reset:function(t){var r=t.getData(),i=t.coordinateSystem;if(t.pipelineContext,!!i){var n=Y(i.dimensions,function(v){return r.mapDimension(v)}).slice(0,2),o=n.length,s=r.getCalculationInfo("stackResultDimension");ve(r,n[0])&&(n[0]=s),ve(r,n[1])&&(n[1]=s);var l=r.getStore(),u=r.getDimensionIndex(n[0]),h=r.getDimensionIndex(n[1]);return o&&{progress:function(v,f){for(var c=v.end-v.start,d=Ct(c*o),g=[],m=[],p=v.start,y=0;p<v.end;p++){var _=void 0;if(o===1){var x=l.get(u,p);_=i.dataToPoint(x,null,m)}else g[0]=l.get(u,p),g[1]=l.get(h,p),_=i.dataToPoint(g,null,m);d[y++]=_[0],d[y++]=_[1]}f.setLayout("points",d)}}}}}}var Xh={average:function(a){for(var e=0,t=0,r=0;r<a.length;r++)isNaN(a[r])||(e+=a[r],t++);return t===0?NaN:e/t},sum:function(a){for(var e=0,t=0;t<a.length;t++)e+=a[t]||0;return e},max:function(a){for(var e=-1/0,t=0;t<a.length;t++)a[t]>e&&(e=a[t]);return isFinite(e)?e:NaN},min:function(a){for(var e=1/0,t=0;t<a.length;t++)a[t]<e&&(e=a[t]);return isFinite(e)?e:NaN},nearest:function(a){return a[0]}},Yh=function(a){return Math.round(a.length/2)};function xo(a){return{seriesType:a,reset:function(e,t,r){var i=e.getData(),n=e.get("sampling"),o=e.coordinateSystem,s=i.count();if(s>10&&o.type==="cartesian2d"&&n){var l=o.getBaseAxis(),u=o.getOtherAxis(l),h=l.getExtent(),v=r.getDevicePixelRatio(),f=Math.abs(h[1]-h[0])*(v||1),c=Math.round(s/f);if(isFinite(c)&&c>1){n==="lttb"?e.setData(i.lttbDownSample(i.mapDimension(u.dim),1/c)):n==="minmax"&&e.setData(i.minmaxDownSample(i.mapDimension(u.dim),1/c));var d=void 0;Z(n)?d=Xh[n]:q(n)&&(d=n),d&&e.setData(i.downSample(i.mapDimension(u.dim),1/c,d,Yh))}}}}}function Zh(a){a.registerChartView(Wh),a.registerSeriesModel(Ah),a.registerLayout(Uh("line")),a.registerVisual({seriesType:"line",reset:function(e){var t=e.getData(),r=e.getModel("lineStyle").getLineStyle();r&&!r.stroke&&(r.stroke=t.getVisual("style").fill),t.setVisual("legendLineStyle",r)}}),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,xo("line"))}var qr=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return va(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,r,i){var n=this.coordinateSystem;if(n&&n.clampData){var o=n.clampData(t),s=n.dataToPoint(o);if(i)M(n.getAxes(),function(f,c){if(f.type==="category"&&r!=null){var d=f.getTicksCoords(),g=f.getTickModel().get("alignWithLabel"),m=o[c],p=r[c]==="x1"||r[c]==="y1";if(p&&!g&&(m+=1),d.length<2)return;if(d.length===2){s[c]=f.toGlobalCoord(f.getExtent()[p?1:0]);return}for(var y=void 0,_=void 0,x=1,w=0;w<d.length;w++){var b=d[w].coord,S=w===d.length-1?d[w-1].tickValue+x:d[w].tickValue;if(S===m){_=b;break}else if(S<m)y=b;else if(y!=null&&S>m){_=(b+y)/2;break}w===1&&(x=S-d[0].tickValue)}_==null&&(y?y&&(_=d[d.length-1].coord):_=d[0].coord),s[c]=f.toGlobalCoord(_)}});else{var l=this.getData(),u=l.getLayout("offset"),h=l.getLayout("size"),v=n.getBaseAxis().isHorizontal()?0:1;s[v]+=u+h/2}return s}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(oe);oe.registerClass(qr);var $h=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(){return va(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),r=this.get("largeThreshold");return r>t&&(t=r),t},e.prototype.brushSelector=function(t,r,i){return i.rect(r.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=Pn(qr.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),e}(qr),qh=function(){function a(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return a}(),xi=function(a){G(e,a);function e(t){var r=a.call(this,t)||this;return r.type="sausage",r}return e.prototype.getDefaultShape=function(){return new qh},e.prototype.buildPath=function(t,r){var i=r.cx,n=r.cy,o=Math.max(r.r0||0,0),s=Math.max(r.r,0),l=(s-o)*.5,u=o+l,h=r.startAngle,v=r.endAngle,f=r.clockwise,c=Math.PI*2,d=f?v-h<c:h-v<c;d||(h=v-(f?c:-c));var g=Math.cos(h),m=Math.sin(h),p=Math.cos(v),y=Math.sin(v);d?(t.moveTo(g*o+i,m*o+n),t.arc(g*u+i,m*u+n,l,-Math.PI+h,h,!f)):t.moveTo(g*s+i,m*s+n),t.arc(i,n,s,h,v,!f),t.arc(p*u+i,y*u+n,l,v-Math.PI*2,v-Math.PI,!f),o!==0&&t.arc(i,n,o,v,h,f)},e}(sr);function Kh(a,e){e=e||{};var t=e.isRoundCap;return function(r,i,n){var o=i.position;if(!o||o instanceof Array)return Va(r,i,n);var s=a(o),l=i.distance!=null?i.distance:5,u=this.shape,h=u.cx,v=u.cy,f=u.r,c=u.r0,d=(f+c)/2,g=u.startAngle,m=u.endAngle,p=(g+m)/2,y=t?Math.abs(f-c)/2:0,_=Math.cos,x=Math.sin,w=h+f*_(g),b=v+f*x(g),S="left",A="top";switch(s){case"startArc":w=h+(c-l)*_(p),b=v+(c-l)*x(p),S="center",A="top";break;case"insideStartArc":w=h+(c+l)*_(p),b=v+(c+l)*x(p),S="center",A="bottom";break;case"startAngle":w=h+d*_(g)+Ye(g,l+y,!1),b=v+d*x(g)+Ze(g,l+y,!1),S="right",A="middle";break;case"insideStartAngle":w=h+d*_(g)+Ye(g,-l+y,!1),b=v+d*x(g)+Ze(g,-l+y,!1),S="left",A="middle";break;case"middle":w=h+d*_(p),b=v+d*x(p),S="center",A="middle";break;case"endArc":w=h+(f+l)*_(p),b=v+(f+l)*x(p),S="center",A="bottom";break;case"insideEndArc":w=h+(f-l)*_(p),b=v+(f-l)*x(p),S="center",A="top";break;case"endAngle":w=h+d*_(m)+Ye(m,l+y,!0),b=v+d*x(m)+Ze(m,l+y,!0),S="left",A="middle";break;case"insideEndAngle":w=h+d*_(m)+Ye(m,-l+y,!0),b=v+d*x(m)+Ze(m,-l+y,!0),S="right",A="middle";break;default:return Va(r,i,n)}return r=r||{},r.x=w,r.y=b,r.align=S,r.verticalAlign=A,r}}function jh(a,e,t,r){if(Tt(r)){a.setTextConfig({rotation:r});return}else if(X(e)){a.setTextConfig({rotation:0});return}var i=a.shape,n=i.clockwise?i.startAngle:i.endAngle,o=i.clockwise?i.endAngle:i.startAngle,s=(n+o)/2,l,u=t(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":l=s;break;case"startAngle":case"insideStartAngle":l=n;break;case"endAngle":case"insideEndAngle":l=o;break;default:a.setTextConfig({rotation:0});return}var h=Math.PI*1.5-l;u==="middle"&&h>Math.PI/2&&h<Math.PI*1.5&&(h-=Math.PI),a.setTextConfig({rotation:h})}function Ye(a,e,t){return e*Math.sin(a)*(t?-1:1)}function Ze(a,e,t){return e*Math.cos(a)*(t?1:-1)}function Ce(a,e,t){var r=a.get("borderRadius");if(r==null)return t?{cornerRadius:0}:null;X(r)||(r=[r,r,r,r]);var i=Math.abs(e.r||0-e.r0||0);return{cornerRadius:Y(r,function(n){return Gr(n,i)})}}var Lr=Math.max,Tr=Math.min;function Jh(a,e){var t=a.getArea&&a.getArea();if(_a(a,"cartesian2d")){var r=a.getBaseAxis();if(r.type!=="category"||!r.onBand){var i=e.getLayout("bandWidth");r.isHorizontal()?(t.x-=i,t.width+=i*2):(t.y-=i,t.height+=i*2)}}return t}var Qh=function(a){G(e,a);function e(){var t=a.call(this)||this;return t.type=e.type,t._isFirstFrame=!0,t}return e.prototype.render=function(t,r,i,n){this._model=t,this._removeOnRenderedListener(i),this._updateDrawMode(t);var o=t.get("coordinateSystem");(o==="cartesian2d"||o==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,r,i):this._renderNormal(t,r,i,n))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,r){this._progressiveEls=[],this._incrementalRenderLarge(t,r)},e.prototype.eachRendered=function(t){In(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t,r,i,n){var o=this.group,s=t.getData(),l=this._data,u=t.coordinateSystem,h=u.getBaseAxis(),v;u.type==="cartesian2d"?v=h.isHorizontal():u.type==="polar"&&(v=h.dim==="angle");var f=t.isAnimationEnabled()?t:null,c=tv(t,u);c&&this._enableRealtimeSort(c,s,i);var d=t.get("clip",!0)||c,g=Jh(u,s);o.removeClipPath();var m=t.get("roundCap",!0),p=t.get("showBackground",!0),y=t.getModel("backgroundStyle"),_=y.get("borderRadius")||0,x=[],w=this._backgroundEls,b=n&&n.isInitSort,S=n&&n.type==="changeAxisOrder";function A(C){var I=$e[u.type](s,C),T=sv(u,v,I);return T.useStyle(y.getItemStyle()),u.type==="cartesian2d"?T.setShape("r",_):T.setShape("cornerRadius",_),x[C]=T,T}s.diff(l).add(function(C){var I=s.getItemModel(C),T=$e[u.type](s,C,I);if(p&&A(C),!(!s.hasValue(C)||!Ci[u.type](T))){var k=!1;d&&(k=bi[u.type](g,T));var P=Si[u.type](t,s,C,T,v,f,h.model,!1,m);c&&(P.forceLabelAnimation=!0),Di(P,s,C,I,T,t,v,u.type==="polar"),b?P.attr({shape:T}):c?wi(c,f,P,T,C,v,!1,!1):mt(P,{shape:T},t,C),s.setItemGraphicEl(C,P),o.add(P),P.ignore=k}}).update(function(C,I){var T=s.getItemModel(C),k=$e[u.type](s,C,T);if(p){var P=void 0;w.length===0?P=A(I):(P=w[I],P.useStyle(y.getItemStyle()),u.type==="cartesian2d"?P.setShape("r",_):P.setShape("cornerRadius",_),x[C]=P);var N=$e[u.type](s,C),z=So(v,N,u);ht(P,{shape:z},f,C)}var O=l.getItemGraphicEl(I);if(!s.hasValue(C)||!Ci[u.type](k)){o.remove(O);return}var E=!1;if(d&&(E=bi[u.type](g,k),E&&o.remove(O)),O?la(O):O=Si[u.type](t,s,C,k,v,f,h.model,!!O,m),c&&(O.forceLabelAnimation=!0),S){var R=O.getTextContent();if(R){var B=Mn(R);B.prevValue!=null&&(B.prevValue=B.value)}}else Di(O,s,C,T,k,t,v,u.type==="polar");b?O.attr({shape:k}):c?wi(c,f,O,k,C,v,!0,S):ht(O,{shape:k},t,C,null),s.setItemGraphicEl(C,O),O.ignore=E,o.add(O)}).remove(function(C){var I=l.getItemGraphicEl(C);I&&Ur(I,t,C)}).execute();var L=this._backgroundGroup||(this._backgroundGroup=new yt);L.removeAll();for(var D=0;D<x.length;++D)L.add(x[D]);o.add(L),this._backgroundEls=x,this._data=s},e.prototype._renderLarge=function(t,r,i){this._clear(),Ti(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,r){this._removeBackground(),Ti(r,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var r=t.get("clip",!0)&&Oh(t.coordinateSystem,!1,t),i=this.group;r?i.setClipPath(r):i.removeClipPath()},e.prototype._enableRealtimeSort=function(t,r,i){var n=this;if(r.count()){var o=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(r,t,i),this._isFirstFrame=!1;else{var s=function(l){var u=r.getItemGraphicEl(l),h=u&&u.shape;return h&&Math.abs(o.isHorizontal()?h.height:h.width)||0};this._onRendered=function(){n._updateSortWithinSameData(r,s,o,i)},i.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,r,i){var n=[];return t.each(t.mapDimension(r.dim),function(o,s){var l=i(s);l=l??NaN,n.push({dataIndex:s,mappedValue:l,ordinalNumber:o})}),n.sort(function(o,s){return s.mappedValue-o.mappedValue}),{ordinalNumbers:Y(n,function(o){return o.ordinalNumber})}},e.prototype._isOrderChangedWithinSameData=function(t,r,i){for(var n=i.scale,o=t.mapDimension(i.dim),s=Number.MAX_VALUE,l=0,u=n.getOrdinalMeta().categories.length;l<u;++l){var h=t.rawIndexOf(o,n.getRawOrdinalNumber(l)),v=h<0?Number.MIN_VALUE:r(t.indexOfRawIndex(h));if(v>s)return!0;s=v}return!1},e.prototype._isOrderDifferentInView=function(t,r){for(var i=r.scale,n=i.getExtent(),o=Math.max(0,n[0]),s=Math.min(n[1],i.getOrdinalMeta().categories.length-1);o<=s;++o)if(t.ordinalNumbers[o]!==i.getRawOrdinalNumber(o))return!0},e.prototype._updateSortWithinSameData=function(t,r,i,n){if(this._isOrderChangedWithinSameData(t,r,i)){var o=this._dataSort(t,i,r);this._isOrderDifferentInView(o,i)&&(this._removeOnRenderedListener(n),n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",axisId:i.index,sortInfo:o}))}},e.prototype._dispatchInitSort=function(t,r,i){var n=r.baseAxis,o=this._dataSort(t,n,function(s){return t.get(t.mapDimension(r.otherAxis.dim),s)});i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",isInitSort:!0,axisId:n.index,sortInfo:o})},e.prototype.remove=function(t,r){this._clear(this._model),this._removeOnRenderedListener(r)},e.prototype.dispose=function(t,r){this._removeOnRenderedListener(r)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var r=this.group,i=this._data;t&&t.isAnimationEnabled()&&i&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],i.eachItemGraphicEl(function(n){Ur(n,t,j(n).dataIndex)})):r.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(qt),bi={cartesian2d:function(a,e){var t=e.width<0?-1:1,r=e.height<0?-1:1;t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height);var i=a.x+a.width,n=a.y+a.height,o=Lr(e.x,a.x),s=Tr(e.x+e.width,i),l=Lr(e.y,a.y),u=Tr(e.y+e.height,n),h=s<o,v=u<l;return e.x=h&&o>i?s:o,e.y=v&&l>n?u:l,e.width=h?0:s-o,e.height=v?0:u-l,t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height),h||v},polar:function(a,e){var t=e.r0<=e.r?1:-1;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}var i=Tr(e.r,a.r),n=Lr(e.r0,a.r0);e.r=i,e.r0=n;var o=i-n<0;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}return o}},Si={cartesian2d:function(a,e,t,r,i,n,o,s,l){var u=new St({shape:W({},r),z2:1});if(u.__dataIndex=t,u.name="item",n){var h=u.shape,v=i?"height":"width";h[v]=0}return u},polar:function(a,e,t,r,i,n,o,s,l){var u=!i&&l?xi:Ne,h=new u({shape:r,z2:1});h.name="item";var v=bo(i);if(h.calculateTextPosition=Kh(v,{isRoundCap:u===xi}),n){var f=h.shape,c=i?"r":"endAngle",d={};f[c]=i?r.r0:r.startAngle,d[c]=r[c],(s?ht:mt)(h,{shape:d},n)}return h}};function tv(a,e){var t=a.get("realtimeSort",!0),r=e.getBaseAxis();if(t&&r.type==="category"&&e.type==="cartesian2d")return{baseAxis:r,otherAxis:e.getOtherAxis(r)}}function wi(a,e,t,r,i,n,o,s){var l,u;n?(u={x:r.x,width:r.width},l={y:r.y,height:r.height}):(u={y:r.y,height:r.height},l={x:r.x,width:r.width}),s||(o?ht:mt)(t,{shape:l},e,i,null);var h=e?a.baseAxis.model:null;(o?ht:mt)(t,{shape:u},h,i)}function Ai(a,e){for(var t=0;t<e.length;t++)if(!isFinite(a[e[t]]))return!0;return!1}var ev=["x","y","width","height"],rv=["cx","cy","r","startAngle","endAngle"],Ci={cartesian2d:function(a){return!Ai(a,ev)},polar:function(a){return!Ai(a,rv)}},$e={cartesian2d:function(a,e,t){var r=a.getItemLayout(e),i=t?iv(t,r):0,n=r.width>0?1:-1,o=r.height>0?1:-1;return{x:r.x+n*i/2,y:r.y+o*i/2,width:r.width-n*i,height:r.height-o*i}},polar:function(a,e,t){var r=a.getItemLayout(e);return{cx:r.cx,cy:r.cy,r0:r.r0,r:r.r,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}}};function av(a){return a.startAngle!=null&&a.endAngle!=null&&a.startAngle===a.endAngle}function bo(a){return function(e){var t=e?"Arc":"Angle";return function(r){switch(r){case"start":case"insideStart":case"end":case"insideEnd":return r+t;default:return r}}}(a)}function Di(a,e,t,r,i,n,o,s){var l=e.getItemVisual(t,"style");if(s){if(!n.get("roundCap")){var h=a.shape,v=Ce(r.getModel("itemStyle"),h,!0);W(h,v),a.setShape(h)}}else{var u=r.get(["itemStyle","borderRadius"])||0;a.setShape("r",u)}a.useStyle(l);var f=r.getShallow("cursor");f&&a.attr("cursor",f);var c=s?o?i.r>=i.r0?"endArc":"startArc":i.endAngle>=i.startAngle?"endAngle":"startAngle":o?i.height>=0?"bottom":"top":i.width>=0?"right":"left",d=Ee(r);Re(a,d,{labelFetcher:n,labelDataIndex:t,defaultText:ma(n.getData(),t),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:c});var g=a.getTextContent();if(s&&g){var m=r.get(["label","position"]);a.textConfig.inside=m==="middle"?!0:null,jh(a,m==="outside"?c:m,bo(o),r.get(["label","rotate"]))}al(g,d,n.getRawValue(t),function(y){return co(e,y)});var p=r.getModel(["emphasis"]);Me(a,p.get("focus"),p.get("blurScope"),p.get("disabled")),er(a,r),av(i)&&(a.style.fill="none",a.style.stroke="none",M(a.states,function(y){y.style&&(y.style.fill=y.style.stroke="none")}))}function iv(a,e){var t=a.get(["itemStyle","borderColor"]);if(!t||t==="none")return 0;var r=a.get(["itemStyle","borderWidth"])||0,i=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),n=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(r,i,n)}var nv=function(){function a(){}return a}(),Li=function(a){G(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeBar",r}return e.prototype.getDefaultShape=function(){return new nv},e.prototype.buildPath=function(t,r){for(var i=r.points,n=this.baseDimIdx,o=1-this.baseDimIdx,s=[],l=[],u=this.barWidth,h=0;h<i.length;h+=3)l[n]=u,l[o]=i[h+2],s[n]=i[h+n],s[o]=i[h+o],t.rect(s[0],s[1],l[0],l[1])},e}(sr);function Ti(a,e,t,r){var i=a.getData(),n=i.getLayout("valueAxisHorizontal")?1:0,o=i.getLayout("largeDataIndices"),s=i.getLayout("size"),l=a.getModel("backgroundStyle"),u=i.getLayout("largeBackgroundPoints");if(u){var h=new Li({shape:{points:u},incremental:!!r,silent:!0,z2:0});h.baseDimIdx=n,h.largeDataIndices=o,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),t&&t.push(h)}var v=new Li({shape:{points:i.getLayout("largePoints")},incremental:!!r,ignoreCoarsePointer:!0,z2:1});v.baseDimIdx=n,v.largeDataIndices=o,v.barWidth=s,e.add(v),v.useStyle(i.getVisual("style")),v.style.stroke=null,j(v).seriesIndex=a.seriesIndex,a.get("silent")||(v.on("mousedown",Ii),v.on("mousemove",Ii)),t&&t.push(v)}var Ii=il(function(a){var e=this,t=ov(e,a.offsetX,a.offsetY);j(e).dataIndex=t>=0?t:null},30,!1);function ov(a,e,t){for(var r=a.baseDimIdx,i=1-r,n=a.shape.points,o=a.largeDataIndices,s=[],l=[],u=a.barWidth,h=0,v=n.length/3;h<v;h++){var f=h*3;if(l[r]=u,l[i]=n[f+2],s[r]=n[f+r],s[i]=n[f+i],l[i]<0&&(s[i]+=l[i],l[i]=-l[i]),e>=s[0]&&e<=s[0]+l[0]&&t>=s[1]&&t<=s[1]+l[1])return o[h]}return-1}function So(a,e,t){if(_a(t,"cartesian2d")){var r=e,i=t.getArea();return{x:a?r.x:i.x,y:a?i.y:r.y,width:a?r.width:i.width,height:a?i.height:r.height}}else{var i=t.getArea(),n=e;return{cx:i.cx,cy:i.cy,r0:a?i.r0:n.r0,r:a?i.r:n.r,startAngle:a?n.startAngle:0,endAngle:a?n.endAngle:Math.PI*2}}}function sv(a,e,t){var r=a.type==="polar"?Ne:St;return new r({shape:So(e,t,a),silent:!0,z2:0})}function lv(a){a.registerChartView(Qh),a.registerSeriesModel($h),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,Q(Tu,"bar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Iu("bar")),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,xo("bar")),a.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var r=e.componentType||"series";t.eachComponent({mainType:r,query:e},function(i){e.sortInfo&&i.axis.setCategorySortInfo(e.sortInfo)})})}var Mi=Math.PI*2,qe=Math.PI/180;function wo(a,e){return le(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Ao(a,e){var t=wo(a,e),r=a.get("center"),i=a.get("radius");X(i)||(i=[0,i]);var n=it(t.width,e.getWidth()),o=it(t.height,e.getHeight()),s=Math.min(n,o),l=it(i[0],s/2),u=it(i[1],s/2),h,v,f=a.coordinateSystem;if(f){var c=f.dataToPoint(r);h=c[0]||0,v=c[1]||0}else X(r)||(r=[r,r]),h=it(r[0],n)+t.x,v=it(r[1],o)+t.y;return{cx:h,cy:v,r0:l,r:u}}function uv(a,e,t){e.eachSeriesByType(a,function(r){var i=r.getData(),n=i.mapDimension("value"),o=wo(r,t),s=Ao(r,t),l=s.cx,u=s.cy,h=s.r,v=s.r0,f=-r.get("startAngle")*qe,c=r.get("endAngle"),d=r.get("padAngle")*qe;c=c==="auto"?f-Mi:-c*qe;var g=r.get("minAngle")*qe,m=g+d,p=0;i.each(n,function(z){!isNaN(z)&&p++});var y=i.getSum(n),_=Math.PI/(y||p)*2,x=r.get("clockwise"),w=r.get("roseType"),b=r.get("stillShowZeroSum"),S=i.getDataExtent(n);S[0]=0;var A=x?1:-1,L=[f,c],D=A*d/2;nl(L,!x),f=L[0],c=L[1];var C=Co(r);C.startAngle=f,C.endAngle=c,C.clockwise=x;var I=Math.abs(c-f),T=I,k=0,P=f;if(i.setLayout({viewRect:o,r:h}),i.each(n,function(z,O){var E;if(isNaN(z)){i.setItemLayout(O,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:x,cx:l,cy:u,r0:v,r:w?NaN:h});return}w!=="area"?E=y===0&&b?_:z*_:E=I/p,E<m?(E=m,T-=m):k+=z;var R=P+A*E,B=0,F=0;d>E?(B=P+A*E/2,F=B):(B=P+D,F=R-D),i.setItemLayout(O,{angle:E,startAngle:B,endAngle:F,clockwise:x,cx:l,cy:u,r0:v,r:w?Vr(z,S,[v,h]):h}),P=R}),T<Mi&&p)if(T<=.001){var N=I/p;i.each(n,function(z,O){if(!isNaN(z)){var E=i.getItemLayout(O);E.angle=N;var R=0,B=0;N<d?(R=f+A*(O+1/2)*N,B=R):(R=f+A*O*N+D,B=f+A*(O+1)*N-D),E.startAngle=R,E.endAngle=B}})}else _=T/k,P=f,i.each(n,function(z,O){if(!isNaN(z)){var E=i.getItemLayout(O),R=E.angle===m?m:z*_,B=0,F=0;R<d?(B=P+A*R/2,F=B):(B=P+D,F=P+A*R-D),E.startAngle=B,E.endAngle=F,P+=A*R}})})}var Co=Xt();function hv(a){return{seriesType:a,reset:function(e,t){var r=t.findComponents({mainType:"legend"});if(!(!r||!r.length)){var i=e.getData();i.filterSelf(function(n){for(var o=i.getName(n),s=0;s<r.length;s++)if(!r[s].isSelected(o))return!1;return!0})}}}}var vv=Math.PI/180;function Pi(a,e,t,r,i,n,o,s,l,u){if(a.length<2)return;function h(g){for(var m=g.rB,p=m*m,y=0;y<g.list.length;y++){var _=g.list[y],x=Math.abs(_.label.y-t),w=r+_.len,b=w*w,S=Math.sqrt(Math.abs((1-x*x/p)*b)),A=e+(S+_.len2)*i,L=A-_.label.x,D=_.targetTextWidth-L*i;Do(_,D,!0),_.label.x=A}}function v(g){for(var m={list:[],maxY:0},p={list:[],maxY:0},y=0;y<g.length;y++)if(g[y].labelAlignTo==="none"){var _=g[y],x=_.label.y>t?p:m,w=Math.abs(_.label.y-t);if(w>=x.maxY){var b=_.label.x-e-_.len2*i,S=r+_.len,A=Math.abs(b)<S?Math.sqrt(w*w/(1-b*b/S/S)):S;x.rB=A,x.maxY=w}x.list.push(_)}h(m),h(p)}for(var f=a.length,c=0;c<f;c++)if(a[c].position==="outer"&&a[c].labelAlignTo==="labelLine"){var d=a[c].label.x-u;a[c].linePoints[1][0]+=d,a[c].label.x=u}mh(a,l,l+o)&&v(a)}function cv(a,e,t,r,i,n,o,s){for(var l=[],u=[],h=Number.MAX_VALUE,v=-Number.MAX_VALUE,f=0;f<a.length;f++){var c=a[f].label;Ir(a[f])||(c.x<e?(h=Math.min(h,c.x),l.push(a[f])):(v=Math.max(v,c.x),u.push(a[f])))}for(var f=0;f<a.length;f++){var d=a[f];if(!Ir(d)&&d.linePoints){if(d.labelStyleWidth!=null)continue;var c=d.label,g=d.linePoints,m=void 0;d.labelAlignTo==="edge"?c.x<e?m=g[2][0]-d.labelDistance-o-d.edgeDistance:m=o+i-d.edgeDistance-g[2][0]-d.labelDistance:d.labelAlignTo==="labelLine"?c.x<e?m=h-o-d.bleedMargin:m=o+i-v-d.bleedMargin:c.x<e?m=c.x-o-d.bleedMargin:m=o+i-c.x-d.bleedMargin,d.targetTextWidth=m,Do(d,m)}}Pi(u,e,t,r,1,i,n,o,s,v),Pi(l,e,t,r,-1,i,n,o,s,h);for(var f=0;f<a.length;f++){var d=a[f];if(!Ir(d)&&d.linePoints){var c=d.label,g=d.linePoints,p=d.labelAlignTo==="edge",y=c.style.padding,_=y?y[1]+y[3]:0,x=c.style.backgroundColor?0:_,w=d.rect.width+x,b=g[1][0]-g[2][0];p?c.x<e?g[2][0]=o+d.edgeDistance+w+d.labelDistance:g[2][0]=o+i-d.edgeDistance-w-d.labelDistance:(c.x<e?g[2][0]=c.x+d.labelDistance:g[2][0]=c.x-d.labelDistance,g[1][0]=g[2][0]+b),g[1][1]=g[2][1]=c.y}}}function Do(a,e,t){if(t===void 0&&(t=!1),a.labelStyleWidth==null){var r=a.label,i=r.style,n=a.rect,o=i.backgroundColor,s=i.padding,l=s?s[1]+s[3]:0,u=i.overflow,h=n.width+(o?0:l);if(e<h||t){var v=n.height;if(u&&u.match("break")){r.setStyle("backgroundColor",null),r.setStyle("width",e-l);var f=r.getBoundingRect();r.setStyle("width",Math.ceil(f.width)),r.setStyle("backgroundColor",o)}else{var c=e-l,d=e<h?c:t?c>a.unconstrainedWidth?null:c:null;r.setStyle("width",d)}var g=r.getBoundingRect();n.width=g.width;var m=(r.style.margin||0)+2.1;n.height=g.height+m,n.y-=(n.height-v)/2}}}function Ir(a){return a.position==="center"}function fv(a){var e=a.getData(),t=[],r,i,n=!1,o=(a.get("minShowLabelAngle")||0)*vv,s=e.getLayout("viewRect"),l=e.getLayout("r"),u=s.width,h=s.x,v=s.y,f=s.height;function c(b){b.ignore=!0}function d(b){if(!b.ignore)return!0;for(var S in b.states)if(b.states[S].ignore===!1)return!0;return!1}e.each(function(b){var S=e.getItemGraphicEl(b),A=S.shape,L=S.getTextContent(),D=S.getTextGuideLine(),C=e.getItemModel(b),I=C.getModel("label"),T=I.get("position")||C.get(["emphasis","label","position"]),k=I.get("distanceToLabelLine"),P=I.get("alignTo"),N=it(I.get("edgeDistance"),u),z=I.get("bleedMargin"),O=C.getModel("labelLine"),E=O.get("length");E=it(E,u);var R=O.get("length2");if(R=it(R,u),Math.abs(A.endAngle-A.startAngle)<o){M(L.states,c),L.ignore=!0,D&&(M(D.states,c),D.ignore=!0);return}if(d(L)){var B=(A.startAngle+A.endAngle)/2,F=Math.cos(B),$=Math.sin(B),tt,J,jt,Be;r=A.cx,i=A.cy;var Jt=T==="inside"||T==="inner";if(T==="center")tt=A.cx,J=A.cy,Be="center";else{var vr=(Jt?(A.r+A.r0)/2*F:A.r*F)+r,cr=(Jt?(A.r+A.r0)/2*$:A.r*$)+i;if(tt=vr+F*3,J=cr+$*3,!Jt){var wa=vr+F*(E+l-A.r),Aa=cr+$*(E+l-A.r),Ca=wa+(F<0?-1:1)*R,Da=Aa;P==="edge"?tt=F<0?h+N:h+u-N:tt=Ca+(F<0?-k:k),J=Da,jt=[[vr,cr],[wa,Aa],[Ca,Da]]}Be=Jt?"center":P==="edge"?F>0?"right":"left":F>0?"left":"right"}var de=Math.PI,Qt=0,ge=I.get("rotate");if(Tt(ge))Qt=ge*(de/180);else if(T==="center")Qt=0;else if(ge==="radial"||ge===!0){var Xo=F<0?-B+de:-B;Qt=Xo}else if(ge==="tangential"&&T!=="outside"&&T!=="outer"){var te=Math.atan2(F,$);te<0&&(te=de*2+te);var Yo=$>0;Yo&&(te=de+te),Qt=te-de}if(n=!!Qt,L.x=tt,L.y=J,L.rotation=Qt,L.setStyle({verticalAlign:"middle"}),Jt){L.setStyle({align:Be});var fr=L.states.select;fr&&(fr.x+=L.x,fr.y+=L.y)}else{var pe=L.getBoundingRect().clone();pe.applyTransform(L.getComputedTransform());var La=(L.style.margin||0)+2.1;pe.y-=La/2,pe.height+=La,t.push({label:L,labelLine:D,position:T,len:E,len2:R,minTurnAngle:O.get("minTurnAngle"),maxSurfaceAngle:O.get("maxSurfaceAngle"),surfaceNormal:new rt(F,$),linePoints:jt,textAlign:Be,labelDistance:k,labelAlignTo:P,edgeDistance:N,bleedMargin:z,rect:pe,unconstrainedWidth:pe.width,labelStyleWidth:L.style.width})}S.setTextConfig({inside:Jt})}}),!n&&a.get("avoidLabelOverlap")&&cv(t,r,i,l,u,f,h,v);for(var g=0;g<t.length;g++){var m=t[g],p=m.label,y=m.labelLine,_=isNaN(p.x)||isNaN(p.y);if(p){p.setStyle({align:m.textAlign}),_&&(M(p.states,c),p.ignore=!0);var x=p.states.select;x&&(x.x+=p.x,x.y+=p.y)}if(y){var w=m.linePoints;_||!w?(M(y.states,c),y.ignore=!0):(hh(w,m.minTurnAngle),vh(w,m.surfaceNormal,m.maxSurfaceAngle),y.setShape({points:w}),p.__hostTarget.textGuideLineConfig={anchor:new rt(w[0][0],w[0][1])})}}}var dv=function(a){G(e,a);function e(t,r,i){var n=a.call(this)||this;n.z2=2;var o=new _t;return n.setTextContent(o),n.updateData(t,r,i,!0),n}return e.prototype.updateData=function(t,r,i,n){var o=this,s=t.hostModel,l=t.getItemModel(r),u=l.getModel("emphasis"),h=t.getItemLayout(r),v=W(Ce(l.getModel("itemStyle"),h,!0),h);if(isNaN(v.startAngle)){o.setShape(v);return}if(n){o.setShape(v);var f=s.getShallow("animationType");s.ecModel.ssr?(mt(o,{scaleX:0,scaleY:0},s,{dataIndex:r,isFrom:!0}),o.originX=v.cx,o.originY=v.cy):f==="scale"?(o.shape.r=h.r0,mt(o,{shape:{r:h.r}},s,r)):i!=null?(o.setShape({startAngle:i,endAngle:i}),mt(o,{shape:{startAngle:h.startAngle,endAngle:h.endAngle}},s,r)):(o.shape.endAngle=h.startAngle,ht(o,{shape:{endAngle:h.endAngle}},s,r))}else la(o),ht(o,{shape:v},s,r);o.useStyle(t.getItemVisual(r,"style")),er(o,l);var c=(h.startAngle+h.endAngle)/2,d=s.get("selectedOffset"),g=Math.cos(c)*d,m=Math.sin(c)*d,p=l.getShallow("cursor");p&&o.attr("cursor",p),this._updateLabel(s,t,r),o.ensureState("emphasis").shape=W({r:h.r+(u.get("scale")&&u.get("scaleSize")||0)},Ce(u.getModel("itemStyle"),h)),W(o.ensureState("select"),{x:g,y:m,shape:Ce(l.getModel(["select","itemStyle"]),h)}),W(o.ensureState("blur"),{shape:Ce(l.getModel(["blur","itemStyle"]),h)});var y=o.getTextGuideLine(),_=o.getTextContent();y&&W(y.ensureState("select"),{x:g,y:m}),W(_.ensureState("select"),{x:g,y:m}),Me(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r,i){var n=this,o=r.getItemModel(i),s=o.getModel("labelLine"),l=r.getItemVisual(i,"style"),u=l&&l.fill,h=l&&l.opacity;Re(n,Ee(o),{labelFetcher:r.hostModel,labelDataIndex:i,inheritColor:u,defaultOpacity:h,defaultText:t.getFormattedLabel(i,"normal")||r.getName(i)});var v=n.getTextContent();n.setTextConfig({position:null,rotation:null}),v.attr({z2:10});var f=t.get(["label","position"]);if(f!=="outside"&&f!=="outer")n.removeTextGuideLine();else{var c=this.getTextGuideLine();c||(c=new Cn,this.setTextGuideLine(c)),fh(this,dh(o),{stroke:u,opacity:ol(s.get(["lineStyle","opacity"]),h,1)})}},e}(Ne),gv=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,i,n){var o=t.getData(),s=this._data,l=this.group,u;if(!s&&o.count()>0){for(var h=o.getItemLayout(0),v=1;isNaN(h&&h.startAngle)&&v<o.count();++v)h=o.getItemLayout(v);h&&(u=h.startAngle)}if(this._emptyCircleSector&&l.remove(this._emptyCircleSector),o.count()===0&&t.get("showEmptyCircle")){var f=Co(t),c=new Ne({shape:W(Ao(t,i),f)});c.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=c,l.add(c)}o.diff(s).add(function(d){var g=new dv(o,d,u);o.setItemGraphicEl(d,g),l.add(g)}).update(function(d,g){var m=s.getItemGraphicEl(g);m.updateData(o,d,u),m.off("click"),l.add(m),o.setItemGraphicEl(d,m)}).remove(function(d){var g=s.getItemGraphicEl(d);Ur(g,t,d)}).execute(),fv(t),t.get("animationTypeUpdate")!=="expansion"&&(this._data=o)},e.prototype.dispose=function(){},e.prototype.containPoint=function(t,r){var i=r.getData(),n=i.getItemLayout(0);if(n){var o=t[0]-n.cx,s=t[1]-n.cy,l=Math.sqrt(o*o+s*s);return l<=n.r&&l>=n.r0}},e.type="pie",e}(qt);function pv(a,e,t){e=X(e)&&{coordDimensions:e}||W({encodeDefine:a.getEncode()},e);var r=a.getSource(),i=Yn(r,e).dimensions,n=new Xn(i,a);return n.initData(r,t),n}var mv=function(){function a(e,t){this._getDataWithEncodedVisual=e,this._getRawData=t}return a.prototype.getAllNames=function(){var e=this._getRawData();return e.mapArray(e.getName)},a.prototype.containName=function(e){var t=this._getRawData();return t.indexOfName(e)>=0},a.prototype.indexOfName=function(e){var t=this._getDataWithEncodedVisual();return t.indexOfName(e)},a.prototype.getItemVisual=function(e,t){var r=this._getDataWithEncodedVisual();return r.getItemVisual(e,t)},a}(),yv=Xt(),_v=function(a){G(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new mv(nt(this.getData,this),nt(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.mergeOption=function(){a.prototype.mergeOption.apply(this,arguments)},e.prototype.getInitialData=function(){return pv(this,{coordDimensions:["value"],encodeDefaulter:Q(sl,this)})},e.prototype.getDataParams=function(t){var r=this.getData(),i=yv(r),n=i.seats;if(!n){var o=[];r.each(r.mapDimension("value"),function(l){o.push(l)}),n=i.seats=ll(o,r.hostModel.get("percentPrecision"))}var s=a.prototype.getDataParams.call(this,t);return s.percent=n[t]||0,s.$vars.push("percent"),s},e.prototype._defaultLabelLine=function(t){ul(t,"labelLine",["show"]);var r=t.labelLine,i=t.emphasis.labelLine;r.show=r.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},e.type="series.pie",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},e}(oe);function xv(a){return{seriesType:a,reset:function(e,t){var r=e.getData();r.filterSelf(function(i){var n=r.mapDimension("value"),o=r.get(n,i);return!(Tt(o)&&!isNaN(o)&&o<0)})}}}function bv(a){a.registerChartView(gv),a.registerSeriesModel(_v),hl("pie",a.registerAction),a.registerLayout(Q(uv,"pie")),a.registerProcessor(hv("pie")),a.registerProcessor(xv("pie"))}var Sv=function(a){G(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(Lt),Kr=function(a){G(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Nt).models[0]},e.type="cartesian2dAxis",e}(Lt);vl(Kr,Ju);var Lo={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},wv=ct({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Lo),xa=ct({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Lo),Av=ct({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},xa),Cv=lt({logBase:10},xa);const Dv={category:wv,value:xa,time:Av,log:Cv};var Lv={value:1,category:1,time:1,log:1};function ki(a,e,t,r){M(Lv,function(i,n){var o=ct(ct({},Dv[n],!0),r,!0),s=function(l){G(u,l);function u(){var h=l!==null&&l.apply(this,arguments)||this;return h.type=e+"Axis."+n,h}return u.prototype.mergeDefaultAndTheme=function(h,v){var f=cl(this),c=f?kn(h):{},d=v.getTheme();ct(h,d.get(n+"Axis")),ct(h,this.getDefaultOption()),h.type=Oi(h),f&&On(h,c,f)},u.prototype.optionUpdated=function(){var h=this.option;h.type==="category"&&(this.__ordinalMeta=Yr.createByAxisModel(this))},u.prototype.getCategories=function(h){var v=this.option;if(v.type==="category")return h?v.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=e+"Axis."+n,u.defaultOption=o,u}(t);a.registerComponentModel(s)}),a.registerSubTypeDefaulter(e+"Axis",Oi)}function Oi(a){return a.type||(a.data?"category":"value")}var Tv=function(){function a(e){this.type="cartesian",this._dimList=[],this._axes={},this.name=e||""}return a.prototype.getAxis=function(e){return this._axes[e]},a.prototype.getAxes=function(){return Y(this._dimList,function(e){return this._axes[e]},this)},a.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),ne(this.getAxes(),function(t){return t.scale.type===e})},a.prototype.addAxis=function(e){var t=e.dim;this._axes[t]=e,this._dimList.push(t)},a}(),jr=["x","y"];function Ei(a){return a.type==="interval"||a.type==="time"}var Iv=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=jr,t}return e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,r=this.getAxis("y").scale;if(!(!Ei(t)||!Ei(r))){var i=t.getExtent(),n=r.getExtent(),o=this.dataToPoint([i[0],n[0]]),s=this.dataToPoint([i[1],n[1]]),l=i[1]-i[0],u=n[1]-n[0];if(!(!l||!u)){var h=(s[0]-o[0])/l,v=(s[1]-o[1])/u,f=o[0]-i[0]*h,c=o[1]-n[0]*v,d=this._transform=[h,0,0,v,f,c];this._invTransform=fl([],d)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var r=this.getAxis("x"),i=this.getAxis("y");return r.contain(r.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,r){var i=this.dataToPoint(t),n=this.dataToPoint(r),o=this.getArea(),s=new Bt(i[0],i[1],n[0]-i[0],n[1]-i[1]);return o.intersect(s)},e.prototype.dataToPoint=function(t,r,i){i=i||[];var n=t[0],o=t[1];if(this._transform&&n!=null&&isFinite(n)&&o!=null&&isFinite(o))return ue(i,t,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return i[0]=s.toGlobalCoord(s.dataToCoord(n,r)),i[1]=l.toGlobalCoord(l.dataToCoord(o,r)),i},e.prototype.clampData=function(t,r){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,o=i.getExtent(),s=n.getExtent(),l=i.parse(t[0]),u=n.parse(t[1]);return r=r||[],r[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),r[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),r},e.prototype.pointToData=function(t,r){var i=[];if(this._invTransform)return ue(i,t,this._invTransform);var n=this.getAxis("x"),o=this.getAxis("y");return i[0]=n.coordToData(n.toLocalCoord(t[0]),r),i[1]=o.coordToData(o.toLocalCoord(t[1]),r),i},e.prototype.getOtherAxis=function(t){return this.getAxis(t.dim==="x"?"y":"x")},e.prototype.getArea=function(t){t=t||0;var r=this.getAxis("x").getGlobalExtent(),i=this.getAxis("y").getGlobalExtent(),n=Math.min(r[0],r[1])-t,o=Math.min(i[0],i[1])-t,s=Math.max(r[0],r[1])-n+t,l=Math.max(i[0],i[1])-o+t;return new Bt(n,o,s,l)},e}(Tv),Mv=function(a){G(e,a);function e(t,r,i,n,o){var s=a.call(this,t,r,i)||this;return s.index=0,s.type=n||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.getGlobalExtent=function(t){var r=this.getExtent();return r[0]=this.toGlobalCoord(r[0]),r[1]=this.toGlobalCoord(r[1]),t&&r[0]>r[1]&&r.reverse(),r},e.prototype.pointToData=function(t,r){return this.coordToData(this.toLocalCoord(t[this.dim==="x"?0:1]),r)},e.prototype.setCategorySortInfo=function(t){if(this.type!=="category")return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(lh);function Jr(a,e,t){t=t||{};var r=a.coordinateSystem,i=e.axis,n={},o=i.getAxesOnZeroOf()[0],s=i.position,l=o?"onZero":s,u=i.dim,h=r.getRect(),v=[h.x,h.x+h.width,h.y,h.y+h.height],f={left:0,right:1,top:0,bottom:1,onZero:2},c=e.get("offset")||0,d=u==="x"?[v[2]-c,v[3]+c]:[v[0]-c,v[1]+c];if(o){var g=o.toGlobalCoord(o.dataToCoord(0));d[f.onZero]=Math.max(Math.min(g,d[1]),d[0])}n.position=[u==="y"?d[f[l]]:v[0],u==="x"?d[f[l]]:v[3]],n.rotation=Math.PI/2*(u==="x"?0:1);var m={top:-1,bottom:1,left:-1,right:1};n.labelDirection=n.tickDirection=n.nameDirection=m[s],n.labelOffset=o?d[f[s]]-d[f.onZero]:0,e.get(["axisTick","inside"])&&(n.tickDirection=-n.tickDirection),Pe(t.labelInside,e.get(["axisLabel","inside"]))&&(n.labelDirection=-n.labelDirection);var p=e.get(["axisLabel","rotate"]);return n.labelRotate=l==="top"?-p:p,n.z2=1,n}function Ri(a){return a.get("coordinateSystem")==="cartesian2d"}function Ni(a){var e={xAxisModel:null,yAxisModel:null};return M(e,function(t,r){var i=r.replace(/Model$/,""),n=a.getReferringComponents(i,Nt).models[0];e[r]=n}),e}var Mr=Math.log;function Pv(a,e,t){var r=ce.prototype,i=r.getTicks.call(t),n=r.getTicks.call(t,!0),o=i.length-1,s=r.getInterval.call(t),l=ro(a,e),u=l.extent,h=l.fixMin,v=l.fixMax;if(a.type==="log"){var f=Mr(a.base);u=[Mr(u[0])/f,Mr(u[1])/f]}a.setExtent(u[0],u[1]),a.calcNiceExtent({splitNumber:o,fixMin:h,fixMax:v});var c=r.getExtent.call(a);h&&(u[0]=c[0]),v&&(u[1]=c[1]);var d=r.getInterval.call(a),g=u[0],m=u[1];if(h&&v)d=(m-g)/o;else if(h)for(m=u[0]+d*o;m<u[1]&&isFinite(m)&&isFinite(u[1]);)d=Sr(d),m=u[0]+d*o;else if(v)for(g=u[1]-d*o;g>u[0]&&isFinite(g)&&isFinite(u[0]);)d=Sr(d),g=u[1]-d*o;else{var p=a.getTicks().length-1;p>o&&(d=Sr(d));var y=d*o;m=Math.ceil(u[1]/d)*d,g=at(m-y),g<0&&u[0]>=0?(g=0,m=at(y)):m>0&&u[1]<=0&&(m=0,g=-at(y))}var _=(i[0].value-n[0].value)/s,x=(i[o].value-n[o].value)/s;r.setExtent.call(a,g+d*_,m+d*x),r.setInterval.call(a,d),(_||x)&&r.setNiceExtent.call(a,g+d,m-d)}var kv=function(){function a(e,t,r){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=jr,this._initCartesian(e,t,r),this.model=e}return a.prototype.getRect=function(){return this._rect},a.prototype.update=function(e,t){var r=this._axesMap;this._updateScale(e,this.model);function i(o){var s,l=or(o),u=l.length;if(u){for(var h=[],v=u-1;v>=0;v--){var f=+l[v],c=o[f],d=c.model,g=c.scale;Zr(g)&&d.get("alignTicks")&&d.get("interval")==null?h.push(c):(si(g,d),Zr(g)&&(s=c))}h.length&&(s||(s=h.pop(),si(s.scale,s.model)),M(h,function(m){Pv(m.scale,m.model,s.scale)}))}}i(r.x),i(r.y);var n={};M(r.x,function(o){Bi(r,"y",o,n)}),M(r.y,function(o){Bi(r,"x",o,n)}),this.resize(this.model,t)},a.prototype.resize=function(e,t,r){var i=e.getBoxLayoutParams(),n=!r&&e.get("containLabel"),o=le(i,{width:t.getWidth(),height:t.getHeight()});this._rect=o;var s=this._axesList;l(),n&&(M(s,function(u){if(!u.model.get(["axisLabel","inside"])){var h=qu(u);if(h){var v=u.isHorizontal()?"height":"width",f=u.model.get(["axisLabel","margin"]);o[v]-=h[v]+f,u.position==="top"?o.y+=h.height+f:u.position==="left"&&(o.x+=h.width+f)}}}),l()),M(this._coordsList,function(u){u.calcAffineTransform()});function l(){M(s,function(u){var h=u.isHorizontal(),v=h?[0,o.width]:[0,o.height],f=u.inverse?1:0;u.setExtent(v[f],v[1-f]),Ov(u,h?o.x:o.y)})}},a.prototype.getAxis=function(e,t){var r=this._axesMap[e];if(r!=null)return r[t||0]},a.prototype.getAxes=function(){return this._axesList.slice()},a.prototype.getCartesian=function(e,t){if(e!=null&&t!=null){var r="x"+e+"y"+t;return this._coordsMap[r]}st(e)&&(t=e.yAxisIndex,e=e.xAxisIndex);for(var i=0,n=this._coordsList;i<n.length;i++)if(n[i].getAxis("x").index===e||n[i].getAxis("y").index===t)return n[i]},a.prototype.getCartesians=function(){return this._coordsList.slice()},a.prototype.convertToPixel=function(e,t,r){var i=this._findConvertTarget(t);return i.cartesian?i.cartesian.dataToPoint(r):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(r)):null},a.prototype.convertFromPixel=function(e,t,r){var i=this._findConvertTarget(t);return i.cartesian?i.cartesian.pointToData(r):i.axis?i.axis.coordToData(i.axis.toLocalCoord(r)):null},a.prototype._findConvertTarget=function(e){var t=e.seriesModel,r=e.xAxisModel||t&&t.getReferringComponents("xAxis",Nt).models[0],i=e.yAxisModel||t&&t.getReferringComponents("yAxis",Nt).models[0],n=e.gridModel,o=this._coordsList,s,l;if(t)s=t.coordinateSystem,Ft(o,s)<0&&(s=null);else if(r&&i)s=this.getCartesian(r.componentIndex,i.componentIndex);else if(r)l=this.getAxis("x",r.componentIndex);else if(i)l=this.getAxis("y",i.componentIndex);else if(n){var u=n.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},a.prototype.containPoint=function(e){var t=this._coordsList[0];if(t)return t.containPoint(e)},a.prototype._initCartesian=function(e,t,r){var i=this,n=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(t.eachComponent("xAxis",u("x"),this),t.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,M(s.x,function(h,v){M(s.y,function(f,c){var d="x"+v+"y"+c,g=new Iv(d);g.master=i,g.model=e,i._coordsMap[d]=g,i._coordsList.push(g),g.addAxis(h),g.addAxis(f)})});function u(h){return function(v,f){if(Pr(v,e)){var c=v.get("position");h==="x"?c!=="top"&&c!=="bottom"&&(c=o.bottom?"top":"bottom"):c!=="left"&&c!=="right"&&(c=o.left?"right":"left"),o[c]=!0;var d=new Mv(h,Zu(v),[0,0],v.get("type"),c),g=d.type==="category";d.onBand=g&&v.get("boundaryGap"),d.inverse=v.get("inverse"),v.axis=d,d.model=v,d.grid=n,d.index=f,n._axesList.push(d),s[h][f]=d,l[h]++}}}},a.prototype._updateScale=function(e,t){M(this._axesList,function(i){if(i.scale.setExtent(1/0,-1/0),i.type==="category"){var n=i.model.get("categorySortInfo");i.scale.setSortInfo(n)}}),e.eachSeries(function(i){if(Ri(i)){var n=Ni(i),o=n.xAxisModel,s=n.yAxisModel;if(!Pr(o,t)||!Pr(s,t))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=i.getData(),h=l.getAxis("x"),v=l.getAxis("y");r(u,h),r(u,v)}},this);function r(i,n){M(ju(i,n.dim),function(o){n.scale.unionExtentFromData(i,o)})}},a.prototype.getTooltipAxes=function(e){var t=[],r=[];return M(this.getCartesians(),function(i){var n=e!=null&&e!=="auto"?i.getAxis(e):i.getBaseAxis(),o=i.getOtherAxis(n);Ft(t,n)<0&&t.push(n),Ft(r,o)<0&&r.push(o)}),{baseAxes:t,otherAxes:r}},a.create=function(e,t){var r=[];return e.eachComponent("grid",function(i,n){var o=new a(i,e,t);o.name="grid_"+n,o.resize(i,t,!0),i.coordinateSystem=o,r.push(o)}),e.eachSeries(function(i){if(Ri(i)){var n=Ni(i),o=n.xAxisModel,s=n.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;i.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),r},a.dimensions=jr,a}();function Pr(a,e){return a.getCoordSysModel()===e}function Bi(a,e,t,r){t.getAxesOnZeroOf=function(){return n?[n]:[]};var i=a[e],n,o=t.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)Gi(i[l])&&(n=i[l]);else for(var u in i)if(i.hasOwnProperty(u)&&Gi(i[u])&&!r[h(i[u])]){n=i[u];break}n&&(r[h(n)]=!0);function h(v){return v.dim+"_"+v.index}}function Gi(a){return a&&a.type!=="category"&&a.type!=="time"&&$u(a)}function Ov(a,e){var t=a.getExtent(),r=t[0]+t[1];a.toGlobalCoord=a.dim==="x"?function(i){return i+e}:function(i){return r-i+e},a.toLocalCoord=a.dim==="x"?function(i){return i-e}:function(i){return r-i+e}}var Vt=Math.PI,Ht=function(){function a(e,t){this.group=new yt,this.opt=t,this.axisModel=e,lt(t,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var r=new yt({x:t.position[0],y:t.position[1],rotation:t.rotation});r.updateTransform(),this._transformGroup=r}return a.prototype.hasBuilder=function(e){return!!zi[e]},a.prototype.add=function(e){zi[e](this.opt,this.axisModel,this.group,this._transformGroup)},a.prototype.getGroup=function(){return this.group},a.innerTextLayout=function(e,t,r){var i=En(t-e),n,o;return ir(i)?(o=r>0?"top":"bottom",n="center"):ir(i-Vt)?(o=r>0?"bottom":"top",n="center"):(o="middle",i>0&&i<Vt?n=r>0?"right":"left":n=r>0?"left":"right"),{rotation:i,textAlign:n,textVerticalAlign:o}},a.makeAxisEventDataBase=function(e){var t={componentType:e.mainType,componentIndex:e.componentIndex};return t[e.mainType+"Index"]=e.componentIndex,t},a.isLabelSilent=function(e){var t=e.get("tooltip");return e.get("silent")||!(e.get("triggerEvent")||t&&t.show)},a}(),zi={axisLine:function(a,e,t,r){var i=e.get(["axisLine","show"]);if(i==="auto"&&a.handleAutoShown&&(i=a.handleAutoShown("axisLine")),!!i){var n=e.axis.getExtent(),o=r.transform,s=[n[0],0],l=[n[1],0],u=s[0]>l[0];o&&(ue(s,s,o),ue(l,l,o));var h=W({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),v=new rr({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:h,strokeContainThreshold:a.strokeContainThreshold||5,silent:!0,z2:1});ar(v.shape,v.style.lineWidth),v.anid="line",t.add(v);var f=e.get(["axisLine","symbol"]);if(f!=null){var c=e.get(["axisLine","symbolSize"]);Z(f)&&(f=[f,f]),(Z(c)||Tt(c))&&(c=[c,c]);var d=Tn(e.get(["axisLine","symbolOffset"])||0,c),g=c[0],m=c[1];M([{rotate:a.rotation+Math.PI/2,offset:d[0],r:0},{rotate:a.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],function(p,y){if(f[y]!=="none"&&f[y]!=null){var _=Ie(f[y],-g/2,-m/2,g,m,h.stroke,!0),x=p.r+p.offset,w=u?l:s;_.attr({rotation:p.rotate,x:w[0]+x*Math.cos(a.rotation),y:w[1]-x*Math.sin(a.rotation),silent:!0,z2:11}),t.add(_)}})}}},axisTickLabel:function(a,e,t,r){var i=Nv(t,r,e,a),n=Gv(t,r,e,a);if(Rv(e,n,i),Bv(t,r,e,a.tickDirection),e.get(["axisLabel","hideOverlap"])){var o=gh(Y(n,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));yh(o)}},axisName:function(a,e,t,r){var i=Pe(a.axisName,e.get("name"));if(i){var n=e.get("nameLocation"),o=a.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,u=e.axis.getExtent(),h=u[0]>u[1]?-1:1,v=[n==="start"?u[0]-h*l:n==="end"?u[1]+h*l:(u[0]+u[1])/2,Fi(n)?a.labelOffset+o*l:0],f,c=e.get("nameRotate");c!=null&&(c=c*Vt/180);var d;Fi(n)?f=Ht.innerTextLayout(a.rotation,c??a.rotation,o):(f=Ev(a.rotation,n,c||0,u),d=a.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(f.rotation)),!isFinite(d)&&(d=null)));var g=s.getFont(),m=e.get("nameTruncate",!0)||{},p=m.ellipsis,y=Pe(a.nameTruncateMaxWidth,m.maxWidth,d),_=new _t({x:v[0],y:v[1],rotation:f.rotation,silent:Ht.isLabelSilent(e),style:he(s,{text:i,font:g,overflow:"truncate",width:y,ellipsis:p,fill:s.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:s.get("align")||f.textAlign,verticalAlign:s.get("verticalAlign")||f.textVerticalAlign}),z2:1});if(ua({el:_,componentModel:e,itemName:i}),_.__fullText=i,_.anid="name",e.get("triggerEvent")){var x=Ht.makeAxisEventDataBase(e);x.targetType="axisName",x.name=i,j(_).eventData=x}r.add(_),_.updateTransform(),t.add(_),_.decomposeTransform()}}};function Ev(a,e,t,r){var i=En(t-a),n,o,s=r[0]>r[1],l=e==="start"&&!s||e!=="start"&&s;return ir(i-Vt/2)?(o=l?"bottom":"top",n="center"):ir(i-Vt*1.5)?(o=l?"top":"bottom",n="center"):(o="middle",i<Vt*1.5&&i>Vt/2?n=l?"left":"right":n=l?"right":"left"),{rotation:i,textAlign:n,textVerticalAlign:o}}function Rv(a,e,t){if(!ao(a.axis)){var r=a.get(["axisLabel","showMinLabel"]),i=a.get(["axisLabel","showMaxLabel"]);e=e||[],t=t||[];var n=e[0],o=e[1],s=e[e.length-1],l=e[e.length-2],u=t[0],h=t[1],v=t[t.length-1],f=t[t.length-2];r===!1?(vt(n),vt(u)):Vi(n,o)&&(r?(vt(o),vt(h)):(vt(n),vt(u))),i===!1?(vt(s),vt(v)):Vi(l,s)&&(i?(vt(l),vt(f)):(vt(s),vt(v)))}}function vt(a){a&&(a.ignore=!0)}function Vi(a,e){var t=a&&a.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(!(!t||!r)){var i=dl([]);return Rn(i,i,-a.rotation),t.applyTransform(Fa([],i,a.getLocalTransform())),r.applyTransform(Fa([],i,e.getLocalTransform())),t.intersect(r)}}function Fi(a){return a==="middle"||a==="center"}function To(a,e,t,r,i){for(var n=[],o=[],s=[],l=0;l<a.length;l++){var u=a[l].coord;o[0]=u,o[1]=0,s[0]=u,s[1]=t,e&&(ue(o,o,e),ue(s,s,e));var h=new rr({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:r,z2:2,autoBatch:!0,silent:!0});ar(h.shape,h.style.lineWidth),h.anid=i+"_"+a[l].tickValue,n.push(h)}return n}function Nv(a,e,t,r){var i=t.axis,n=t.getModel("axisTick"),o=n.get("show");if(o==="auto"&&r.handleAutoShown&&(o=r.handleAutoShown("axisTick")),!(!o||i.scale.isBlank())){for(var s=n.getModel("lineStyle"),l=r.tickDirection*n.get("length"),u=i.getTicksCoords(),h=To(u,e.transform,l,lt(s.getLineStyle(),{stroke:t.get(["axisLine","lineStyle","color"])}),"ticks"),v=0;v<h.length;v++)a.add(h[v]);return h}}function Bv(a,e,t,r){var i=t.axis,n=t.getModel("minorTick");if(!(!n.get("show")||i.scale.isBlank())){var o=i.getMinorTicksCoords();if(o.length)for(var s=n.getModel("lineStyle"),l=r*n.get("length"),u=lt(s.getLineStyle(),lt(t.getModel("axisTick").getLineStyle(),{stroke:t.get(["axisLine","lineStyle","color"])})),h=0;h<o.length;h++)for(var v=To(o[h],e.transform,l,u,"minorticks_"+h),f=0;f<v.length;f++)a.add(v[f])}}function Gv(a,e,t,r){var i=t.axis,n=Pe(r.axisLabelShow,t.get(["axisLabel","show"]));if(!(!n||i.scale.isBlank())){var o=t.getModel("axisLabel"),s=o.get("margin"),l=i.getViewLabels(),u=(Pe(r.labelRotate,o.get("rotate"))||0)*Vt/180,h=Ht.innerTextLayout(r.rotation,u,r.labelDirection),v=t.getCategories&&t.getCategories(!0),f=[],c=Ht.isLabelSilent(t),d=t.get("triggerEvent");return M(l,function(g,m){var p=i.scale.type==="ordinal"?i.scale.getRawOrdinalNumber(g.tickValue):g.tickValue,y=g.formattedLabel,_=g.rawLabel,x=o;if(v&&v[p]){var w=v[p];st(w)&&w.textStyle&&(x=new Rt(w.textStyle,o,t.ecModel))}var b=x.getTextColor()||t.get(["axisLine","lineStyle","color"]),S=i.dataToCoord(p),A=x.getShallow("align",!0)||h.textAlign,L=bt(x.getShallow("alignMinLabel",!0),A),D=bt(x.getShallow("alignMaxLabel",!0),A),C=x.getShallow("verticalAlign",!0)||x.getShallow("baseline",!0)||h.textVerticalAlign,I=bt(x.getShallow("verticalAlignMinLabel",!0),C),T=bt(x.getShallow("verticalAlignMaxLabel",!0),C),k=new _t({x:S,y:r.labelOffset+r.labelDirection*s,rotation:h.rotation,silent:c,z2:10+(g.level||0),style:he(x,{text:y,align:m===0?L:m===l.length-1?D:A,verticalAlign:m===0?I:m===l.length-1?T:C,fill:q(b)?b(i.type==="category"?_:i.type==="value"?p+"":p,m):b})});if(k.anid="label_"+p,ua({el:k,componentModel:t,itemName:y,formatterParamsExtra:{isTruncated:function(){return k.isTruncated},value:_,tickIndex:m}}),d){var P=Ht.makeAxisEventDataBase(t);P.targetType="axisLabel",P.value=_,P.tickIndex=m,i.type==="category"&&(P.dataIndex=p),j(k).eventData=P}e.add(k),k.updateTransform(),f.push(k),a.add(k),k.decomposeTransform()}),f}}function zv(a,e){var t={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return Vv(t,a,e),t.seriesInvolved&&Hv(t,a),t}function Vv(a,e,t){var r=e.getComponent("tooltip"),i=e.getComponent("axisPointer"),n=i.get("link",!0)||[],o=[];M(t.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var l=Oe(s.model),u=a.coordSysAxesInfo[l]={};a.coordSysMap[l]=s;var h=s.model,v=h.getModel("tooltip",r);if(M(s.getAxes(),Q(g,!1,null)),s.getTooltipAxes&&r&&v.get("show")){var f=v.get("trigger")==="axis",c=v.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(v.get(["axisPointer","axis"]));(f||c)&&M(d.baseAxes,Q(g,c?"cross":!0,f)),c&&M(d.otherAxes,Q(g,"cross",!1))}function g(m,p,y){var _=y.model.getModel("axisPointer",i),x=_.get("show");if(!(!x||x==="auto"&&!m&&!Qr(_))){p==null&&(p=_.get("triggerTooltip")),_=m?Fv(y,v,i,e,m,p):_;var w=_.get("snap"),b=_.get("triggerEmphasis"),S=Oe(y.model),A=p||w||y.type==="category",L=a.axesInfo[S]={key:S,axis:y,coordSys:s,axisPointerModel:_,triggerTooltip:p,triggerEmphasis:b,involveSeries:A,snap:w,useHandle:Qr(_),seriesModels:[],linkGroup:null};u[S]=L,a.seriesInvolved=a.seriesInvolved||A;var D=Wv(n,y);if(D!=null){var C=o[D]||(o[D]={axesInfo:{}});C.axesInfo[S]=L,C.mapper=n[D].mapper,L.linkGroup=C}}}})}function Fv(a,e,t,r,i,n){var o=e.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],l={};M(s,function(f){l[f]=ie(o.get(f))}),l.snap=a.type!=="category"&&!!n,o.get("type")==="cross"&&(l.type="line");var u=l.label||(l.label={});if(u.show==null&&(u.show=!1),i==="cross"){var h=o.get(["label","show"]);if(u.show=h??!0,!n){var v=l.lineStyle=o.get("crossStyle");v&&lt(u,v.textStyle)}}return a.model.getModel("axisPointer",new Rt(l,t,r))}function Hv(a,e){e.eachSeries(function(t){var r=t.coordinateSystem,i=t.get(["tooltip","trigger"],!0),n=t.get(["tooltip","show"],!0);!r||i==="none"||i===!1||i==="item"||n===!1||t.get(["axisPointer","show"],!0)===!1||M(a.coordSysAxesInfo[Oe(r.model)],function(o){var s=o.axis;r.getAxis(s.dim)===s&&(o.seriesModels.push(t),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=t.getData().count())})})}function Wv(a,e){for(var t=e.model,r=e.dim,i=0;i<a.length;i++){var n=a[i]||{};if(kr(n[r+"AxisId"],t.id)||kr(n[r+"AxisIndex"],t.componentIndex)||kr(n[r+"AxisName"],t.name))return i}}function kr(a,e){return a==="all"||X(a)&&Ft(a,e)>=0||a===e}function Uv(a){var e=ba(a);if(e){var t=e.axisPointerModel,r=e.axis.scale,i=t.option,n=t.get("status"),o=t.get("value");o!=null&&(o=r.parse(o));var s=Qr(t);n==null&&(i.status=s?"show":"hide");var l=r.getExtent().slice();l[0]>l[1]&&l.reverse(),(o==null||o>l[1])&&(o=l[1]),o<l[0]&&(o=l[0]),i.value=o,s&&(i.status=e.axis.scale.isBlank()?"hide":"show")}}function ba(a){var e=(a.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Oe(a)]}function Xv(a){var e=ba(a);return e&&e.axisPointerModel}function Qr(a){return!!a.get(["handle","show"])}function Oe(a){return a.type+"||"+a.id}var Hi={},Io=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){this.axisPointerClass&&Uv(t),a.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,i,!0)},e.prototype.updateAxisPointer=function(t,r,i,n){this._doUpdateAxisPointerClass(t,i,!1)},e.prototype.remove=function(t,r){var i=this._axisPointer;i&&i.remove(r)},e.prototype.dispose=function(t,r){this._disposeAxisPointer(r),a.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,r,i){var n=e.getAxisPointerClass(this.axisPointerClass);if(n){var o=Xv(t);o?(this._axisPointer||(this._axisPointer=new n)).render(t,o,r,i):this._disposeAxisPointer(r)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,r){Hi[t]=r},e.getAxisPointerClass=function(t){return t&&Hi[t]},e.type="axis",e}(Wt),ta=Xt();function Yv(a,e,t,r){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitArea"),o=n.getModel("areaStyle"),s=o.get("color"),l=r.coordinateSystem.getRect(),u=i.getTicksCoords({tickModel:n,clamp:!0});if(u.length){var h=s.length,v=ta(a).splitAreaColors,f=ft(),c=0;if(v)for(var d=0;d<u.length;d++){var g=v.get(u[d].tickValue);if(g!=null){c=(g+(h-1)*d)%h;break}}var m=i.toGlobalCoord(u[0].coord),p=o.getAreaStyle();s=X(s)?s:[s];for(var d=1;d<u.length;d++){var y=i.toGlobalCoord(u[d].coord),_=void 0,x=void 0,w=void 0,b=void 0;i.isHorizontal()?(_=m,x=l.y,w=y-_,b=l.height,m=_+w):(_=l.x,x=m,w=l.width,b=y-x,m=x+b);var S=u[d-1].tickValue;S!=null&&f.set(S,c),e.add(new St({anid:S!=null?"area_"+S:null,shape:{x:_,y:x,width:w,height:b},style:lt({fill:s[c]},p),autoBatch:!0,silent:!0})),c=(c+1)%h}ta(a).splitAreaColors=f}}}function Zv(a){ta(a).splitAreaColors=null}var $v=["axisLine","axisTickLabel","axisName"],qv=["splitArea","splitLine","minorSplitLine"],Mo=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="CartesianAxisPointer",t}return e.prototype.render=function(t,r,i,n){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new yt,this.group.add(this._axisGroup),!!t.get("show")){var s=t.getCoordSysModel(),l=Jr(s,t),u=new Ht(t,W({handleAutoShown:function(v){for(var f=s.coordinateSystem.getCartesians(),c=0;c<f.length;c++)if(Zr(f[c].getOtherAxis(t.axis).scale))return!0;return!1}},l));M($v,u.add,u),this._axisGroup.add(u.getGroup()),M(qv,function(v){t.get([v,"show"])&&Kv[v](this,this._axisGroup,t,s)},this);var h=n&&n.type==="changeAxisOrder"&&n.isInitSort;h||gl(o,this._axisGroup,t),a.prototype.render.call(this,t,r,i,n)}},e.prototype.remove=function(){Zv(this)},e.type="cartesianAxis",e}(Io),Kv={splitLine:function(a,e,t,r){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),o=n.getModel("lineStyle"),s=o.get("color"),l=n.get("showMinLine")!==!1,u=n.get("showMaxLine")!==!1;s=X(s)?s:[s];for(var h=r.coordinateSystem.getRect(),v=i.isHorizontal(),f=0,c=i.getTicksCoords({tickModel:n}),d=[],g=[],m=o.getLineStyle(),p=0;p<c.length;p++){var y=i.toGlobalCoord(c[p].coord);if(!(p===0&&!l||p===c.length-1&&!u)){var _=c[p].tickValue;v?(d[0]=y,d[1]=h.y,g[0]=y,g[1]=h.y+h.height):(d[0]=h.x,d[1]=y,g[0]=h.x+h.width,g[1]=y);var x=f++%s.length,w=new rr({anid:_!=null?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:g[0],y2:g[1]},style:lt({stroke:s[x]},m),silent:!0});ar(w.shape,m.lineWidth),e.add(w)}}}},minorSplitLine:function(a,e,t,r){var i=t.axis,n=t.getModel("minorSplitLine"),o=n.getModel("lineStyle"),s=r.coordinateSystem.getRect(),l=i.isHorizontal(),u=i.getMinorTicksCoords();if(u.length)for(var h=[],v=[],f=o.getLineStyle(),c=0;c<u.length;c++)for(var d=0;d<u[c].length;d++){var g=i.toGlobalCoord(u[c][d].coord);l?(h[0]=g,h[1]=s.y,v[0]=g,v[1]=s.y+s.height):(h[0]=s.x,h[1]=g,v[0]=s.x+s.width,v[1]=g);var m=new rr({anid:"minor_line_"+u[c][d].tickValue,autoBatch:!0,shape:{x1:h[0],y1:h[1],x2:v[0],y2:v[1]},style:f,silent:!0});ar(m.shape,f.lineWidth),e.add(m)}},splitArea:function(a,e,t,r){Yv(a,e,t,r)}},Po=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="xAxis",e}(Mo),jv=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=Po.type,t}return e.type="yAxis",e}(Mo),Jv=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="grid",t}return e.prototype.render=function(t,r){this.group.removeAll(),t.get("show")&&this.group.add(new St({shape:t.coordinateSystem.getRect(),style:lt({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(Wt),Wi={offset:0};function Qv(a){a.registerComponentView(Jv),a.registerComponentModel(Sv),a.registerCoordinateSystem("cartesian2d",kv),ki(a,"x",Kr,Wi),ki(a,"y",Kr,Wi),a.registerComponentView(Po),a.registerComponentView(jv),a.registerPreprocessor(function(e){e.xAxis&&e.yAxis&&!e.grid&&(e.grid={})})}var $t=Xt(),Ui=ie,Or=nt,tc=function(){function a(){this._dragging=!1,this.animationThreshold=15}return a.prototype.render=function(e,t,r,i){var n=t.get("value"),o=t.get("status");if(this._axisModel=e,this._axisPointerModel=t,this._api=r,!(!i&&this._lastValue===n&&this._lastStatus===o)){this._lastValue=n,this._lastStatus=o;var s=this._group,l=this._handle;if(!o||o==="hide"){s&&s.hide(),l&&l.hide();return}s&&s.show(),l&&l.show();var u={};this.makeElOption(u,n,e,t,r);var h=u.graphicKey;h!==this._lastGraphicKey&&this.clear(r),this._lastGraphicKey=h;var v=this._moveAnimation=this.determineAnimation(e,t);if(!s)s=this._group=new yt,this.createPointerEl(s,u,e,t),this.createLabelEl(s,u,e,t),r.getZr().add(s);else{var f=Q(Xi,t,v);this.updatePointerEl(s,u,f),this.updateLabelEl(s,u,f,t)}Zi(s,t,!0),this._renderHandle(n)}},a.prototype.remove=function(e){this.clear(e)},a.prototype.dispose=function(e){this.clear(e)},a.prototype.determineAnimation=function(e,t){var r=t.get("animation"),i=e.axis,n=i.type==="category",o=t.get("snap");if(!o&&!n)return!1;if(r==="auto"||r==null){var s=this.animationThreshold;if(n&&i.getBandWidth()>s)return!0;if(o){var l=ba(e).seriesDataCount,u=i.getExtent();return Math.abs(u[0]-u[1])/l>s}return!1}return r===!0},a.prototype.makeElOption=function(e,t,r,i,n){},a.prototype.createPointerEl=function(e,t,r,i){var n=t.pointer;if(n){var o=$t(e).pointerEl=new pl[n.type](Ui(t.pointer));e.add(o)}},a.prototype.createLabelEl=function(e,t,r,i){if(t.label){var n=$t(e).labelEl=new _t(Ui(t.label));e.add(n),Yi(n,i)}},a.prototype.updatePointerEl=function(e,t,r){var i=$t(e).pointerEl;i&&t.pointer&&(i.setStyle(t.pointer.style),r(i,{shape:t.pointer.shape}))},a.prototype.updateLabelEl=function(e,t,r,i){var n=$t(e).labelEl;n&&(n.setStyle(t.label.style),r(n,{x:t.label.x,y:t.label.y}),Yi(n,i))},a.prototype._renderHandle=function(e){if(!(this._dragging||!this.updateHandleTransform)){var t=this._axisPointerModel,r=this._api.getZr(),i=this._handle,n=t.getModel("handle"),o=t.get("status");if(!n.get("show")||!o||o==="hide"){i&&r.remove(i),this._handle=null;return}var s;this._handle||(s=!0,i=this._handle=Nn(n.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(u){ml(u.event)},onmousedown:Or(this._onHandleDragMove,this,0,0),drift:Or(this._onHandleDragMove,this),ondragend:Or(this._onHandleDragEnd,this)}),r.add(i)),Zi(i,t,!1),i.setStyle(n.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var l=n.get("size");X(l)||(l=[l,l]),i.scaleX=l[0]/2,i.scaleY=l[1]/2,Bn(this,"_doDispatchAxisPointer",n.get("throttle")||0,"fixRate"),this._moveHandleToValue(e,s)}},a.prototype._moveHandleToValue=function(e,t){Xi(this._axisPointerModel,!t&&this._moveAnimation,this._handle,Er(this.getHandleTransform(e,this._axisModel,this._axisPointerModel)))},a.prototype._onHandleDragMove=function(e,t){var r=this._handle;if(r){this._dragging=!0;var i=this.updateHandleTransform(Er(r),[e,t],this._axisModel,this._axisPointerModel);this._payloadInfo=i,r.stopAnimation(),r.attr(Er(i)),$t(r).lastProp=null,this._doDispatchAxisPointer()}},a.prototype._doDispatchAxisPointer=function(){var e=this._handle;if(e){var t=this._payloadInfo,r=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:r.axis.dim,axisIndex:r.componentIndex}]})}},a.prototype._onHandleDragEnd=function(){this._dragging=!1;var e=this._handle;if(e){var t=this._axisPointerModel.get("value");this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"})}},a.prototype.clear=function(e){this._lastValue=null,this._lastStatus=null;var t=e.getZr(),r=this._group,i=this._handle;t&&r&&(this._lastGraphicKey=null,r&&t.remove(r),i&&t.remove(i),this._group=null,this._handle=null,this._payloadInfo=null),Xr(this,"_doDispatchAxisPointer")},a.prototype.doClear=function(){},a.prototype.buildLabel=function(e,t,r){return r=r||0,{x:e[r],y:e[1-r],width:t[r],height:t[1-r]}},a}();function Xi(a,e,t,r){ko($t(t).lastProp,r)||($t(t).lastProp=r,e?ht(t,r,a):(t.stopAnimation(),t.attr(r)))}function ko(a,e){if(st(a)&&st(e)){var t=!0;return M(e,function(r,i){t=t&&ko(a[i],r)}),!!t}else return a===e}function Yi(a,e){a[e.get(["label","show"])?"show":"hide"]()}function Er(a){return{x:a.x||0,y:a.y||0,rotation:a.rotation||0}}function Zi(a,e,t){var r=e.get("z"),i=e.get("zlevel");a&&a.traverse(function(n){n.type!=="group"&&(r!=null&&(n.z=r),i!=null&&(n.zlevel=i),n.silent=t)})}function ec(a){var e=a.get("type"),t=a.getModel(e+"Style"),r;return e==="line"?(r=t.getLineStyle(),r.fill=null):e==="shadow"&&(r=t.getAreaStyle(),r.stroke=null),r}function rc(a,e,t,r,i){var n=t.get("value"),o=Oo(n,e.axis,e.ecModel,t.get("seriesDataIndices"),{precision:t.get(["label","precision"]),formatter:t.get(["label","formatter"])}),s=t.getModel("label"),l=ha(s.get("padding")||0),u=s.getFont(),h=An(o,u),v=i.position,f=h.width+l[1]+l[3],c=h.height+l[0]+l[2],d=i.align;d==="right"&&(v[0]-=f),d==="center"&&(v[0]-=f/2);var g=i.verticalAlign;g==="bottom"&&(v[1]-=c),g==="middle"&&(v[1]-=c/2),ac(v,f,c,r);var m=s.get("backgroundColor");(!m||m==="auto")&&(m=e.get(["axisLine","lineStyle","color"])),a.label={x:v[0],y:v[1],style:he(s,{text:o,font:u,fill:s.getTextColor(),padding:l,backgroundColor:m}),z2:10}}function ac(a,e,t,r){var i=r.getWidth(),n=r.getHeight();a[0]=Math.min(a[0]+e,i)-e,a[1]=Math.min(a[1]+t,n)-t,a[0]=Math.max(a[0],0),a[1]=Math.max(a[1],0)}function Oo(a,e,t,r,i){a=e.scale.parse(a);var n=e.scale.getLabel({value:a},{precision:i.precision}),o=i.formatter;if(o){var s={value:ga(e,{value:a}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]};M(r,function(l){var u=t.getSeriesByIndex(l.seriesIndex),h=l.dataIndexInside,v=u&&u.getDataParams(h);v&&s.seriesData.push(v)}),Z(o)?n=o.replace("{value}",n):q(o)&&(n=o(s))}return n}function Eo(a,e,t){var r=yl();return Rn(r,r,t.rotation),_l(r,r,t.position),xl([a.dataToCoord(e),(t.labelOffset||0)+(t.labelDirection||1)*(t.labelMargin||0)],r)}function ic(a,e,t,r,i,n){var o=Ht.innerTextLayout(t.rotation,0,t.labelDirection);t.labelMargin=i.get(["label","margin"]),rc(e,r,i,n,{position:Eo(r.axis,a,t),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function nc(a,e,t){return t=t||0,{x1:a[t],y1:a[1-t],x2:e[t],y2:e[1-t]}}function oc(a,e,t){return t=t||0,{x:a[t],y:a[1-t],width:e[t],height:e[1-t]}}var sc=function(a){G(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,i,n,o){var s=i.axis,l=s.grid,u=n.get("type"),h=$i(l,s).getOtherAxis(s).getGlobalExtent(),v=s.toGlobalCoord(s.dataToCoord(r,!0));if(u&&u!=="none"){var f=ec(n),c=lc[u](s,v,h);c.style=f,t.graphicKey=c.type,t.pointer=c}var d=Jr(l.model,i);ic(r,t,d,i,n,o)},e.prototype.getHandleTransform=function(t,r,i){var n=Jr(r.axis.grid.model,r,{labelInside:!1});n.labelMargin=i.get(["handle","margin"]);var o=Eo(r.axis,t,n);return{x:o[0],y:o[1],rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,r,i,n){var o=i.axis,s=o.grid,l=o.getGlobalExtent(!0),u=$i(s,o).getOtherAxis(o).getGlobalExtent(),h=o.dim==="x"?0:1,v=[t.x,t.y];v[h]+=r[h],v[h]=Math.min(l[1],v[h]),v[h]=Math.max(l[0],v[h]);var f=(u[1]+u[0])/2,c=[f,f];c[h]=v[h];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:v[0],y:v[1],rotation:t.rotation,cursorPoint:c,tooltipOption:d[h]}},e}(tc);function $i(a,e){var t={};return t[e.dim+"AxisIndex"]=e.index,a.getCartesian(t)}var lc={line:function(a,e,t){var r=nc([e,t[0]],[e,t[1]],qi(a));return{type:"Line",subPixelOptimize:!0,shape:r}},shadow:function(a,e,t){var r=Math.max(1,a.getBandWidth()),i=t[1]-t[0];return{type:"Rect",shape:oc([e-r/2,t[0]],[r,i],qi(a))}}};function qi(a){return a.dim==="x"?0:1}var uc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="axisPointer",e.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},e}(Lt),Dt=Xt(),hc=M;function Ro(a,e,t){if(!ut.node){var r=e.getZr();Dt(r).records||(Dt(r).records={}),vc(r,e);var i=Dt(r).records[a]||(Dt(r).records[a]={});i.handler=t}}function vc(a,e){if(Dt(a).initialized)return;Dt(a).initialized=!0,t("click",Q(Ki,"click")),t("mousemove",Q(Ki,"mousemove")),t("globalout",fc);function t(r,i){a.on(r,function(n){var o=dc(e);hc(Dt(a).records,function(s){s&&i(s,n,o.dispatchAction)}),cc(o.pendings,e)})}}function cc(a,e){var t=a.showTip.length,r=a.hideTip.length,i;t?i=a.showTip[t-1]:r&&(i=a.hideTip[r-1]),i&&(i.dispatchAction=null,e.dispatchAction(i))}function fc(a,e,t){a.handler("leave",null,t)}function Ki(a,e,t,r){e.handler(a,t,r)}function dc(a){var e={showTip:[],hideTip:[]},t=function(r){var i=e[r.type];i?i.push(r):(r.dispatchAction=t,a.dispatchAction(r))};return{dispatchAction:t,pendings:e}}function ea(a,e){if(!ut.node){var t=e.getZr(),r=(Dt(t).records||{})[a];r&&(Dt(t).records[a]=null)}}var gc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=r.getComponent("tooltip"),o=t.get("triggerOn")||n&&n.get("triggerOn")||"mousemove|click";Ro("axisPointer",i,function(s,l,u){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&u({type:"updateAxisPointer",currTrigger:s,x:l&&l.offsetX,y:l&&l.offsetY})})},e.prototype.remove=function(t,r){ea("axisPointer",r)},e.prototype.dispose=function(t,r){ea("axisPointer",r)},e.type="axisPointer",e}(Wt);function No(a,e){var t=[],r=a.seriesIndex,i;if(r==null||!(i=e.getSeriesByIndex(r)))return{point:[]};var n=i.getData(),o=Wr(n,a);if(o==null||o<0||X(o))return{point:[]};var s=n.getItemGraphicEl(o),l=i.coordinateSystem;if(i.getTooltipPosition)t=i.getTooltipPosition(o)||[];else if(l&&l.dataToPoint)if(a.isStacked){var u=l.getBaseAxis(),h=l.getOtherAxis(u),v=h.dim,f=u.dim,c=v==="x"||v==="radius"?1:0,d=n.mapDimension(f),g=[];g[c]=n.get(d,o),g[1-c]=n.get(n.getCalculationInfo("stackResultDimension"),o),t=l.dataToPoint(g)||[]}else t=l.dataToPoint(n.getValues(Y(l.dimensions,function(p){return n.mapDimension(p)}),o))||[];else if(s){var m=s.getBoundingRect().clone();m.applyTransform(s.transform),t=[m.x+m.width/2,m.y+m.height/2]}return{point:t,el:s}}var ji=Xt();function pc(a,e,t){var r=a.currTrigger,i=[a.x,a.y],n=a,o=a.dispatchAction||nt(t.dispatchAction,t),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Qe(i)&&(i=No({seriesIndex:n.seriesIndex,dataIndex:n.dataIndex},e).point);var l=Qe(i),u=n.axesInfo,h=s.axesInfo,v=r==="leave"||Qe(i),f={},c={},d={list:[],map:{}},g={showPointer:Q(yc,c),showTooltip:Q(_c,d)};M(s.coordSysMap,function(p,y){var _=l||p.containPoint(i);M(s.coordSysAxesInfo[y],function(x,w){var b=x.axis,S=wc(u,x);if(!v&&_&&(!u||S)){var A=S&&S.value;A==null&&!l&&(A=b.pointToData(i)),A!=null&&Ji(x,A,g,!1,f)}})});var m={};return M(h,function(p,y){var _=p.linkGroup;_&&!c[y]&&M(_.axesInfo,function(x,w){var b=c[w];if(x!==p&&b){var S=b.value;_.mapper&&(S=p.axis.scale.parse(_.mapper(S,Qi(x),Qi(p)))),m[p.key]=S}})}),M(m,function(p,y){Ji(h[y],p,g,!0,f)}),xc(c,h,f),bc(d,i,a,o),Sc(h,o,t),f}}function Ji(a,e,t,r,i){var n=a.axis;if(!(n.scale.isBlank()||!n.containData(e))){if(!a.involveSeries){t.showPointer(a,e);return}var o=mc(e,a),s=o.payloadBatch,l=o.snapToValue;s[0]&&i.seriesIndex==null&&W(i,s[0]),!r&&a.snap&&n.containData(l)&&l!=null&&(e=l),t.showPointer(a,e,s),t.showTooltip(a,o,l)}}function mc(a,e){var t=e.axis,r=t.dim,i=a,n=[],o=Number.MAX_VALUE,s=-1;return M(e.seriesModels,function(l,u){var h=l.getData().mapDimensionsAll(r),v,f;if(l.getAxisTooltipData){var c=l.getAxisTooltipData(h,a,t);f=c.dataIndices,v=c.nestestValue}else{if(f=l.getData().indicesOfNearest(h[0],a,t.type==="category"?.5:null),!f.length)return;v=l.getData().get(h[0],f[0])}if(!(v==null||!isFinite(v))){var d=a-v,g=Math.abs(d);g<=o&&((g<o||d>=0&&s<0)&&(o=g,s=d,i=v,n.length=0),M(f,function(m){n.push({seriesIndex:l.seriesIndex,dataIndexInside:m,dataIndex:l.getData().getRawIndex(m)})}))}}),{payloadBatch:n,snapToValue:i}}function yc(a,e,t,r){a[e.key]={value:t,payloadBatch:r}}function _c(a,e,t,r){var i=t.payloadBatch,n=e.axis,o=n.model,s=e.axisPointerModel;if(!(!e.triggerTooltip||!i.length)){var l=e.coordSys.model,u=Oe(l),h=a.map[u];h||(h=a.map[u]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},a.list.push(h)),h.dataByAxis.push({axisDim:n.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:r,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:i.slice()})}}function xc(a,e,t){var r=t.axesInfo=[];M(e,function(i,n){var o=i.axisPointerModel.option,s=a[n];s?(!i.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!i.useHandle&&(o.status="hide"),o.status==="show"&&r.push({axisDim:i.axis.dim,axisIndex:i.axis.model.componentIndex,value:o.value})})}function bc(a,e,t,r){if(Qe(e)||!a.list.length){r({type:"hideTip"});return}var i=((a.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};r({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:t.tooltipOption,position:t.position,dataIndexInside:i.dataIndexInside,dataIndex:i.dataIndex,seriesIndex:i.seriesIndex,dataByCoordSys:a.list})}function Sc(a,e,t){var r=t.getZr(),i="axisPointerLastHighlights",n=ji(r)[i]||{},o=ji(r)[i]={};M(a,function(u,h){var v=u.axisPointerModel.option;v.status==="show"&&u.triggerEmphasis&&M(v.seriesDataIndices,function(f){var c=f.seriesIndex+" | "+f.dataIndex;o[c]=f})});var s=[],l=[];M(n,function(u,h){!o[h]&&l.push(u)}),M(o,function(u,h){!n[h]&&s.push(u)}),l.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:l}),s.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function wc(a,e){for(var t=0;t<(a||[]).length;t++){var r=a[t];if(e.axis.dim===r.axisDim&&e.axis.model.componentIndex===r.axisIndex)return r}}function Qi(a){var e=a.axis.model,t={},r=t.axisDim=a.axis.dim;return t.axisIndex=t[r+"AxisIndex"]=e.componentIndex,t.axisName=t[r+"AxisName"]=e.name,t.axisId=t[r+"AxisId"]=e.id,t}function Qe(a){return!a||a[0]==null||isNaN(a[0])||a[1]==null||isNaN(a[1])}function Bo(a){Io.registerAxisPointerClass("CartesianAxisPointer",sc),a.registerComponentModel(uc),a.registerComponentView(gc),a.registerPreprocessor(function(e){if(e){(!e.axisPointer||e.axisPointer.length===0)&&(e.axisPointer={});var t=e.axisPointer.link;t&&!X(t)&&(e.axisPointer.link=[t])}}),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,function(e,t){e.getComponent("axisPointer").coordSysAxesInfo=zv(e,t)}),a.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},pc)}function Ac(a){Ut(Qv),Ut(Bo)}function Cc(a,e){var t=ha(e.get("padding")),r=e.getItemStyle(["color","opacity"]);return r.fill=e.get("backgroundColor"),a=new St({shape:{x:a.x-t[3],y:a.y-t[0],width:a.width+t[1]+t[3],height:a.height+t[0]+t[2],r:e.get("borderRadius")},style:r,silent:!0,z2:-1}),a}var Dc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="tooltip",e.dependencies=["axisPointer"],e.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},e}(Lt);function Go(a){var e=a.get("confine");return e!=null?!!e:a.get("renderMode")==="richText"}function zo(a){if(ut.domSupported){for(var e=document.documentElement.style,t=0,r=a.length;t<r;t++)if(a[t]in e)return a[t]}}var Vo=zo(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Lc=zo(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function Fo(a,e){if(!a)return e;e=Gn(e,!0);var t=a.indexOf(e);return a=t===-1?e:"-"+a.slice(0,t)+"-"+e,a.toLowerCase()}function Tc(a,e){var t=a.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(a);return t?t[e]:null}var Ic=Fo(Lc,"transition"),Sa=Fo(Vo,"transform"),Mc="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(ut.transform3dSupported?"will-change:transform;":"");function Pc(a){return a=a==="left"?"right":a==="right"?"left":a==="top"?"bottom":"top",a}function kc(a,e,t){if(!Z(t)||t==="inside")return"";var r=a.get("backgroundColor"),i=a.get("borderWidth");e=se(e);var n=Pc(t),o=Math.max(Math.round(i)*1.5,6),s="",l=Sa+":",u;Ft(["left","right"],n)>-1?(s+="top:50%",l+="translateY(-50%) rotate("+(u=n==="left"?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(u=n==="top"?225:45)+"deg)");var h=u*Math.PI/180,v=o+i,f=v*Math.abs(Math.cos(h))+v*Math.abs(Math.sin(h)),c=Math.round(((f-Math.SQRT2*i)/2+Math.SQRT2*i-(f-v)/2)*100)/100;s+=";"+n+":-"+c+"px";var d=e+" solid "+i+"px;",g=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+l+";","border-bottom:"+d,"border-right:"+d,"background-color:"+r+";"];return'<div style="'+g.join("")+'"></div>'}function Oc(a,e){var t="cubic-bezier(0.23,1,0.32,1)",r=" "+a/2+"s "+t,i="opacity"+r+",visibility"+r;return e||(r=" "+a+"s "+t,i+=ut.transformSupported?","+Sa+r:",left"+r+",top"+r),Ic+":"+i}function tn(a,e,t){var r=a.toFixed(0)+"px",i=e.toFixed(0)+"px";if(!ut.transformSupported)return t?"top:"+i+";left:"+r+";":[["top",i],["left",r]];var n=ut.transform3dSupported,o="translate"+(n?"3d":"")+"("+r+","+i+(n?",0":"")+")";return t?"top:0;left:0;"+Sa+":"+o+";":[["top",0],["left",0],[Vo,o]]}function Ec(a){var e=[],t=a.get("fontSize"),r=a.getTextColor();r&&e.push("color:"+r),e.push("font:"+a.getFont());var i=bt(a.get("lineHeight"),Math.round(t*3/2));t&&e.push("line-height:"+i+"px");var n=a.get("textShadowColor"),o=a.get("textShadowBlur")||0,s=a.get("textShadowOffsetX")||0,l=a.get("textShadowOffsetY")||0;return n&&o&&e.push("text-shadow:"+s+"px "+l+"px "+o+"px "+n),M(["decoration","align"],function(u){var h=a.get(u);h&&e.push("text-"+u+":"+h)}),e.join(";")}function Rc(a,e,t){var r=[],i=a.get("transitionDuration"),n=a.get("backgroundColor"),o=a.get("shadowBlur"),s=a.get("shadowColor"),l=a.get("shadowOffsetX"),u=a.get("shadowOffsetY"),h=a.getModel("textStyle"),v=zn(a,"html"),f=l+"px "+u+"px "+o+"px "+s;return r.push("box-shadow:"+f),e&&i&&r.push(Oc(i,t)),n&&r.push("background-color:"+n),M(["width","color","radius"],function(c){var d="border-"+c,g=Gn(d),m=a.get(g);m!=null&&r.push(d+":"+m+(c==="color"?"":"px"))}),r.push(Ec(h)),v!=null&&r.push("padding:"+ha(v).join("px ")+"px"),r.join(";")+";"}function en(a,e,t,r,i){var n=e&&e.painter;if(t){var o=n&&n.getViewportRoot();o&&Sl(a,o,t,r,i)}else{a[0]=r,a[1]=i;var s=n&&n.getViewportRootOffset();s&&(a[0]+=s.offsetLeft,a[1]+=s.offsetTop)}a[2]=a[0]/e.getWidth(),a[3]=a[1]/e.getHeight()}var Nc=function(){function a(e,t){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,ut.wxa)return null;var r=document.createElement("div");r.domBelongToZr=!0,this.el=r;var i=this._zr=e.getZr(),n=t.appendTo,o=n&&(Z(n)?document.querySelector(n):Ha(n)?n:q(n)&&n(e.getDom()));en(this._styleCoord,i,o,e.getWidth()/2,e.getHeight()/2),(o||e.getDom()).appendChild(r),this._api=e,this._container=o;var s=this;r.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},r.onmousemove=function(l){if(l=l||window.event,!s._enterable){var u=i.handler,h=i.painter.getViewportRoot();bl(h,l,!0),u.dispatch("mousemove",l)}},r.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return a.prototype.update=function(e){if(!this._container){var t=this._api.getDom(),r=Tc(t,"position"),i=t.style;i.position!=="absolute"&&r!=="absolute"&&(i.position="relative")}var n=e.get("alwaysShowContent");n&&this._moveIfResized(),this._alwaysShowContent=n,this.el.className=e.get("className")||""},a.prototype.show=function(e,t){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var r=this.el,i=r.style,n=this._styleCoord;r.innerHTML?i.cssText=Mc+Rc(e,!this._firstShow,this._longHide)+tn(n[0],n[1],!0)+("border-color:"+se(t)+";")+(e.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},a.prototype.setContent=function(e,t,r,i,n){var o=this.el;if(e==null){o.innerHTML="";return}var s="";if(Z(n)&&r.get("trigger")==="item"&&!Go(r)&&(s=kc(r,i,n)),Z(e))o.innerHTML=e+s;else if(e){o.innerHTML="",X(e)||(e=[e]);for(var l=0;l<e.length;l++)Ha(e[l])&&e[l].parentNode!==o&&o.appendChild(e[l]);if(s&&o.childNodes.length){var u=document.createElement("div");u.innerHTML=s,o.appendChild(u)}}},a.prototype.setEnterable=function(e){this._enterable=e},a.prototype.getSize=function(){var e=this.el;return e?[e.offsetWidth,e.offsetHeight]:[0,0]},a.prototype.moveTo=function(e,t){if(this.el){var r=this._styleCoord;if(en(r,this._zr,this._container,e,t),r[0]!=null&&r[1]!=null){var i=this.el.style,n=tn(r[0],r[1]);M(n,function(o){i[o[0]]=o[1]})}}},a.prototype._moveIfResized=function(){var e=this._styleCoord[2],t=this._styleCoord[3];this.moveTo(e*this._zr.getWidth(),t*this._zr.getHeight())},a.prototype.hide=function(){var e=this,t=this.el.style;t.visibility="hidden",t.opacity="0",ut.transform3dSupported&&(t.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return e._longHide=!0},500)},a.prototype.hideLater=function(e){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(e?(this._hideDelay=e,this._show=!1,this._hideTimeout=setTimeout(nt(this.hide,this),e)):this.hide())},a.prototype.isShow=function(){return this._show},a.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var e=this.el.parentNode;e&&e.removeChild(this.el),this.el=this._container=null},a}(),Bc=function(){function a(e){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=e.getZr(),an(this._styleCoord,this._zr,e.getWidth()/2,e.getHeight()/2)}return a.prototype.update=function(e){var t=e.get("alwaysShowContent");t&&this._moveIfResized(),this._alwaysShowContent=t},a.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},a.prototype.setContent=function(e,t,r,i,n){var o=this;st(e)&&wl(""),this.el&&this._zr.remove(this.el);var s=r.getModel("textStyle");this.el=new _t({style:{rich:t.richTextStyles,text:e,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:s.get("textShadowColor"),fill:r.get(["textStyle","color"]),padding:zn(r,"richText"),verticalAlign:"top",align:"left"},z:r.get("z")}),M(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(u){o.el.style[u]=r.get(u)}),M(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(u){o.el.style[u]=s.get(u)||0}),this._zr.add(this.el);var l=this;this.el.on("mouseover",function(){l._enterable&&(clearTimeout(l._hideTimeout),l._show=!0),l._inContent=!0}),this.el.on("mouseout",function(){l._enterable&&l._show&&l.hideLater(l._hideDelay),l._inContent=!1})},a.prototype.setEnterable=function(e){this._enterable=e},a.prototype.getSize=function(){var e=this.el,t=this.el.getBoundingRect(),r=rn(e.style);return[t.width+r.left+r.right,t.height+r.top+r.bottom]},a.prototype.moveTo=function(e,t){var r=this.el;if(r){var i=this._styleCoord;an(i,this._zr,e,t),e=i[0],t=i[1];var n=r.style,o=Et(n.borderWidth||0),s=rn(n);r.x=e+o+s.left,r.y=t+o+s.top,r.markRedraw()}},a.prototype._moveIfResized=function(){var e=this._styleCoord[2],t=this._styleCoord[3];this.moveTo(e*this._zr.getWidth(),t*this._zr.getHeight())},a.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},a.prototype.hideLater=function(e){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(e?(this._hideDelay=e,this._show=!1,this._hideTimeout=setTimeout(nt(this.hide,this),e)):this.hide())},a.prototype.isShow=function(){return this._show},a.prototype.dispose=function(){this._zr.remove(this.el)},a}();function Et(a){return Math.max(0,a)}function rn(a){var e=Et(a.shadowBlur||0),t=Et(a.shadowOffsetX||0),r=Et(a.shadowOffsetY||0);return{left:Et(e-t),right:Et(e+t),top:Et(e-r),bottom:Et(e+r)}}function an(a,e,t,r){a[0]=t,a[1]=r,a[2]=a[0]/e.getWidth(),a[3]=a[1]/e.getHeight()}var Gc=new St({shape:{x:-1,y:-1,width:2,height:2}}),zc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){if(!(ut.node||!r.getDom())){var i=t.getComponent("tooltip"),n=this._renderMode=Al(i.get("renderMode"));this._tooltipContent=n==="richText"?new Bc(r):new Nc(r,{appendTo:i.get("appendToBody",!0)?"body":i.get("appendTo",!0)})}},e.prototype.render=function(t,r,i){if(!(ut.node||!i.getDom())){this.group.removeAll(),this._tooltipModel=t,this._ecModel=r,this._api=i;var n=this._tooltipContent;n.update(t),n.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&t.get("transitionDuration")?Bn(this,"_updatePosition",50,"fixRate"):Xr(this,"_updatePosition")}},e.prototype._initGlobalListener=function(){var t=this._tooltipModel,r=t.get("triggerOn");Ro("itemTooltip",this._api,nt(function(i,n,o){r!=="none"&&(r.indexOf(i)>=0?this._tryShow(n,o):i==="leave"&&this._hide(o))},this))},e.prototype._keepShow=function(){var t=this._tooltipModel,r=this._ecModel,i=this._api,n=t.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&n!=="none"&&n!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!i.isDisposed()&&o.manuallyShowTip(t,r,i,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},e.prototype.manuallyShowTip=function(t,r,i,n){if(!(n.from===this.uid||ut.node||!i.getDom())){var o=nn(n,i);this._ticket="";var s=n.dataByCoordSys,l=Wc(n,r,i);if(l){var u=l.el.getBoundingRect().clone();u.applyTransform(l.el.transform),this._tryShow({offsetX:u.x+u.width/2,offsetY:u.y+u.height/2,target:l.el,position:n.position,positionDefault:"bottom"},o)}else if(n.tooltip&&n.x!=null&&n.y!=null){var h=Gc;h.x=n.x,h.y=n.y,h.update(),j(h).tooltipConfig={name:null,option:n.tooltip},this._tryShow({offsetX:n.x,offsetY:n.y,target:h},o)}else if(s)this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,dataByCoordSys:s,tooltipOption:n.tooltipOption},o);else if(n.seriesIndex!=null){if(this._manuallyAxisShowTip(t,r,i,n))return;var v=No(n,r),f=v.point[0],c=v.point[1];f!=null&&c!=null&&this._tryShow({offsetX:f,offsetY:c,target:v.el,position:n.position,positionDefault:"bottom"},o)}else n.x!=null&&n.y!=null&&(i.dispatchAction({type:"updateAxisPointer",x:n.x,y:n.y}),this._tryShow({offsetX:n.x,offsetY:n.y,position:n.position,target:i.getZr().findHover(n.x,n.y).target},o))}},e.prototype.manuallyHideTip=function(t,r,i,n){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,n.from!==this.uid&&this._hide(nn(n,i))},e.prototype._manuallyAxisShowTip=function(t,r,i,n){var o=n.seriesIndex,s=n.dataIndex,l=r.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||l==null)){var u=r.getSeriesByIndex(o);if(u){var h=u.getData(),v=we([h.getItemModel(s),u,(u.coordinateSystem||{}).model],this._tooltipModel);if(v.get("trigger")==="axis")return i.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:n.position}),!0}}},e.prototype._tryShow=function(t,r){var i=t.target,n=this._tooltipModel;if(n){this._lastX=t.offsetX,this._lastY=t.offsetY;var o=t.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,t);else if(i){var s=j(i);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var l,u;Cl(i,function(h){if(j(h).dataIndex!=null)return l=h,!0;if(j(h).tooltipConfig!=null)return u=h,!0},!0),l?this._showSeriesItemTooltip(t,l,r):u?this._showComponentItemTooltip(t,u,r):this._hide(r)}else this._lastDataByCoordSys=null,this._hide(r)}},e.prototype._showOrMove=function(t,r){var i=t.get("showDelay");r=nt(r,this),clearTimeout(this._showTimout),i>0?this._showTimout=setTimeout(r,i):r()},e.prototype._showAxisTooltip=function(t,r){var i=this._ecModel,n=this._tooltipModel,o=[r.offsetX,r.offsetY],s=we([r.tooltipOption],n),l=this._renderMode,u=[],h=Wa("section",{blocks:[],noHeader:!0}),v=[],f=new pr;M(t,function(y){M(y.dataByAxis,function(_){var x=i.getComponent(_.axisDim+"Axis",_.axisIndex),w=_.value;if(!(!x||w==null)){var b=Oo(w,x.axis,i,_.seriesDataIndices,_.valueLabelOpt),S=Wa("section",{header:b,noHeader:!Dl(b),sortBlocks:!0,blocks:[]});h.blocks.push(S),M(_.seriesDataIndices,function(A){var L=i.getSeriesByIndex(A.seriesIndex),D=A.dataIndexInside,C=L.getDataParams(D);if(!(C.dataIndex<0)){C.axisDim=_.axisDim,C.axisIndex=_.axisIndex,C.axisType=_.axisType,C.axisId=_.axisId,C.axisValue=ga(x.axis,{value:w}),C.axisValueLabel=b,C.marker=f.makeTooltipMarker("item",se(C.color),l);var I=Ua(L.formatTooltip(D,!0,null)),T=I.frag;if(T){var k=we([L],n).get("valueFormatter");S.blocks.push(k?W({valueFormatter:k},T):T)}I.text&&v.push(I.text),u.push(C)}})}})}),h.blocks.reverse(),v.reverse();var c=r.position,d=s.get("order"),g=Xa(h,f,l,d,i.get("useUTC"),s.get("textStyle"));g&&v.unshift(g);var m=l==="richText"?`

`:"<br/>",p=v.join(m);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(t,u)?this._updatePosition(s,c,o[0],o[1],this._tooltipContent,u):this._showTooltipContent(s,p,u,Math.random()+"",o[0],o[1],c,null,f)})},e.prototype._showSeriesItemTooltip=function(t,r,i){var n=this._ecModel,o=j(r),s=o.seriesIndex,l=n.getSeriesByIndex(s),u=o.dataModel||l,h=o.dataIndex,v=o.dataType,f=u.getData(v),c=this._renderMode,d=t.positionDefault,g=we([f.getItemModel(h),u,l&&(l.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),m=g.get("trigger");if(!(m!=null&&m!=="item")){var p=u.getDataParams(h,v),y=new pr;p.marker=y.makeTooltipMarker("item",se(p.color),c);var _=Ua(u.formatTooltip(h,!1,v)),x=g.get("order"),w=g.get("valueFormatter"),b=_.frag,S=b?Xa(w?W({valueFormatter:w},b):b,y,c,x,n.get("useUTC"),g.get("textStyle")):_.text,A="item_"+u.name+"_"+h;this._showOrMove(g,function(){this._showTooltipContent(g,S,p,A,t.offsetX,t.offsetY,t.position,t.target,y)}),i({type:"showTip",dataIndexInside:h,dataIndex:f.getRawIndex(h),seriesIndex:s,from:this.uid})}},e.prototype._showComponentItemTooltip=function(t,r,i){var n=this._renderMode==="html",o=j(r),s=o.tooltipConfig,l=s.option||{},u=l.encodeHTMLContent;if(Z(l)){var h=l;l={content:h,formatter:h},u=!0}u&&n&&l.content&&(l=ie(l),l.content=Ll(l.content));var v=[l],f=this._ecModel.getComponent(o.componentMainType,o.componentIndex);f&&v.push(f),v.push({formatter:l.content});var c=t.positionDefault,d=we(v,this._tooltipModel,c?{position:c}:null),g=d.get("content"),m=Math.random()+"",p=new pr;this._showOrMove(d,function(){var y=ie(d.get("formatterParams")||{});this._showTooltipContent(d,g,y,m,t.offsetX,t.offsetY,t.position,r,p)}),i({type:"showTip",from:this.uid})},e.prototype._showTooltipContent=function(t,r,i,n,o,s,l,u,h){if(this._ticket="",!(!t.get("showContent")||!t.get("show"))){var v=this._tooltipContent;v.setEnterable(t.get("enterable"));var f=t.get("formatter");l=l||t.get("position");var c=r,d=this._getNearestPoint([o,s],i,t.get("trigger"),t.get("borderColor")),g=d.color;if(f)if(Z(f)){var m=t.ecModel.get("useUTC"),p=X(i)?i[0]:i,y=p&&p.axisType&&p.axisType.indexOf("time")>=0;c=f,y&&(c=gn(p.axisValue,c,m)),c=Tl(c,i,!0)}else if(q(f)){var _=nt(function(x,w){x===this._ticket&&(v.setContent(w,h,t,g,l),this._updatePosition(t,l,o,s,v,i,u))},this);this._ticket=n,c=f(i,n,_)}else c=f;v.setContent(c,h,t,g,l),v.show(t,g),this._updatePosition(t,l,o,s,v,i,u)}},e.prototype._getNearestPoint=function(t,r,i,n){if(i==="axis"||X(r))return{color:n||(this._renderMode==="html"?"#fff":"none")};if(!X(r))return{color:n||r.color||r.borderColor}},e.prototype._updatePosition=function(t,r,i,n,o,s,l){var u=this._api.getWidth(),h=this._api.getHeight();r=r||t.get("position");var v=o.getSize(),f=t.get("align"),c=t.get("verticalAlign"),d=l&&l.getBoundingRect().clone();if(l&&d.applyTransform(l.transform),q(r)&&(r=r([i,n],s,o.el,d,{viewSize:[u,h],contentSize:v.slice()})),X(r))i=it(r[0],u),n=it(r[1],h);else if(st(r)){var g=r;g.width=v[0],g.height=v[1];var m=le(g,{width:u,height:h});i=m.x,n=m.y,f=null,c=null}else if(Z(r)&&l){var p=Hc(r,d,v,t.get("borderWidth"));i=p[0],n=p[1]}else{var p=Vc(i,n,o,u,h,f?null:20,c?null:20);i=p[0],n=p[1]}if(f&&(i-=on(f)?v[0]/2:f==="right"?v[0]:0),c&&(n-=on(c)?v[1]/2:c==="bottom"?v[1]:0),Go(t)){var p=Fc(i,n,o,u,h);i=p[0],n=p[1]}o.moveTo(i,n)},e.prototype._updateContentNotChangedOnAxis=function(t,r){var i=this._lastDataByCoordSys,n=this._cbParamsList,o=!!i&&i.length===t.length;return o&&M(i,function(s,l){var u=s.dataByAxis||[],h=t[l]||{},v=h.dataByAxis||[];o=o&&u.length===v.length,o&&M(u,function(f,c){var d=v[c]||{},g=f.seriesDataIndices||[],m=d.seriesDataIndices||[];o=o&&f.value===d.value&&f.axisType===d.axisType&&f.axisId===d.axisId&&g.length===m.length,o&&M(g,function(p,y){var _=m[y];o=o&&p.seriesIndex===_.seriesIndex&&p.dataIndex===_.dataIndex}),n&&M(f.seriesDataIndices,function(p){var y=p.seriesIndex,_=r[y],x=n[y];_&&x&&x.data!==_.data&&(o=!1)})})}),this._lastDataByCoordSys=t,this._cbParamsList=r,!!o},e.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},e.prototype.dispose=function(t,r){ut.node||!r.getDom()||(Xr(this,"_updatePosition"),this._tooltipContent.dispose(),ea("itemTooltip",r))},e.type="tooltip",e}(Wt);function we(a,e,t){var r=e.ecModel,i;t?(i=new Rt(t,r,r),i=new Rt(e.option,i,r)):i=e;for(var n=a.length-1;n>=0;n--){var o=a[n];o&&(o instanceof Rt&&(o=o.get("tooltip",!0)),Z(o)&&(o={formatter:o}),o&&(i=new Rt(o,i,r)))}return i}function nn(a,e){return a.dispatchAction||nt(e.dispatchAction,e)}function Vc(a,e,t,r,i,n,o){var s=t.getSize(),l=s[0],u=s[1];return n!=null&&(a+l+n+2>r?a-=l+n:a+=n),o!=null&&(e+u+o>i?e-=u+o:e+=o),[a,e]}function Fc(a,e,t,r,i){var n=t.getSize(),o=n[0],s=n[1];return a=Math.min(a+o,r)-o,e=Math.min(e+s,i)-s,a=Math.max(a,0),e=Math.max(e,0),[a,e]}function Hc(a,e,t,r){var i=t[0],n=t[1],o=Math.ceil(Math.SQRT2*r)+8,s=0,l=0,u=e.width,h=e.height;switch(a){case"inside":s=e.x+u/2-i/2,l=e.y+h/2-n/2;break;case"top":s=e.x+u/2-i/2,l=e.y-n-o;break;case"bottom":s=e.x+u/2-i/2,l=e.y+h+o;break;case"left":s=e.x-i-o,l=e.y+h/2-n/2;break;case"right":s=e.x+u+o,l=e.y+h/2-n/2}return[s,l]}function on(a){return a==="center"||a==="middle"}function Wc(a,e,t){var r=Il(a).queryOptionMap,i=r.keys()[0];if(!(!i||i==="series")){var n=Ml(e,i,r.get(i),{useDefault:!1,enableAll:!1,enableNone:!1}),o=n.models[0];if(o){var s=t.getViewOfComponentModel(o),l;if(s.group.traverse(function(u){var h=j(u).tooltipConfig;if(h&&h.name===a.name)return l=u,!0}),l)return{componentMainType:i,componentIndex:o.componentIndex,el:l}}}}function Uc(a){Ut(Bo),a.registerComponentModel(Dc),a.registerComponentView(zc),a.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Ya),a.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Ya)}var Xc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode={type:"box",ignoreSize:!0},t}return e.type="title",e.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},e}(Lt),Yc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){if(this.group.removeAll(),!!t.get("show")){var n=this.group,o=t.getModel("textStyle"),s=t.getModel("subtextStyle"),l=t.get("textAlign"),u=bt(t.get("textBaseline"),t.get("textVerticalAlign")),h=new _t({style:he(o,{text:t.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),v=h.getBoundingRect(),f=t.get("subtext"),c=new _t({style:he(s,{text:f,fill:s.getTextColor(),y:v.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),g=t.get("sublink"),m=t.get("triggerEvent",!0);h.silent=!d&&!m,c.silent=!g&&!m,d&&h.on("click",function(){Za(d,"_"+t.get("target"))}),g&&c.on("click",function(){Za(g,"_"+t.get("subtarget"))}),j(h).eventData=j(c).eventData=m?{componentType:"title",componentIndex:t.componentIndex}:null,n.add(h),f&&n.add(c);var p=n.getBoundingRect(),y=t.getBoxLayoutParams();y.width=p.width,y.height=p.height;var _=le(y,{width:i.getWidth(),height:i.getHeight()},t.get("padding"));l||(l=t.get("left")||t.get("right"),l==="middle"&&(l="center"),l==="right"?_.x+=_.width:l==="center"&&(_.x+=_.width/2)),u||(u=t.get("top")||t.get("bottom"),u==="center"&&(u="middle"),u==="bottom"?_.y+=_.height:u==="middle"&&(_.y+=_.height/2),u=u||"top"),n.x=_.x,n.y=_.y,n.markRedraw();var x={align:l,verticalAlign:u};h.setStyle(x),c.setStyle(x),p=n.getBoundingRect();var w=_.margin,b=t.getItemStyle(["color","opacity"]);b.fill=t.get("backgroundColor");var S=new St({shape:{x:p.x-w[3],y:p.y-w[0],width:p.width+w[1]+w[3],height:p.height+w[0]+w[2],r:t.get("borderRadius")},style:b,subPixelOptimize:!0,silent:!0});n.add(S)}},e.type="title",e}(Wt);function Zc(a){a.registerComponentModel(Xc),a.registerComponentView(Yc)}var $c=function(a,e){if(e==="all")return{type:"all",title:a.getLocaleModel().get(["legend","selector","all"])};if(e==="inverse")return{type:"inverse",title:a.getLocaleModel().get(["legend","selector","inverse"])}},ra=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode={type:"box",ignoreSize:!0},t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{},this._updateSelector(t)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),this._updateSelector(t)},e.prototype._updateSelector=function(t){var r=t.selector,i=this.ecModel;r===!0&&(r=t.selector=["all","inverse"]),X(r)&&M(r,function(n,o){Z(n)&&(n={type:n}),r[o]=ct(n,$c(i,n.type))})},e.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&this.get("selectedMode")==="single"){for(var r=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),r=!0;break}}!r&&this.select(t[0].get("name"))}},e.prototype._updateData=function(t){var r=[],i=[];t.eachRawSeries(function(l){var u=l.name;i.push(u);var h;if(l.legendVisualProvider){var v=l.legendVisualProvider,f=v.getAllNames();t.isSeriesFiltered(l)||(i=i.concat(f)),f.length?r=r.concat(f):h=!0}else h=!0;h&&Pl(l)&&r.push(l.name)}),this._availableNames=i;var n=this.get("data")||r,o=ft(),s=Y(n,function(l){return(Z(l)||Tt(l))&&(l={name:l}),o.get(l.name)?null:(o.set(l.name,!0),new Rt(l,this,this.ecModel))},this);this._data=ne(s,function(l){return!!l})},e.prototype.getData=function(){return this._data},e.prototype.select=function(t){var r=this.option.selected,i=this.get("selectedMode");if(i==="single"){var n=this._data;M(n,function(o){r[o.get("name")]=!1})}r[t]=!0},e.prototype.unSelect=function(t){this.get("selectedMode")!=="single"&&(this.option.selected[t]=!1)},e.prototype.toggleSelected=function(t){var r=this.option.selected;r.hasOwnProperty(t)||(r[t]=!0),this[r[t]?"unSelect":"select"](t)},e.prototype.allSelect=function(){var t=this._data,r=this.option.selected;M(t,function(i){r[i.get("name",!0)]=!0})},e.prototype.inverseSelect=function(){var t=this._data,r=this.option.selected;M(t,function(i){var n=i.get("name",!0);r.hasOwnProperty(n)||(r[n]=!0),r[n]=!r[n]})},e.prototype.isSelected=function(t){var r=this.option.selected;return!(r.hasOwnProperty(t)&&!r[t])&&Ft(this._availableNames,t)>=0},e.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},e.type="legend.plain",e.dependencies=["series"],e.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},e}(Lt),ae=Q,aa=M,Ke=yt,Ho=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.newlineDisabled=!1,t}return e.prototype.init=function(){this.group.add(this._contentGroup=new Ke),this.group.add(this._selectorGroup=new Ke),this._isFirstRender=!0},e.prototype.getContentGroup=function(){return this._contentGroup},e.prototype.getSelectorGroup=function(){return this._selectorGroup},e.prototype.render=function(t,r,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!t.get("show",!0)){var o=t.get("align"),s=t.get("orient");(!o||o==="auto")&&(o=t.get("left")==="right"&&s==="vertical"?"right":"left");var l=t.get("selector",!0),u=t.get("selectorPosition",!0);l&&(!u||u==="auto")&&(u=s==="horizontal"?"end":"start"),this.renderInner(o,t,r,i,l,s,u);var h=t.getBoxLayoutParams(),v={width:i.getWidth(),height:i.getHeight()},f=t.get("padding"),c=le(h,v,f),d=this.layoutInner(t,o,c,n,l,u),g=le(lt({width:d.width,height:d.height},h),v,f);this.group.x=g.x-d.x,this.group.y=g.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=Cc(d,t))}},e.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},e.prototype.renderInner=function(t,r,i,n,o,s,l){var u=this.getContentGroup(),h=ft(),v=r.get("selectedMode"),f=[];i.eachRawSeries(function(c){!c.get("legendHoverLink")&&f.push(c.id)}),aa(r.getData(),function(c,d){var g=c.get("name");if(!this.newlineDisabled&&(g===""||g===`
`)){var m=new Ke;m.newline=!0,u.add(m);return}var p=i.getSeriesByName(g)[0];if(!h.get(g))if(p){var y=p.getData(),_=y.getVisual("legendLineStyle")||{},x=y.getVisual("legendIcon"),w=y.getVisual("style"),b=this._createItem(p,g,d,c,r,t,_,w,x,v,n);b.on("click",ae(sn,g,null,n,f)).on("mouseover",ae(ia,p.name,null,n,f)).on("mouseout",ae(na,p.name,null,n,f)),i.ssr&&b.eachChild(function(S){var A=j(S);A.seriesIndex=p.seriesIndex,A.dataIndex=d,A.ssrType="legend"}),h.set(g,!0)}else i.eachRawSeries(function(S){if(!h.get(g)&&S.legendVisualProvider){var A=S.legendVisualProvider;if(!A.containName(g))return;var L=A.indexOfName(g),D=A.getItemVisual(L,"style"),C=A.getItemVisual(L,"legendIcon"),I=kl(D.fill);I&&I[3]===0&&(I[3]=.2,D=W(W({},D),{fill:Ol(I,"rgba")}));var T=this._createItem(S,g,d,c,r,t,{},D,C,v,n);T.on("click",ae(sn,null,g,n,f)).on("mouseover",ae(ia,null,g,n,f)).on("mouseout",ae(na,null,g,n,f)),i.ssr&&T.eachChild(function(k){var P=j(k);P.seriesIndex=S.seriesIndex,P.dataIndex=d,P.ssrType="legend"}),h.set(g,!0)}},this)},this),o&&this._createSelector(o,r,n,s,l)},e.prototype._createSelector=function(t,r,i,n,o){var s=this.getSelectorGroup();aa(t,function(u){var h=u.type,v=new _t({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){i.dispatchAction({type:h==="all"?"legendAllSelect":"legendInverseSelect",legendId:r.id})}});s.add(v);var f=r.getModel("selectorLabel"),c=r.getModel(["emphasis","selectorLabel"]);Re(v,{normal:f,emphasis:c},{defaultText:u.title}),$a(v)})},e.prototype._createItem=function(t,r,i,n,o,s,l,u,h,v,f){var c=t.visualDrawType,d=o.get("itemWidth"),g=o.get("itemHeight"),m=o.isSelected(r),p=n.get("symbolRotate"),y=n.get("symbolKeepAspect"),_=n.get("icon");h=_||h||"roundRect";var x=qc(h,n,l,u,c,m,f),w=new Ke,b=n.getModel("textStyle");if(q(t.getLegendIcon)&&(!_||_==="inherit"))w.add(t.getLegendIcon({itemWidth:d,itemHeight:g,icon:h,iconRotate:p,itemStyle:x.itemStyle,lineStyle:x.lineStyle,symbolKeepAspect:y}));else{var S=_==="inherit"&&t.getData().getVisual("symbol")?p==="inherit"?t.getData().getVisual("symbolRotate"):p:0;w.add(Kc({itemWidth:d,itemHeight:g,icon:h,iconRotate:S,itemStyle:x.itemStyle,symbolKeepAspect:y}))}var A=s==="left"?d+5:-5,L=s,D=o.get("formatter"),C=r;Z(D)&&D?C=D.replace("{name}",r??""):q(D)&&(C=D(r));var I=m?b.getTextColor():n.get("inactiveColor");w.add(new _t({style:he(b,{text:C,x:A,y:g/2,fill:I,align:L,verticalAlign:"middle"},{inheritColor:I})}));var T=new St({shape:w.getBoundingRect(),style:{fill:"transparent"}}),k=n.getModel("tooltip");return k.get("show")&&ua({el:T,componentModel:o,itemName:r,itemTooltipOption:k.option}),w.add(T),w.eachChild(function(P){P.silent=!0}),T.silent=!v,this.getContentGroup().add(w),$a(w),w.__legendDataIndex=i,w},e.prototype.layoutInner=function(t,r,i,n,o,s){var l=this.getContentGroup(),u=this.getSelectorGroup();Le(t.get("orient"),l,t.get("itemGap"),i.width,i.height);var h=l.getBoundingRect(),v=[-h.x,-h.y];if(u.markRedraw(),l.markRedraw(),o){Le("horizontal",u,t.get("selectorItemGap",!0));var f=u.getBoundingRect(),c=[-f.x,-f.y],d=t.get("selectorButtonGap",!0),g=t.getOrient().index,m=g===0?"width":"height",p=g===0?"height":"width",y=g===0?"y":"x";s==="end"?c[g]+=h[m]+d:v[g]+=f[m]+d,c[1-g]+=h[p]/2-f[p]/2,u.x=c[0],u.y=c[1],l.x=v[0],l.y=v[1];var _={x:0,y:0};return _[m]=h[m]+d+f[m],_[p]=Math.max(h[p],f[p]),_[y]=Math.min(0,f[y]+c[1-g]),_}else return l.x=v[0],l.y=v[1],this.group.getBoundingRect()},e.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},e.type="legend.plain",e}(Wt);function qc(a,e,t,r,i,n,o){function s(m,p){m.lineWidth==="auto"&&(m.lineWidth=p.lineWidth>0?2:0),aa(m,function(y,_){m[_]==="inherit"&&(m[_]=p[_])})}var l=e.getModel("itemStyle"),u=l.getItemStyle(),h=a.lastIndexOf("empty",0)===0?"fill":"stroke",v=l.getShallow("decal");u.decal=!v||v==="inherit"?r.decal:El(v,o),u.fill==="inherit"&&(u.fill=r[i]),u.stroke==="inherit"&&(u.stroke=r[h]),u.opacity==="inherit"&&(u.opacity=(i==="fill"?r:t).opacity),s(u,r);var f=e.getModel("lineStyle"),c=f.getLineStyle();if(s(c,t),u.fill==="auto"&&(u.fill=r.fill),u.stroke==="auto"&&(u.stroke=r.fill),c.stroke==="auto"&&(c.stroke=r.fill),!n){var d=e.get("inactiveBorderWidth"),g=u[h];u.lineWidth=d==="auto"?r.lineWidth>0&&g?2:0:u.lineWidth,u.fill=e.get("inactiveColor"),u.stroke=e.get("inactiveBorderColor"),c.stroke=f.get("inactiveColor"),c.lineWidth=f.get("inactiveWidth")}return{itemStyle:u,lineStyle:c}}function Kc(a){var e=a.icon||"roundRect",t=Ie(e,0,0,a.itemWidth,a.itemHeight,a.itemStyle.fill,a.symbolKeepAspect);return t.setStyle(a.itemStyle),t.rotation=(a.iconRotate||0)*Math.PI/180,t.setOrigin([a.itemWidth/2,a.itemHeight/2]),e.indexOf("empty")>-1&&(t.style.stroke=t.style.fill,t.style.fill="#fff",t.style.lineWidth=2),t}function sn(a,e,t,r){na(a,e,t,r),t.dispatchAction({type:"legendToggleSelect",name:a??e}),ia(a,e,t,r)}function Wo(a){for(var e=a.getZr().storage.getDisplayList(),t,r=0,i=e.length;r<i&&!(t=e[r].states.emphasis);)r++;return t&&t.hoverLayer}function ia(a,e,t,r){Wo(t)||t.dispatchAction({type:"highlight",seriesName:a,name:e,excludeSeriesId:r})}function na(a,e,t,r){Wo(t)||t.dispatchAction({type:"downplay",seriesName:a,name:e,excludeSeriesId:r})}function jc(a){var e=a.findComponents({mainType:"legend"});e&&e.length&&a.filterSeries(function(t){for(var r=0;r<e.length;r++)if(!e[r].isSelected(t.name))return!1;return!0})}function Ae(a,e,t){var r=a==="allSelect"||a==="inverseSelect",i={},n=[];t.eachComponent({mainType:"legend",query:e},function(s){r?s[a]():s[a](e.name),ln(s,i),n.push(s.componentIndex)});var o={};return t.eachComponent("legend",function(s){M(i,function(l,u){s[l?"select":"unSelect"](u)}),ln(s,o)}),r?{selected:o,legendIndex:n}:{name:e.name,selected:o}}function ln(a,e){var t=e||{};return M(a.getData(),function(r){var i=r.get("name");if(!(i===`
`||i==="")){var n=a.isSelected(i);Rl(t,i)?t[i]=t[i]&&n:t[i]=n}}),t}function Jc(a){a.registerAction("legendToggleSelect","legendselectchanged",Q(Ae,"toggleSelected")),a.registerAction("legendAllSelect","legendselectall",Q(Ae,"allSelect")),a.registerAction("legendInverseSelect","legendinverseselect",Q(Ae,"inverseSelect")),a.registerAction("legendSelect","legendselected",Q(Ae,"select")),a.registerAction("legendUnSelect","legendunselected",Q(Ae,"unSelect"))}function Uo(a){a.registerComponentModel(ra),a.registerComponentView(Ho),a.registerProcessor(a.PRIORITY.PROCESSOR.SERIES_FILTER,jc),a.registerSubTypeDefaulter("legend",function(){return"plain"}),Jc(a)}var Qc=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},e.prototype.init=function(t,r,i){var n=kn(t);a.prototype.init.call(this,t,r,i),un(this,t,n)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),un(this,this.option,t)},e.type="legend.scroll",e.defaultOption=Pn(ra.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),e}(ra);function un(a,e,t){var r=a.getOrient(),i=[1,1];i[r.index]=0,On(e,t,{type:"box",ignoreSize:!!i})}var hn=yt,Rr=["width","height"],Nr=["x","y"],tf=function(a){G(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.newlineDisabled=!0,t._currentIndex=0,t}return e.prototype.init=function(){a.prototype.init.call(this),this.group.add(this._containerGroup=new hn),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new hn)},e.prototype.resetInner=function(){a.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},e.prototype.renderInner=function(t,r,i,n,o,s,l){var u=this;a.prototype.renderInner.call(this,t,r,i,n,o,s,l);var h=this._controllerGroup,v=r.get("pageIconSize",!0),f=X(v)?v:[v,v];d("pagePrev",0);var c=r.getModel("pageTextStyle");h.add(new _t({name:"pageText",style:{text:"xx/xx",fill:c.getTextColor(),font:c.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(g,m){var p=g+"DataIndex",y=Nn(r.get("pageIcons",!0)[r.getOrient().name][m],{onclick:nt(u._pageGo,u,p,r,n)},{x:-f[0]/2,y:-f[1]/2,width:f[0],height:f[1]});y.name=g,h.add(y)}},e.prototype.layoutInner=function(t,r,i,n,o,s){var l=this.getSelectorGroup(),u=t.getOrient().index,h=Rr[u],v=Nr[u],f=Rr[1-u],c=Nr[1-u];o&&Le("horizontal",l,t.get("selectorItemGap",!0));var d=t.get("selectorButtonGap",!0),g=l.getBoundingRect(),m=[-g.x,-g.y],p=ie(i);o&&(p[h]=i[h]-g[h]-d);var y=this._layoutContentAndController(t,n,p,u,h,f,c,v);if(o){if(s==="end")m[u]+=y[h]+d;else{var _=g[h]+d;m[u]-=_,y[v]-=_}y[h]+=g[h]+d,m[1-u]+=y[c]+y[f]/2-g[f]/2,y[f]=Math.max(y[f],g[f]),y[c]=Math.min(y[c],g[c]+m[1-u]),l.x=m[0],l.y=m[1],l.markRedraw()}return y},e.prototype._layoutContentAndController=function(t,r,i,n,o,s,l,u){var h=this.getContentGroup(),v=this._containerGroup,f=this._controllerGroup;Le(t.get("orient"),h,t.get("itemGap"),n?i.width:null,n?null:i.height),Le("horizontal",f,t.get("pageButtonItemGap",!0));var c=h.getBoundingRect(),d=f.getBoundingRect(),g=this._showController=c[o]>i[o],m=[-c.x,-c.y];r||(m[n]=h[u]);var p=[0,0],y=[-d.x,-d.y],_=bt(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(g){var x=t.get("pageButtonPosition",!0);x==="end"?y[n]+=i[o]-d[o]:p[n]+=d[o]+_}y[1-n]+=c[s]/2-d[s]/2,h.setPosition(m),v.setPosition(p),f.setPosition(y);var w={x:0,y:0};if(w[o]=g?i[o]:c[o],w[s]=Math.max(c[s],d[s]),w[l]=Math.min(0,d[l]+y[1-n]),v.__rectSize=i[o],g){var b={x:0,y:0};b[o]=Math.max(i[o]-d[o]-_,0),b[s]=w[s],v.setClipPath(new St({shape:b})),v.__rectSize=b[o]}else f.eachChild(function(A){A.attr({invisible:!0,silent:!0})});var S=this._getPageInfo(t);return S.pageIndex!=null&&ht(h,{x:S.contentPosition[0],y:S.contentPosition[1]},g?t:null),this._updatePageInfoView(t,S),w},e.prototype._pageGo=function(t,r,i){var n=this._getPageInfo(r)[t];n!=null&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:r.id})},e.prototype._updatePageInfoView=function(t,r){var i=this._controllerGroup;M(["pagePrev","pageNext"],function(h){var v=h+"DataIndex",f=r[v]!=null,c=i.childOfName(h);c&&(c.setStyle("fill",f?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),c.cursor=f?"pointer":"default")});var n=i.childOfName("pageText"),o=t.get("pageFormatter"),s=r.pageIndex,l=s!=null?s+1:0,u=r.pageCount;n&&o&&n.setStyle("text",Z(o)?o.replace("{current}",l==null?"":l+"").replace("{total}",u==null?"":u+""):o({current:l,total:u}))},e.prototype._getPageInfo=function(t){var r=t.get("scrollDataIndex",!0),i=this.getContentGroup(),n=this._containerGroup.__rectSize,o=t.getOrient().index,s=Rr[o],l=Nr[o],u=this._findTargetItemIndex(r),h=i.children(),v=h[u],f=h.length,c=f?1:0,d={contentPosition:[i.x,i.y],pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!v)return d;var g=x(v);d.contentPosition[o]=-g.s;for(var m=u+1,p=g,y=g,_=null;m<=f;++m)_=x(h[m]),(!_&&y.e>p.s+n||_&&!w(_,p.s))&&(y.i>p.i?p=y:p=_,p&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=p.i),++d.pageCount)),y=_;for(var m=u-1,p=g,y=g,_=null;m>=-1;--m)_=x(h[m]),(!_||!w(y,_.s))&&p.i<y.i&&(y=p,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=p.i),++d.pageCount,++d.pageIndex),p=_;return d;function x(b){if(b){var S=b.getBoundingRect(),A=S[l]+b[l];return{s:A,e:A+S[s],i:b.__legendDataIndex}}}function w(b,S){return b.e>=S&&b.s<=S+n}},e.prototype._findTargetItemIndex=function(t){if(!this._showController)return 0;var r,i=this.getContentGroup(),n;return i.eachChild(function(o,s){var l=o.__legendDataIndex;n==null&&l!=null&&(n=s),l===t&&(r=s)}),r??n},e.type="legend.scroll",e}(Ho);function ef(a){a.registerAction("legendScroll","legendscroll",function(e,t){var r=e.scrollDataIndex;r!=null&&t.eachComponent({mainType:"legend",subType:"scroll",query:e},function(i){i.setScrollDataIndex(r)})})}function rf(a){Ut(Uo),a.registerComponentModel(Qc),a.registerComponentView(tf),ef(a)}function af(a){Ut(Uo),Ut(rf)}const nf={class:"dashboard-container"},of={class:"stat-icon"},sf={class:"stat-content"},lf={class:"stat-value"},uf={class:"stat-label"},hf={class:"dashboard-card"},vf={class:"card-header"},cf={class:"chart-container"},ff={class:"dashboard-card"},df={class:"chart-container"},gf={class:"dashboard-card"},pf={class:"card-header"},mf={class:"dashboard-card"},yf={class:"card-header"},_f={class:"dashboard-card"},xf={class:"card-header"},bf={class:"status-item"},Sf={class:"status-content"},wf={class:"status-name"},Af={class:"status-value"},Cf=Nl({__name:"Dashboard",setup(a){Ut([wh,Zh,bv,lv,Zc,Uc,af,Ac]);const e=ee("30d"),t=ee(!1),r=ee([{key:"users",label:"总用户数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"User",gradient:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},{key:"novels",label:"小说总数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"Reading",gradient:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"},{key:"memberCodes",label:"会员码总数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"Postcard",gradient:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"},{key:"syncRecords",label:"同步记录数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"Refresh",gradient:"linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"}]),i=ee([]),n=ee([]),o=ee([{name:"API服务",value:"正常运行",status:"success",icon:"CircleCheckFilled"},{name:"数据库",value:"连接正常",status:"success",icon:"CircleCheckFilled"},{name:"存储空间",value:"78% 已使用",status:"warning",icon:"WarningFilled"}]),s=Ka(()=>({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["1/15","1/16","1/17","1/18","1/19","1/20","1/21"]},yAxis:{type:"value"},series:[{data:[120,132,101,134,90,230,210],type:"line",smooth:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.05)"}]}},lineStyle:{color:"#409eff"}}]})),l=Ka(()=>({tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{type:"pie",radius:"50%",data:[{value:1048,name:"都市"},{value:735,name:"玄幻"},{value:580,name:"科幻"},{value:484,name:"历史"},{value:300,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})),u=c=>Zl(c).format("MM-DD HH:mm"),h=c=>c>=1e4?(c/1e4).toFixed(1)+"万":c.toString(),v=async()=>{t.value=!0;try{await new Promise(c=>setTimeout(c,1e3))}finally{t.value=!1}},f=async()=>{try{const c=await fetch("/api/dashboard",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(c.ok){const d=await c.json();if(d.success){const g=d.data;r.value[0].value=g.stats.totalUsers.toString(),r.value[1].value=g.stats.totalNovels.toString(),r.value[2].value=g.stats.totalMemberCodes.toString(),r.value[3].value=g.stats.totalSyncRecords.toString(),i.value=g.recentUsers||[],n.value=g.popularNovels||[]}}}catch(c){console.error("加载仪表板数据失败:",c)}};return Bl(()=>{f()}),(c,d)=>{const g=Xl,m=Vl,p=Gl,y=zl,_=Fl,x=Wl,w=Ul,b=Hl,S=Yl("Refresh");return It(),mr("div",nf,[d[16]||(d[16]=H("div",{class:"page-header"},[H("h1",{class:"page-title"},"仪表板"),H("p",{class:"page-subtitle"},"系统概览和关键指标")],-1)),V(p,{gutter:20,class:"stats-row"},{default:U(()=>[(It(!0),mr(ja,null,Ja(r.value,A=>(It(),me(m,{xs:24,sm:12,md:6,key:A.key},{default:U(()=>[H("div",{class:"stat-card",style:$l({background:A.gradient})},[H("div",of,[V(g,{size:32},{default:U(()=>[(It(),me(yr(A.icon)))]),_:2},1024)]),H("div",sf,[H("div",lf,At(A.value),1),H("div",uf,At(A.label),1),H("div",{class:ti(["stat-trend",A.trendClass])},[V(g,{size:"12"},{default:U(()=>[(It(),me(yr(A.trendIcon)))]),_:2},1024),dt(" "+At(A.trend),1)],2)])],4)]),_:2},1024))),128))]),_:1}),V(p,{gutter:20,class:"charts-row"},{default:U(()=>[V(m,{xs:24,lg:12},{default:U(()=>[H("div",hf,[H("div",vf,[d[8]||(d[8]=H("h3",{class:"card-title"},"用户增长趋势",-1)),V(_,{size:"small"},{default:U(()=>[V(y,{type:e.value==="7d"?"primary":"",onClick:d[0]||(d[0]=A=>e.value="7d")},{default:U(()=>d[5]||(d[5]=[dt("7天")])),_:1,__:[5]},8,["type"]),V(y,{type:e.value==="30d"?"primary":"",onClick:d[1]||(d[1]=A=>e.value="30d")},{default:U(()=>d[6]||(d[6]=[dt("30天")])),_:1,__:[6]},8,["type"]),V(y,{type:e.value==="90d"?"primary":"",onClick:d[2]||(d[2]=A=>e.value="90d")},{default:U(()=>d[7]||(d[7]=[dt("90天")])),_:1,__:[7]},8,["type"])]),_:1})]),H("div",cf,[V(Qa(qa),{option:s.value,style:{height:"300px"}},null,8,["option"])])])]),_:1}),V(m,{xs:24,lg:12},{default:U(()=>[H("div",ff,[d[9]||(d[9]=H("div",{class:"card-header"},[H("h3",{class:"card-title"},"小说创作统计")],-1)),H("div",df,[V(Qa(qa),{option:l.value,style:{height:"300px"}},null,8,["option"])])])]),_:1})]),_:1}),V(p,{gutter:20,class:"tables-row"},{default:U(()=>[V(m,{xs:24,lg:12},{default:U(()=>[H("div",gf,[H("div",pf,[d[11]||(d[11]=H("h3",{class:"card-title"},"最新注册用户",-1)),V(y,{type:"text",onClick:d[3]||(d[3]=A=>c.$router.push("/users"))},{default:U(()=>d[10]||(d[10]=[dt("查看全部")])),_:1,__:[10]})]),V(b,{data:i.value,style:{width:"100%"}},{default:U(()=>[V(x,{prop:"username",label:"用户名",width:"120"}),V(x,{prop:"phoneNumber",label:"手机号",width:"120"}),V(x,{label:"会员状态",width:"80"},{default:U(({row:A})=>[V(w,{type:A.isMember?"success":"info",size:"small"},{default:U(()=>[dt(At(A.isMember?"会员":"普通"),1)]),_:2},1032,["type"])]),_:1}),V(x,{prop:"createdAt",label:"注册时间"},{default:U(({row:A})=>[dt(At(u(A.createdAt)),1)]),_:1})]),_:1},8,["data"])])]),_:1}),V(m,{xs:24,lg:12},{default:U(()=>[H("div",mf,[H("div",yf,[d[13]||(d[13]=H("h3",{class:"card-title"},"热门小说",-1)),V(y,{type:"text",onClick:d[4]||(d[4]=A=>c.$router.push("/novels"))},{default:U(()=>d[12]||(d[12]=[dt("查看全部")])),_:1,__:[12]})]),V(b,{data:n.value,style:{width:"100%"}},{default:U(()=>[V(x,{prop:"title",label:"小说标题","show-overflow-tooltip":""}),V(x,{prop:"genre",label:"类型",width:"80"}),V(x,{label:"字数",width:"80"},{default:U(({row:A})=>[dt(At(h(A.wordCount)),1)]),_:1}),V(x,{prop:"createdAt",label:"创建时间",width:"100"},{default:U(({row:A})=>[dt(At(u(A.createdAt)),1)]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1}),H("div",_f,[H("div",xf,[d[15]||(d[15]=H("h3",{class:"card-title"},"系统状态",-1)),V(y,{onClick:v,loading:t.value,size:"small"},{default:U(()=>[V(g,null,{default:U(()=>[V(S)]),_:1}),d[14]||(d[14]=dt(" 刷新 "))]),_:1,__:[14]},8,["loading"])]),V(p,{gutter:20},{default:U(()=>[(It(!0),mr(ja,null,Ja(o.value,A=>(It(),me(m,{xs:24,sm:8,key:A.name},{default:U(()=>[H("div",bf,[H("div",{class:ti(["status-icon",A.status])},[V(g,{size:"20"},{default:U(()=>[(It(),me(yr(A.icon)))]),_:2},1024)],2),H("div",Sf,[H("div",wf,At(A.name),1),H("div",Af,At(A.value),1)])])]),_:2},1024))),128))]),_:1})])])}}}),Of=Zo(Cf,[["__scopeId","data-v-fe6e1186"]]);export{Of as default};
