import{_ as we}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                   *//* empty css                     *//* empty css                        *//* empty css                      *//* empty css                             *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                       *//* empty css               */import{d as ke,r as y,a as T,o as xe,c as g,b as o,e as t,w as a,W as Ce,f as ze,m as Ve,a2 as Se,a3 as Ee,q as De,A as Re,a4 as Be,H as S,a5 as Ie,a6 as Me,v,Z as Ue,i as u,X as Fe,E as Te,B as k,t as r,a7 as $e,h as n,_ as Le,F as $,G as L,$ as Pe,I as B,ab as je,ac as Ae,ae as Ne,g as Ye,l as Ge,af as He,ag as Ke,a0 as Oe,u as qe,U as We}from"./index-CAzH2L69.js";const Xe={class:"data-sync-container"},Ze={class:"stat-card"},Je={class:"stat-icon"},Qe={class:"stat-content"},et={class:"stat-value"},tt={class:"stat-card"},at={class:"stat-icon"},st={class:"stat-content"},lt={class:"stat-value"},ot={class:"stat-card"},nt={class:"stat-icon"},rt={class:"stat-content"},dt={class:"stat-value"},it={class:"stat-card"},ut={class:"stat-icon"},ct={class:"stat-content"},_t={class:"stat-value"},pt={class:"dashboard-card"},mt={class:"search-bar"},ft={class:"search-left"},gt={class:"search-right"},vt={class:"dashboard-card"},yt={class:"sync-content"},ht={key:0,class:"error-message"},bt={key:1,class:"success-message"},wt={class:"pagination-container"},kt={key:0},xt={style:{"margin-top":"20px"}},Ct={key:0,style:{"margin-top":"20px"}},zt={key:1,style:{"margin-top":"20px"}},Vt=ke({__name:"index",setup(St){const X=qe(),I=y(!1),M=y(!1),U=y(!1),x=y(!1),d=y(null),i=T({keyword:"",status:"",dateRange:null}),h=T({userId:"",syncContent:["小说","角色卡片","知识库","用户设置"]}),c=T({page:1,size:20,total:0}),C=y({totalSyncs:0,successRate:0,todayCount:0,totalDataSize:0}),P=y([]),j=y([]),A=l=>({success:"success",failed:"danger",syncing:"warning"})[l]||"info",N=l=>({success:"成功",failed:"失败",syncing:"同步中"})[l]||"未知",F=l=>{if(l===0)return"0 B";const e=1024,p=["B","KB","MB","GB","TB"],_=Math.floor(Math.log(l)/Math.log(e));return parseFloat((l/Math.pow(e,_)).toFixed(2))+" "+p[_]},Y=l=>Oe(l).format("YYYY-MM-DD HH:mm:ss"),Z=l=>{X.push(`/users/${l}`)},G=()=>{c.page=1,b()},J=()=>{i.keyword="",i.status="",i.dateRange=null,c.page=1,b()},Q=()=>{x.value=!0},ee=()=>{v.info("导出功能开发中...")},te=l=>{d.value=l,U.value=!0},ae=async l=>{try{await We.confirm("确定要重试这次同步吗？","重试确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),v.success("重试同步已启动"),b()}catch{}},se=async()=>{if(!h.userId){v.warning("请选择要同步的用户");return}if(h.syncContent.length===0){v.warning("请选择要同步的内容");return}M.value=!0;try{await new Promise(l=>setTimeout(l,2e3)),v.success("同步已启动"),x.value=!1,b()}catch{v.error("启动同步失败")}finally{M.value=!1}},le=l=>{c.size=l,c.page=1,b()},oe=l=>{c.page=l,b()},ne=async()=>{try{const l=await fetch("/api/sync/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(l.ok){const e=await l.json();e.success&&(C.value=e.data)}}catch(l){console.error("加载同步统计失败:",l)}},re=async()=>{try{const l=await fetch("/api/users?size=100",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(l.ok){const e=await l.json();e.success&&(P.value=e.data.users||[])}}catch(l){console.error("加载用户列表失败:",l)}},b=async()=>{I.value=!0;try{const l=new URLSearchParams({page:c.page.toString(),size:c.size.toString(),keyword:i.keyword,status:i.status});i.dateRange&&i.dateRange.length===2&&(l.append("startDate",i.dateRange[0]),l.append("endDate",i.dateRange[1]));const e=await fetch(`/api/sync/records?${l}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(e.ok){const p=await e.json();p.success?(j.value=p.data.syncRecords||[],c.total=p.data.total||0):v.error(p.message||"加载同步记录失败")}else v.error("网络请求失败")}catch(l){console.error("加载同步记录失败:",l),v.error("加载同步记录失败")}finally{I.value=!1}};return xe(()=>{ne(),re(),b()}),(l,e)=>{const p=k("Refresh"),_=Te,E=Fe,de=k("CircleCheckFilled"),ie=k("Clock"),ue=k("FolderOpened"),ce=Ce,H=k("Search"),K=Ve,z=$e,O=Se,_e=Ee,m=De,pe=k("Download"),f=Le,D=Pe,me=Ue,fe=Ie,w=Ae,ge=je,ve=Ne,q=Me,W=Ge,R=Ke,ye=He,he=Ye,be=Be;return u(),g("div",Xe,[e[30]||(e[30]=o("div",{class:"page-header"},[o("h1",{class:"page-title"},"数据同步管理"),o("p",{class:"page-subtitle"},"管理用户数据同步记录和备份")],-1)),t(ce,{gutter:20,class:"stats-row"},{default:a(()=>[t(E,{xs:12,sm:6},{default:a(()=>[o("div",Ze,[o("div",Je,[t(_,{size:"32",color:"#1890ff"},{default:a(()=>[t(p)]),_:1})]),o("div",Qe,[o("div",et,r(C.value.totalSyncs),1),e[11]||(e[11]=o("div",{class:"stat-label"},"总同步次数",-1))])])]),_:1}),t(E,{xs:12,sm:6},{default:a(()=>[o("div",tt,[o("div",at,[t(_,{size:"32",color:"#52c41a"},{default:a(()=>[t(de)]),_:1})]),o("div",st,[o("div",lt,r(C.value.successRate)+"%",1),e[12]||(e[12]=o("div",{class:"stat-label"},"成功率",-1))])])]),_:1}),t(E,{xs:12,sm:6},{default:a(()=>[o("div",ot,[o("div",nt,[t(_,{size:"32",color:"#faad14"},{default:a(()=>[t(ie)]),_:1})]),o("div",rt,[o("div",dt,r(C.value.todayCount),1),e[13]||(e[13]=o("div",{class:"stat-label"},"今日同步",-1))])])]),_:1}),t(E,{xs:12,sm:6},{default:a(()=>[o("div",it,[o("div",ut,[t(_,{size:"32",color:"#f5222d"},{default:a(()=>[t(ue)]),_:1})]),o("div",ct,[o("div",_t,r(F(C.value.totalDataSize)),1),e[14]||(e[14]=o("div",{class:"stat-label"},"总数据量",-1))])])]),_:1})]),_:1}),o("div",pt,[o("div",mt,[o("div",ft,[t(K,{modelValue:i.keyword,"onUpdate:modelValue":e[0]||(e[0]=s=>i.keyword=s),placeholder:"搜索用户名、同步ID",style:{width:"300px"},clearable:"",onKeyup:ze(G,["enter"])},{prefix:a(()=>[t(_,null,{default:a(()=>[t(H)]),_:1})]),_:1},8,["modelValue"]),t(O,{modelValue:i.status,"onUpdate:modelValue":e[1]||(e[1]=s=>i.status=s),placeholder:"同步状态",style:{width:"120px"},clearable:""},{default:a(()=>[t(z,{label:"全部",value:""}),t(z,{label:"成功",value:"success"}),t(z,{label:"失败",value:"failed"}),t(z,{label:"进行中",value:"syncing"})]),_:1},8,["modelValue"]),t(_e,{modelValue:i.dateRange,"onUpdate:modelValue":e[2]||(e[2]=s=>i.dateRange=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"}},null,8,["modelValue"]),t(m,{type:"primary",onClick:G},{default:a(()=>[t(_,null,{default:a(()=>[t(H)]),_:1}),e[15]||(e[15]=n(" 搜索 "))]),_:1,__:[15]}),t(m,{onClick:J},{default:a(()=>[t(_,null,{default:a(()=>[t(p)]),_:1}),e[16]||(e[16]=n(" 重置 "))]),_:1,__:[16]})]),o("div",gt,[t(m,{type:"success",onClick:Q},{default:a(()=>[t(_,null,{default:a(()=>[t(p)]),_:1}),e[17]||(e[17]=n(" 手动同步 "))]),_:1,__:[17]}),t(m,{onClick:ee},{default:a(()=>[t(_,null,{default:a(()=>[t(pe)]),_:1}),e[18]||(e[18]=n(" 导出记录 "))]),_:1,__:[18]})])])]),o("div",vt,[Re((u(),S(me,{data:j.value,style:{width:"100%"}},{default:a(()=>[t(f,{prop:"id",label:"同步ID",width:"180","show-overflow-tooltip":""}),t(f,{prop:"username",label:"用户",width:"120"},{default:a(({row:s})=>[t(m,{type:"text",onClick:V=>Z(s.userId)},{default:a(()=>[n(r(s.username),1)]),_:2},1032,["onClick"])]),_:1}),t(f,{label:"同步内容",width:"200"},{default:a(({row:s})=>[o("div",yt,[(u(!0),g($,null,L(s.syncContent,V=>(u(),S(D,{key:V,size:"small",style:{"margin-right":"4px","margin-bottom":"4px"}},{default:a(()=>[n(r(V),1)]),_:2},1024))),128))])]),_:1}),t(f,{label:"数据大小",width:"100"},{default:a(({row:s})=>[n(r(F(s.dataSize)),1)]),_:1}),t(f,{label:"状态",width:"100"},{default:a(({row:s})=>[t(D,{type:A(s.status),size:"small"},{default:a(()=>[n(r(N(s.status)),1)]),_:2},1032,["type"])]),_:1}),t(f,{label:"耗时",width:"80"},{default:a(({row:s})=>[n(r(s.duration||"-"),1)]),_:1}),t(f,{prop:"timestamp",label:"同步时间",width:"160"},{default:a(({row:s})=>[n(r(Y(s.timestamp)),1)]),_:1}),t(f,{label:"错误信息","show-overflow-tooltip":""},{default:a(({row:s})=>[s.error?(u(),g("span",ht,r(s.error),1)):(u(),g("span",bt,"-"))]),_:1}),t(f,{label:"操作",width:"150",fixed:"right"},{default:a(({row:s})=>[t(m,{type:"primary",size:"small",onClick:V=>te(s)},{default:a(()=>e[19]||(e[19]=[n(" 详情 ")])),_:2,__:[19]},1032,["onClick"]),s.status==="failed"?(u(),S(m,{key:0,type:"warning",size:"small",onClick:V=>ae(s)},{default:a(()=>e[20]||(e[20]=[n(" 重试 ")])),_:2,__:[20]},1032,["onClick"])):B("",!0)]),_:1})]),_:1},8,["data"])),[[be,I.value]]),o("div",wt,[t(fe,{"current-page":c.page,"onUpdate:currentPage":e[3]||(e[3]=s=>c.page=s),"page-size":c.size,"onUpdate:pageSize":e[4]||(e[4]=s=>c.size=s),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:le,onCurrentChange:oe},null,8,["current-page","page-size","total"])])]),t(q,{modelValue:U.value,"onUpdate:modelValue":e[6]||(e[6]=s=>U.value=s),title:"同步详情",width:"800px"},{default:a(()=>[d.value?(u(),g("div",kt,[t(ge,{column:2,border:""},{default:a(()=>[t(w,{label:"同步ID"},{default:a(()=>[n(r(d.value.id),1)]),_:1}),t(w,{label:"用户"},{default:a(()=>[n(r(d.value.username),1)]),_:1}),t(w,{label:"状态"},{default:a(()=>[t(D,{type:A(d.value.status)},{default:a(()=>[n(r(N(d.value.status)),1)]),_:1},8,["type"])]),_:1}),t(w,{label:"数据大小"},{default:a(()=>[n(r(F(d.value.dataSize)),1)]),_:1}),t(w,{label:"耗时"},{default:a(()=>[n(r(d.value.duration||"-"),1)]),_:1}),t(w,{label:"同步时间"},{default:a(()=>[n(r(Y(d.value.timestamp)),1)]),_:1})]),_:1}),o("div",xt,[e[21]||(e[21]=o("h4",null,"同步内容",-1)),(u(!0),g($,null,L(d.value.syncContent,s=>(u(),S(D,{key:s,style:{"margin-right":"8px","margin-bottom":"8px"}},{default:a(()=>[n(r(s),1)]),_:2},1024))),128))]),d.value.error?(u(),g("div",Ct,[e[22]||(e[22]=o("h4",null,"错误信息",-1)),t(ve,{title:d.value.error,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):B("",!0),d.value.details?(u(),g("div",zt,[e[23]||(e[23]=o("h4",null,"详细信息",-1)),t(K,{modelValue:d.value.details,"onUpdate:modelValue":e[5]||(e[5]=s=>d.value.details=s),type:"textarea",rows:6,readonly:""},null,8,["modelValue"])])):B("",!0)])):B("",!0)]),_:1},8,["modelValue"]),t(q,{modelValue:x.value,"onUpdate:modelValue":e[10]||(e[10]=s=>x.value=s),title:"手动同步",width:"500px"},{footer:a(()=>[t(m,{onClick:e[9]||(e[9]=s=>x.value=!1)},{default:a(()=>e[28]||(e[28]=[n("取消")])),_:1,__:[28]}),t(m,{type:"primary",onClick:se,loading:M.value},{default:a(()=>e[29]||(e[29]=[n(" 开始同步 ")])),_:1,__:[29]},8,["loading"])]),default:a(()=>[t(he,{model:h,"label-width":"100px"},{default:a(()=>[t(W,{label:"选择用户"},{default:a(()=>[t(O,{modelValue:h.userId,"onUpdate:modelValue":e[7]||(e[7]=s=>h.userId=s),placeholder:"请选择用户",style:{width:"100%"}},{default:a(()=>[(u(!0),g($,null,L(P.value,s=>(u(),S(z,{key:s.id,label:s.username,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(W,{label:"同步内容"},{default:a(()=>[t(ye,{modelValue:h.syncContent,"onUpdate:modelValue":e[8]||(e[8]=s=>h.syncContent=s)},{default:a(()=>[t(R,{label:"小说"},{default:a(()=>e[24]||(e[24]=[n("小说数据")])),_:1,__:[24]}),t(R,{label:"角色卡片"},{default:a(()=>e[25]||(e[25]=[n("角色卡片")])),_:1,__:[25]}),t(R,{label:"知识库"},{default:a(()=>e[26]||(e[26]=[n("知识库文档")])),_:1,__:[26]}),t(R,{label:"用户设置"},{default:a(()=>e[27]||(e[27]=[n("用户设置")])),_:1,__:[27]})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),jt=we(Vt,[["__scopeId","data-v-3fd1842b"]]);export{jt as default};
