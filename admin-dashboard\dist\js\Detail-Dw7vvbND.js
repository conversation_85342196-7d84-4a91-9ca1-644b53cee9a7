import{_ as Q}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                  *//* empty css                        *//* empty css                   *//* empty css               *//* empty css                */import{d as X,z as Z,r as M,o as J,c as E,b as t,e as s,w as a,q as K,t as d,$ as tt,h as r,aa as et,W as st,Z as at,v as p,i as k,E as ot,B as f,X as lt,_ as dt,F as nt,G as it,a0 as $,u as rt,H as ct,Q as ut}from"./index-BQm3CBcS.js";const _t={class:"novel-detail-container"},vt={class:"back-header"},pt={class:"dashboard-card"},mt={class:"card-header"},ht={class:"header-actions"},ft={class:"novel-info"},gt={class:"info-main"},Ct={class:"novel-title"},yt={class:"novel-meta"},wt={class:"author-info"},bt={class:"novel-description"},xt={class:"info-stats"},Mt={class:"stat-item"},kt={class:"stat-value"},$t={class:"stat-item"},At={class:"stat-value"},Et={class:"stat-item"},St={class:"stat-value"},Tt={class:"detail-item"},zt={class:"detail-value"},Dt={class:"detail-item"},Bt={class:"detail-value"},It={class:"detail-item"},qt={class:"detail-value"},Ft={class:"detail-item"},Vt={class:"detail-value"},Lt={class:"detail-item"},Nt={class:"detail-value"},Rt={class:"detail-item"},Ut={class:"detail-value"},Yt={class:"dashboard-card"},Ht={class:"card-header"},Wt={class:"chapter-stats"},jt={class:"dashboard-card"},Gt={class:"card-header"},Ot={class:"character-stats"},Pt={class:"character-card"},Qt={class:"character-avatar"},Xt={class:"character-info"},Zt={class:"character-name"},Jt={class:"character-role"},Kt={class:"character-desc"},te={class:"dashboard-card"},ee={class:"chart-container"},se={class:"chart-placeholder"},ae={class:"chart-container"},oe={class:"chart-placeholder"},le=X({__name:"Detail",setup(de){const S=Z(),T=rt(),z=S.params.id,l=M({id:"",title:"",author:"",userId:"",genre:"",status:"ongoing",description:"",wordCount:0,chapterCount:0,qualityScore:0,createdAt:"",updatedAt:""}),y=M([]),w=M([]),D=o=>({科幻:"primary",历史:"success",都市:"warning",玄幻:"danger",其他:"info"})[o]||"info",B=o=>({ongoing:"success",completed:"primary",paused:"warning"})[o]||"info",I=o=>({ongoing:"连载中",completed:"已完结",paused:"暂停"})[o]||"未知",q=o=>o>=1e4?(o/1e4).toFixed(1)+"万":o.toString(),b=o=>$(o).format("YYYY-MM-DD HH:mm:ss"),F=()=>{const o=A(),c=(l.value.chapterCount||1)/o;return c>=1?`${c.toFixed(1)}章/天`:`${(1/c).toFixed(1)}天/章`},A=()=>{const o=$(l.value.createdAt);return $(l.value.updatedAt).diff(o,"day")+1},V=()=>{T.push(`/users/${l.value.userId}`)},L=()=>{p.info("质量分析功能开发中...")},N=()=>{p.info("编辑功能开发中...")},R=o=>{p.info(`查看章节: ${o.title}`)},U=o=>{p.info(`编辑章节: ${o.title}`)},Y=async()=>{try{const o=await fetch(`/api/novels/${z}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(o.ok){const e=await o.json();if(e.success){l.value=e.data;const c=e.data.chapterCount||0,v=c>0?Math.floor(e.data.wordCount/c):2e3,u=[];for(let i=1;i<=Math.min(c,10);i++)u.push({id:i.toString(),title:`第${i}章：${H()}`,wordCount:v+Math.floor(Math.random()*500)-250,createdAt:new Date(Date.now()-(c-i)*24*60*60*1e3).toISOString()});y.value=u;const m=["主角","女主角","反派","导师","朋友","敌人"],g=[],C=Math.min(Math.floor(Math.random()*6)+2,m.length);for(let i=0;i<C;i++)g.push({id:(i+1).toString(),name:`角色${i+1}`,role:m[i],description:`${m[i]}的详细描述`,avatar:""});w.value=g}else p.error(e.message||"小说不存在")}else o.status===404?p.error("小说不存在"):p.error("网络请求失败")}catch(o){console.error("加载小说详情失败:",o),p.error("加载小说详情失败")}},H=()=>{const o=["初遇","觉醒","挑战","成长","危机","转折","突破","对决","真相","终章","序幕","开端","冲突","高潮","结局","新生","探索","发现","抉择","命运"];return o[Math.floor(Math.random()*o.length)]};return J(()=>{Y()}),(o,e)=>{const c=f("ArrowLeft"),v=ot,u=K,m=f("TrendCharts"),g=f("Edit"),C=tt,i=et,_=lt,x=st,h=dt,W=at,j=f("User"),G=ut,O=f("BarChart");return k(),E("div",_t,[t("div",vt,[s(u,{onClick:e[0]||(e[0]=n=>o.$router.back()),type:"text"},{default:a(()=>[s(v,null,{default:a(()=>[s(c)]),_:1}),e[2]||(e[2]=r(" 返回小说列表 "))]),_:1,__:[2]})]),t("div",pt,[t("div",mt,[e[5]||(e[5]=t("h3",{class:"card-title"},"小说基本信息",-1)),t("div",ht,[s(u,{type:"warning",onClick:L},{default:a(()=>[s(v,null,{default:a(()=>[s(m)]),_:1}),e[3]||(e[3]=r(" 质量分析 "))]),_:1,__:[3]}),s(u,{type:"primary",onClick:N},{default:a(()=>[s(v,null,{default:a(()=>[s(g)]),_:1}),e[4]||(e[4]=r(" 编辑小说 "))]),_:1,__:[4]})])]),t("div",ft,[t("div",gt,[t("h1",Ct,d(l.value.title),1),t("div",yt,[s(C,{type:D(l.value.genre),size:"large"},{default:a(()=>[r(d(l.value.genre),1)]),_:1},8,["type"]),s(C,{type:B(l.value.status),size:"large"},{default:a(()=>[r(d(I(l.value.status)),1)]),_:1},8,["type"]),t("span",wt,[e[6]||(e[6]=r(" 作者： ")),s(u,{type:"text",onClick:V},{default:a(()=>[r(d(l.value.author),1)]),_:1})])]),t("div",bt,d(l.value.description||"暂无简介"),1)]),t("div",xt,[t("div",Mt,[t("div",kt,d(q(l.value.wordCount)),1),e[7]||(e[7]=t("div",{class:"stat-label"},"总字数",-1))]),t("div",$t,[t("div",At,d(l.value.chapterCount||0),1),e[8]||(e[8]=t("div",{class:"stat-label"},"章节数",-1))]),t("div",Et,[t("div",St,[s(i,{modelValue:l.value.qualityScore,"onUpdate:modelValue":e[1]||(e[1]=n=>l.value.qualityScore=n),disabled:"","show-score":""},null,8,["modelValue"])]),e[9]||(e[9]=t("div",{class:"stat-label"},"质量评分",-1))])])]),s(x,{gutter:20,class:"detail-grid"},{default:a(()=>[s(_,{xs:24,sm:12,md:8},{default:a(()=>[t("div",Tt,[e[10]||(e[10]=t("div",{class:"detail-label"},"创建时间",-1)),t("div",zt,d(b(l.value.createdAt)),1)])]),_:1}),s(_,{xs:24,sm:12,md:8},{default:a(()=>[t("div",Dt,[e[11]||(e[11]=t("div",{class:"detail-label"},"最后更新",-1)),t("div",Bt,d(b(l.value.updatedAt)),1)])]),_:1}),s(_,{xs:24,sm:12,md:8},{default:a(()=>[t("div",It,[e[12]||(e[12]=t("div",{class:"detail-label"},"平均章节字数",-1)),t("div",qt,d(Math.round(l.value.wordCount/(l.value.chapterCount||1))),1)])]),_:1}),s(_,{xs:24,sm:12,md:8},{default:a(()=>[t("div",Ft,[e[13]||(e[13]=t("div",{class:"detail-label"},"更新频率",-1)),t("div",Vt,d(F()),1)])]),_:1}),s(_,{xs:24,sm:12,md:8},{default:a(()=>[t("div",Lt,[e[14]||(e[14]=t("div",{class:"detail-label"},"创作天数",-1)),t("div",Nt,d(A())+"天",1)])]),_:1}),s(_,{xs:24,sm:12,md:8},{default:a(()=>[t("div",Rt,[e[15]||(e[15]=t("div",{class:"detail-label"},"小说ID",-1)),t("div",Ut,d(l.value.id),1)])]),_:1})]),_:1})]),t("div",Yt,[t("div",Ht,[e[16]||(e[16]=t("h3",{class:"card-title"},"章节列表",-1)),t("div",Wt," 共 "+d(y.value.length)+" 章节 ",1)]),s(W,{data:y.value,style:{width:"100%"}},{default:a(()=>[s(h,{type:"index",label:"序号",width:"60"}),s(h,{prop:"title",label:"章节标题","show-overflow-tooltip":""}),s(h,{label:"字数",width:"100"},{default:a(({row:n})=>[r(d(n.wordCount||0),1)]),_:1}),s(h,{prop:"createdAt",label:"创建时间",width:"160"},{default:a(({row:n})=>[r(d(b(n.createdAt)),1)]),_:1}),s(h,{label:"操作",width:"120"},{default:a(({row:n})=>[s(u,{type:"text",size:"small",onClick:P=>R(n)},{default:a(()=>e[17]||(e[17]=[r(" 查看 ")])),_:2,__:[17]},1032,["onClick"]),s(u,{type:"text",size:"small",onClick:P=>U(n)},{default:a(()=>e[18]||(e[18]=[r(" 编辑 ")])),_:2,__:[18]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),t("div",jt,[t("div",Gt,[e[19]||(e[19]=t("h3",{class:"card-title"},"相关角色",-1)),t("div",Ot," 共 "+d(w.value.length)+" 个角色 ",1)]),s(x,{gutter:16},{default:a(()=>[(k(!0),E(nt,null,it(w.value,n=>(k(),ct(_,{xs:24,sm:12,md:8,lg:6,key:n.id},{default:a(()=>[t("div",Pt,[t("div",Qt,[s(G,{size:60,src:n.avatar},{default:a(()=>[s(v,{size:"30"},{default:a(()=>[s(j)]),_:1})]),_:2},1032,["src"])]),t("div",Xt,[t("div",Zt,d(n.name),1),t("div",Jt,d(n.role||"角色"),1),t("div",Kt,d(n.description||"暂无描述"),1)])])]),_:2},1024))),128))]),_:1})]),t("div",te,[e[24]||(e[24]=t("div",{class:"card-header"},[t("h3",{class:"card-title"},"创作统计")],-1)),s(x,{gutter:20},{default:a(()=>[s(_,{xs:24,lg:12},{default:a(()=>[t("div",ee,[e[21]||(e[21]=t("h4",null,"字数增长趋势",-1)),t("div",se,[s(v,{size:"48",color:"#d9d9d9"},{default:a(()=>[s(m)]),_:1}),e[20]||(e[20]=t("p",null,"图表功能开发中...",-1))])])]),_:1}),s(_,{xs:24,lg:12},{default:a(()=>[t("div",ae,[e[23]||(e[23]=t("h4",null,"章节更新频率",-1)),t("div",oe,[s(v,{size:"48",color:"#d9d9d9"},{default:a(()=>[s(O)]),_:1}),e[22]||(e[22]=t("p",null,"图表功能开发中...",-1))])])]),_:1})]),_:1})])])}}}),pe=Q(le,[["__scopeId","data-v-40970a69"]]);export{pe as default};
