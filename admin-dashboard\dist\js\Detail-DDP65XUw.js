import{_ as W}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                        *//* empty css                   *//* empty css               *//* empty css                  */import{d as j,z as V,r as b,o as Y,c as y,b as e,e as t,w as a,q as H,Q as U,t as l,H as q,I as F,$ as P,W as Q,Z as X,v as w,u as Z,i as p,h as d,E as G,B as C,X as J,_ as K,a0 as O}from"./index-BQm3CBcS.js";const ss={class:"user-detail-container"},es={class:"back-header"},ts={class:"dashboard-card"},as={class:"card-header"},os={class:"user-profile"},ls={class:"profile-avatar"},ns={class:"profile-info"},is={class:"profile-name"},ds={class:"profile-tags"},rs={class:"info-item"},us={class:"info-value"},cs={class:"info-item"},_s={class:"info-value"},ms={class:"info-item"},vs={class:"info-value"},fs={class:"info-item"},ps={class:"info-value"},hs={class:"info-item"},bs={class:"info-value"},ys={class:"info-item"},ws={class:"info-value"},gs={key:0,class:"permanent"},Cs={key:1},xs={key:2,class:"no-member"},ks={class:"dashboard-card"},Es={class:"stat-item"},Ts={class:"stat-value"},Ms={class:"stat-item"},As={class:"stat-value"},Ds={class:"stat-item"},Ns={class:"stat-value"},$s={class:"stat-item"},zs={class:"stat-value"},Bs={class:"dashboard-card"},Is={class:"card-header"},Ss={class:"dashboard-card"},Ls=j({__name:"Detail",setup(Rs){const A=V(),h=Z(),D=A.params.id,n=b({id:"",username:"",phoneNumber:"",email:"",avatar:"",isMember:!1,membershipType:"none",memberExpireTime:null,isPermanentMember:!1,isDataSyncEnabled:!1,createdAt:"",lastLoginAt:null}),_=b({novelCount:0,totalWords:0,characterCount:0,knowledgeCount:0}),x=b([]),k=b([]),N=o=>{switch(o){case"permanent":return"success";case"monthly":return"warning";default:return"info"}},$=o=>{switch(o){case"permanent":return"永久会员";case"monthly":return"月会员";default:return"普通用户"}},m=o=>O(o).format("YYYY-MM-DD HH:mm:ss"),E=o=>o>=1e4?(o/1e4).toFixed(1)+"万":o.toString(),z=()=>{w.info("编辑功能开发中...")},B=o=>{h.push(`/novels/${o.id}`)},I=async()=>{try{const o=await fetch(`/api/users/${D}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(o.ok){const s=await o.json();if(s.success){n.value=s.data.user;const v=s.data.novels||[];x.value=v.slice(0,5),_.value={novelCount:v.length,totalWords:v.reduce((f,c)=>f+(c.wordCount||0),0),characterCount:Math.floor(Math.random()*20)+5,knowledgeCount:Math.floor(Math.random()*10)+2},k.value=s.data.syncRecords||[],console.log("用户详情加载成功:",s.data)}else w.error(s.message||"用户不存在"),h.push("/users")}else{const s=await o.json();w.error(s.message||"获取用户信息失败"),o.status===404&&h.push("/users")}}catch(o){console.error("加载用户详情失败:",o),w.error("加载用户详情失败"),h.push("/users")}};return Y(()=>{I()}),(o,s)=>{const v=C("ArrowLeft"),f=G,c=H,S=C("Edit"),L=C("User"),R=U,g=P,r=J,T=Q,u=K,M=X;return p(),y("div",ss,[e("div",es,[t(c,{onClick:s[0]||(s[0]=i=>o.$router.back()),type:"text"},{default:a(()=>[t(f,null,{default:a(()=>[t(v)]),_:1}),s[2]||(s[2]=d(" 返回用户列表 "))]),_:1,__:[2]})]),e("div",ts,[e("div",as,[s[4]||(s[4]=e("h3",{class:"card-title"},"用户基本信息",-1)),t(c,{type:"primary",onClick:z},{default:a(()=>[t(f,null,{default:a(()=>[t(S)]),_:1}),s[3]||(s[3]=d(" 编辑用户 "))]),_:1,__:[3]})]),e("div",os,[e("div",ls,[t(R,{size:80,src:n.value.avatar},{default:a(()=>[t(f,{size:"40"},{default:a(()=>[t(L)]),_:1})]),_:1},8,["src"])]),e("div",ns,[e("h2",is,l(n.value.username),1),e("div",ds,[t(g,{type:N(n.value.membershipType),size:"large"},{default:a(()=>[d(l($(n.value.membershipType)),1)]),_:1},8,["type"]),n.value.isDataSyncEnabled?(p(),q(g,{key:0,type:"success",size:"large"},{default:a(()=>s[5]||(s[5]=[d(" 数据同步已启用 ")])),_:1,__:[5]})):F("",!0)])])]),t(T,{gutter:20,class:"info-grid"},{default:a(()=>[t(r,{xs:24,sm:12,md:8},{default:a(()=>[e("div",rs,[s[6]||(s[6]=e("div",{class:"info-label"},"用户ID",-1)),e("div",us,l(n.value.id),1)])]),_:1}),t(r,{xs:24,sm:12,md:8},{default:a(()=>[e("div",cs,[s[7]||(s[7]=e("div",{class:"info-label"},"手机号",-1)),e("div",_s,l(n.value.phoneNumber),1)])]),_:1}),t(r,{xs:24,sm:12,md:8},{default:a(()=>[e("div",ms,[s[8]||(s[8]=e("div",{class:"info-label"},"邮箱",-1)),e("div",vs,l(n.value.email||"未设置"),1)])]),_:1}),t(r,{xs:24,sm:12,md:8},{default:a(()=>[e("div",fs,[s[9]||(s[9]=e("div",{class:"info-label"},"注册时间",-1)),e("div",ps,l(m(n.value.createdAt)),1)])]),_:1}),t(r,{xs:24,sm:12,md:8},{default:a(()=>[e("div",hs,[s[10]||(s[10]=e("div",{class:"info-label"},"最后登录",-1)),e("div",bs,l(n.value.lastLoginAt?m(n.value.lastLoginAt):"从未登录"),1)])]),_:1}),t(r,{xs:24,sm:12,md:8},{default:a(()=>[e("div",ys,[s[11]||(s[11]=e("div",{class:"info-label"},"会员到期时间",-1)),e("div",ws,[n.value.membershipType==="permanent"?(p(),y("span",gs," 永久有效 ")):n.value.memberExpireTime?(p(),y("span",Cs,l(m(n.value.memberExpireTime)),1)):(p(),y("span",xs,"非会员"))])])]),_:1})]),_:1})]),e("div",ks,[s[16]||(s[16]=e("div",{class:"card-header"},[e("h3",{class:"card-title"},"创作统计")],-1)),t(T,{gutter:20},{default:a(()=>[t(r,{xs:12,sm:6},{default:a(()=>[e("div",Es,[e("div",Ts,l(_.value.novelCount),1),s[12]||(s[12]=e("div",{class:"stat-label"},"小说总数",-1))])]),_:1}),t(r,{xs:12,sm:6},{default:a(()=>[e("div",Ms,[e("div",As,l(E(_.value.totalWords)),1),s[13]||(s[13]=e("div",{class:"stat-label"},"总字数",-1))])]),_:1}),t(r,{xs:12,sm:6},{default:a(()=>[e("div",Ds,[e("div",Ns,l(_.value.characterCount),1),s[14]||(s[14]=e("div",{class:"stat-label"},"角色卡片",-1))])]),_:1}),t(r,{xs:12,sm:6},{default:a(()=>[e("div",$s,[e("div",zs,l(_.value.knowledgeCount),1),s[15]||(s[15]=e("div",{class:"stat-label"},"知识库文档",-1))])]),_:1})]),_:1})]),e("div",Bs,[e("div",Is,[s[18]||(s[18]=e("h3",{class:"card-title"},"最近创作的小说",-1)),t(c,{type:"text",onClick:s[1]||(s[1]=i=>o.$router.push("/novels"))},{default:a(()=>s[17]||(s[17]=[d(" 查看全部 ")])),_:1,__:[17]})]),t(M,{data:x.value,style:{width:"100%"}},{default:a(()=>[t(u,{prop:"title",label:"小说标题","show-overflow-tooltip":""}),t(u,{prop:"genre",label:"类型",width:"100"}),t(u,{label:"字数",width:"100"},{default:a(({row:i})=>[d(l(E(i.wordCount)),1)]),_:1}),t(u,{prop:"createdAt",label:"创建时间",width:"160"},{default:a(({row:i})=>[d(l(m(i.createdAt)),1)]),_:1}),t(u,{label:"操作",width:"100"},{default:a(({row:i})=>[t(c,{type:"text",onClick:Ws=>B(i)},{default:a(()=>s[19]||(s[19]=[d(" 查看 ")])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),e("div",Ss,[s[20]||(s[20]=e("div",{class:"card-header"},[e("h3",{class:"card-title"},"数据同步记录")],-1)),t(M,{data:k.value,style:{width:"100%"}},{default:a(()=>[t(u,{label:"同步时间",width:"160"},{default:a(({row:i})=>[d(l(m(i.timestamp)),1)]),_:1}),t(u,{prop:"dataSize",label:"数据大小",width:"100"}),t(u,{label:"同步内容","show-overflow-tooltip":""},{default:a(({row:i})=>[d(l(i.syncContent.join(", ")),1)]),_:1}),t(u,{label:"状态",width:"80"},{default:a(({row:i})=>[t(g,{type:i.status==="success"?"success":"danger",size:"small"},{default:a(()=>[d(l(i.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])])])}}}),Fs=W(Ls,[["__scopeId","data-v-ebab3ce8"]]);export{Fs as default};
