import{_ as pe}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                   *//* empty css                      *//* empty css                     *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                   *//* empty css                  *//* empty css                  *//* empty css                       */import{d as ue,r as y,a as z,o as ce,c as v,b as m,e,w as n,f as _e,m as ge,a2 as fe,a3 as be,q as he,A as ye,a4 as ve,H as we,a5 as Ee,a6 as Ve,v as r,Z as ke,i as h,E as Se,B as T,a7 as Te,h as p,t as w,_ as De,Q as Ce,$ as Ue,a8 as xe,a9 as ze,g as Re,l as $e,a0 as Be,u as Ne,U as Ae}from"./index-BQm3CBcS.js";const Me={class:"users-container"},je={class:"dashboard-card"},Ie={class:"search-bar"},Le={class:"search-left"},Pe={class:"search-right"},Fe={class:"dashboard-card"},Oe={class:"user-info"},Ye={class:"username"},qe={key:0,class:"permanent-member"},He={key:1},Je={key:2,class:"no-member"},Ke={key:0},Qe={key:1,class:"never-login"},Ze={class:"pagination-container"},Ge=ue({__name:"index",setup(We){const A=Ne(),C=y(!1),U=y(!1),E=y(!1),f=y([]),V=y(),i=z({keyword:"",memberType:"",dateRange:null}),d=z({page:1,size:20,total:0}),R=y([]),s=z({id:"",username:"",phoneNumber:"",email:"",membershipType:"none",isDataSyncEnabled:!1}),M={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],phoneNumber:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}]},j=l=>{switch(l){case"permanent":return"success";case"monthly":return"warning";default:return"info"}},I=l=>{switch(l){case"permanent":return"永久会员";case"monthly":return"月会员";default:return"普通用户"}},x=l=>Be(l).format("YYYY-MM-DD HH:mm"),$=()=>{d.page=1,_()},L=()=>{i.keyword="",i.memberType="",i.dateRange=null,d.page=1,_()},P=l=>{f.value=l},F=async()=>{if(f.value.length===0){r.warning("请选择要删除的用户");return}try{await Ae.confirm(`确定要删除选中的 ${f.value.length} 个用户吗？此操作将同时删除用户的所有相关数据（小说、同步记录等）。`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=f.value.map(g=>g.id),a=await fetch("/api/users/batch-delete",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({userIds:l})}),o=await a.json();a.ok&&o.success?(r.success(o.message),f.value=[],_()):r.error(o.message||"批量删除失败")}catch(l){l.message&&(console.error("批量删除失败:",l),r.error("批量删除失败"))}},O=()=>{r.info("导出功能开发中...")},Y=l=>{A.push(`/users/${l.id}`)},q=l=>{Object.assign(s,{id:l.id,username:l.username,phoneNumber:l.phoneNumber,email:l.email||"",membershipType:l.membershipType,isDataSyncEnabled:l.isDataSyncEnabled}),E.value=!0},H=async l=>{try{const a=await fetch(`/api/users/${l.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}}),o=await a.json();a.ok&&o.success?(r.success(o.message),_()):r.error(o.message||"删除失败")}catch(a){console.error("删除用户失败:",a),r.error("删除失败")}},J=async l=>{try{r.success("设置已更新")}catch{r.error("设置失败"),l.isDataSyncEnabled=!l.isDataSyncEnabled}},K=async()=>{if(V.value)try{if(!await V.value.validate())return;U.value=!0;const a=await fetch(`/api/users/${s.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({username:s.username,email:s.email,isMember:s.isMember,membershipType:s.membershipType,isDataSyncEnabled:s.isDataSyncEnabled})}),o=await a.json();a.ok&&o.success?(r.success(o.message),E.value=!1,_()):r.error(o.message||"保存失败")}catch(l){console.error("保存用户失败:",l),r.error("保存失败")}finally{U.value=!1}},Q=()=>{V.value&&V.value.resetFields()},Z=l=>{d.size=l,d.page=1,_()},G=l=>{d.page=l,_()},_=async()=>{C.value=!0;try{const l=new URLSearchParams({page:d.page.toString(),size:d.size.toString(),keyword:i.keyword,memberType:i.memberType});i.dateRange&&i.dateRange.length===2&&(l.append("startDate",i.dateRange[0]),l.append("endDate",i.dateRange[1]));const a=await fetch(`/api/users?${l}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(a.ok){const o=await a.json();o.success?(R.value=o.data.users||[],d.total=o.data.total||0):r.error(o.message||"加载用户列表失败")}else r.error("网络请求失败")}catch(l){console.error("加载用户列表失败:",l),r.error("加载用户列表失败")}finally{C.value=!1}};return ce(()=>{_()}),(l,a)=>{const o=T("Search"),g=Se,D=ge,b=Te,B=fe,W=be,c=he,X=T("Refresh"),ee=T("Delete"),ae=T("Download"),u=De,te=T("User"),le=Ce,ne=Ue,N=xe,oe=ze,se=ke,re=Ee,k=$e,ie=Re,de=Ve,me=ve;return h(),v("div",Me,[a[20]||(a[20]=m("div",{class:"page-header"},[m("h1",{class:"page-title"},"用户管理"),m("p",{class:"page-subtitle"},"管理系统中的所有用户")],-1)),m("div",je,[m("div",Ie,[m("div",Le,[e(D,{modelValue:i.keyword,"onUpdate:modelValue":a[0]||(a[0]=t=>i.keyword=t),placeholder:"搜索用户名、手机号",style:{width:"300px"},clearable:"",onKeyup:_e($,["enter"])},{prefix:n(()=>[e(g,null,{default:n(()=>[e(o)]),_:1})]),_:1},8,["modelValue"]),e(B,{modelValue:i.memberType,"onUpdate:modelValue":a[1]||(a[1]=t=>i.memberType=t),placeholder:"会员类型",style:{width:"120px"},clearable:""},{default:n(()=>[e(b,{label:"全部",value:""}),e(b,{label:"普通用户",value:"none"}),e(b,{label:"月会员",value:"monthly"}),e(b,{label:"永久会员",value:"permanent"})]),_:1},8,["modelValue"]),e(W,{modelValue:i.dateRange,"onUpdate:modelValue":a[2]||(a[2]=t=>i.dateRange=t),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",style:{width:"240px"}},null,8,["modelValue"]),e(c,{type:"primary",onClick:$},{default:n(()=>[e(g,null,{default:n(()=>[e(o)]),_:1}),a[12]||(a[12]=p(" 搜索 "))]),_:1,__:[12]}),e(c,{onClick:L},{default:n(()=>[e(g,null,{default:n(()=>[e(X)]),_:1}),a[13]||(a[13]=p(" 重置 "))]),_:1,__:[13]})]),m("div",Pe,[e(c,{type:"danger",disabled:f.value.length===0,onClick:F},{default:n(()=>[e(g,null,{default:n(()=>[e(ee)]),_:1}),p(" 批量删除 ("+w(f.value.length)+") ",1)]),_:1},8,["disabled"]),e(c,{onClick:O},{default:n(()=>[e(g,null,{default:n(()=>[e(ae)]),_:1}),a[14]||(a[14]=p(" 导出数据 "))]),_:1,__:[14]})])])]),m("div",Fe,[ye((h(),we(se,{data:R.value,onSelectionChange:P,style:{width:"100%"}},{default:n(()=>[e(u,{type:"selection",width:"55"}),e(u,{prop:"username",label:"用户名",width:"120"},{default:n(({row:t})=>[m("div",Oe,[e(le,{size:32,src:t.avatar},{default:n(()=>[e(g,null,{default:n(()=>[e(te)]),_:1})]),_:2},1032,["src"]),m("span",Ye,w(t.username),1)])]),_:1}),e(u,{prop:"phoneNumber",label:"手机号",width:"130"}),e(u,{prop:"email",label:"邮箱",width:"180","show-overflow-tooltip":""}),e(u,{label:"会员状态",width:"100"},{default:n(({row:t})=>[e(ne,{type:j(t.membershipType),size:"small"},{default:n(()=>[p(w(I(t.membershipType)),1)]),_:2},1032,["type"])]),_:1}),e(u,{label:"会员到期",width:"120"},{default:n(({row:t})=>[t.membershipType==="permanent"?(h(),v("span",qe," 永久有效 ")):t.memberExpireTime?(h(),v("span",He,w(x(t.memberExpireTime)),1)):(h(),v("span",Je,"-"))]),_:1}),e(u,{label:"数据同步",width:"80"},{default:n(({row:t})=>[e(N,{modelValue:t.isDataSyncEnabled,"onUpdate:modelValue":S=>t.isDataSyncEnabled=S,onChange:S=>J(t),disabled:!t.isMember},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(u,{prop:"createdAt",label:"注册时间",width:"160"},{default:n(({row:t})=>[p(w(x(t.createdAt)),1)]),_:1}),e(u,{label:"最后登录",width:"160"},{default:n(({row:t})=>[t.lastLoginAt?(h(),v("span",Ke,w(x(t.lastLoginAt)),1)):(h(),v("span",Qe,"从未登录"))]),_:1}),e(u,{label:"操作",width:"180",fixed:"right"},{default:n(({row:t})=>[e(c,{type:"primary",size:"small",onClick:S=>Y(t)},{default:n(()=>a[15]||(a[15]=[p(" 详情 ")])),_:2,__:[15]},1032,["onClick"]),e(c,{type:"warning",size:"small",onClick:S=>q(t)},{default:n(()=>a[16]||(a[16]=[p(" 编辑 ")])),_:2,__:[16]},1032,["onClick"]),e(oe,{title:"确定要删除这个用户吗？",onConfirm:S=>H(t)},{reference:n(()=>[e(c,{type:"danger",size:"small"},{default:n(()=>a[17]||(a[17]=[p(" 删除 ")])),_:1,__:[17]})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[me,C.value]]),m("div",Ze,[e(re,{"current-page":d.page,"onUpdate:currentPage":a[3]||(a[3]=t=>d.page=t),"page-size":d.size,"onUpdate:pageSize":a[4]||(a[4]=t=>d.size=t),"page-sizes":[10,20,50,100],total:d.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Z,onCurrentChange:G},null,8,["current-page","page-size","total"])])]),e(de,{modelValue:E.value,"onUpdate:modelValue":a[11]||(a[11]=t=>E.value=t),title:"编辑用户",width:"600px",onClose:Q},{footer:n(()=>[e(c,{onClick:a[10]||(a[10]=t=>E.value=!1)},{default:n(()=>a[18]||(a[18]=[p("取消")])),_:1,__:[18]}),e(c,{type:"primary",onClick:K,loading:U.value},{default:n(()=>a[19]||(a[19]=[p(" 保存 ")])),_:1,__:[19]},8,["loading"])]),default:n(()=>[e(ie,{ref_key:"editFormRef",ref:V,model:s,rules:M,"label-width":"100px"},{default:n(()=>[e(k,{label:"用户名",prop:"username"},{default:n(()=>[e(D,{modelValue:s.username,"onUpdate:modelValue":a[5]||(a[5]=t=>s.username=t)},null,8,["modelValue"])]),_:1}),e(k,{label:"手机号",prop:"phoneNumber"},{default:n(()=>[e(D,{modelValue:s.phoneNumber,"onUpdate:modelValue":a[6]||(a[6]=t=>s.phoneNumber=t)},null,8,["modelValue"])]),_:1}),e(k,{label:"邮箱",prop:"email"},{default:n(()=>[e(D,{modelValue:s.email,"onUpdate:modelValue":a[7]||(a[7]=t=>s.email=t)},null,8,["modelValue"])]),_:1}),e(k,{label:"会员类型",prop:"membershipType"},{default:n(()=>[e(B,{modelValue:s.membershipType,"onUpdate:modelValue":a[8]||(a[8]=t=>s.membershipType=t),style:{width:"100%"}},{default:n(()=>[e(b,{label:"普通用户",value:"none"}),e(b,{label:"月会员",value:"monthly"}),e(b,{label:"永久会员",value:"permanent"})]),_:1},8,["modelValue"])]),_:1}),e(k,{label:"数据同步",prop:"isDataSyncEnabled"},{default:n(()=>[e(N,{modelValue:s.isDataSyncEnabled,"onUpdate:modelValue":a[9]||(a[9]=t=>s.isDataSyncEnabled=t)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),ua=pe(Ge,[["__scopeId","data-v-4e724341"]]);export{ua as default};
