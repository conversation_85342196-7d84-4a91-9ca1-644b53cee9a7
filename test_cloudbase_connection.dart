import 'dart:convert';
import 'package:http/http.dart' as http;

/// 测试CloudBase连接的简单脚本
void main() async {
  print('🔍 开始测试CloudBase连接...');
  
  // 测试生产环境API
  final productionUrl = 'https://api.dznovel.top/api';
  
  try {
    print('🔍 测试健康检查端点: $productionUrl/health');
    
    final response = await http.get(
      Uri.parse('$productionUrl/health'),
      headers: {
        'Content-Type': 'application/json',
      },
    ).timeout(Duration(seconds: 10));
    
    print('🔍 响应状态码: ${response.statusCode}');
    print('🔍 响应内容: ${response.body}');
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      print('✅ CloudBase连接成功！');
      print('📊 服务器信息: $data');
    } else {
      print('❌ CloudBase连接失败: 状态码 ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ CloudBase连接异常: $e');
  }
  
  // 测试带查询参数的健康检查端点
  try {
    print('\n🔍 测试带查询参数的健康检查端点: $productionUrl/health?_api_path=health');
    
    final response = await http.get(
      Uri.parse('$productionUrl/health?_api_path=health'),
      headers: {
        'Content-Type': 'application/json',
      },
    ).timeout(Duration(seconds: 10));
    
    print('🔍 响应状态码: ${response.statusCode}');
    print('🔍 响应内容: ${response.body}');
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      print('✅ 带查询参数的CloudBase连接成功！');
      print('📊 服务器信息: $data');
    } else {
      print('❌ 带查询参数的CloudBase连接失败: 状态码 ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ 带查询参数的CloudBase连接异常: $e');
  }
}
