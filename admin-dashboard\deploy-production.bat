@echo off
echo 🚀 开始构建后台管理系统生产版本...
echo.

echo 📦 安装依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 🔧 构建生产版本...
call npm run build:prod
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo ✅ 后台管理系统生产版本构建完成！
echo 📁 构建文件位置: dist/
echo.
echo 🌐 部署说明:
echo    1. 将 dist/ 目录中的文件上传到Web服务器
echo    2. 配置Web服务器将 /api/* 请求代理到 https://api.dznovel.top/api/*
echo    3. 或者直接部署到支持SPA的静态托管服务
echo.
echo 📋 Nginx配置示例:
echo    location /api/ {
echo        proxy_pass https://api.dznovel.top/api/;
echo        proxy_set_header Host $host;
echo        proxy_set_header X-Real-IP $remote_addr;
echo    }
echo.
echo    location / {
echo        try_files $uri $uri/ /index.html;
echo    }
echo.

pause
