<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统 - 生产环境API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f8fff8; }
        .error { border-color: #f44336; background-color: #fff8f8; }
        .pending { border-color: #ff9800; background-color: #fff8f0; }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #66b1ff; }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 后台管理系统 - 生产环境API测试</h1>
        
        <div class="test-item pending" id="health-test">
            <h3>🏥 健康检查测试</h3>
            <button onclick="testHealthCheck()">测试健康检查</button>
            <div class="log" id="health-log">等待测试...</div>
        </div>

        <div class="test-item pending" id="dashboard-test">
            <h3>📊 仪表板数据测试</h3>
            <button onclick="testDashboard()">测试仪表板</button>
            <div class="log" id="dashboard-log">等待测试...</div>
        </div>

        <div class="test-item pending" id="users-test">
            <h3>👥 用户数据测试</h3>
            <button onclick="testUsers()">测试用户列表</button>
            <div class="log" id="users-log">等待测试...</div>
        </div>

        <div class="test-item pending" id="admin-login-test">
            <h3>🔐 管理员登录测试</h3>
            <button onclick="testAdminLogin()">测试管理员登录</button>
            <div class="log" id="admin-login-log">等待测试...</div>
        </div>

        <button onclick="runAllTests()" style="background: #67C23A; font-size: 16px; padding: 15px 30px;">
            🚀 运行所有测试
        </button>
    </div>

    <script>
        const API_BASE_URL = 'https://api.dznovel.top/api';
        
        function addApiPathParam(url) {
            const separator = url.includes('?') ? '&' : '?';
            const apiPath = url.startsWith('/') ? url.substring(1) : url;
            return `${url}${separator}_api_path=${apiPath}`;
        }

        async function makeRequest(endpoint, options = {}) {
            const url = addApiPathParam(`${API_BASE_URL}${endpoint}`);
            console.log('🔍 请求URL:', url);
            
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            return response;
        }

        async function testHealthCheck() {
            const logEl = document.getElementById('health-log');
            const testEl = document.getElementById('health-test');
            
            try {
                logEl.textContent = '🔍 正在测试健康检查...';
                testEl.className = 'test-item pending';
                
                const response = await makeRequest('/health');
                const data = await response.json();
                
                if (response.ok) {
                    logEl.textContent = `✅ 健康检查成功！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                    testEl.className = 'test-item success';
                } else {
                    logEl.textContent = `❌ 健康检查失败\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                    testEl.className = 'test-item error';
                }
            } catch (error) {
                logEl.textContent = `❌ 健康检查异常: ${error.message}`;
                testEl.className = 'test-item error';
            }
        }

        async function testDashboard() {
            const logEl = document.getElementById('dashboard-log');
            const testEl = document.getElementById('dashboard-test');
            
            try {
                logEl.textContent = '🔍 正在测试仪表板数据...';
                testEl.className = 'test-item pending';
                
                const response = await makeRequest('/dashboard');
                const data = await response.json();
                
                if (response.ok) {
                    logEl.textContent = `✅ 仪表板数据获取成功！\n状态码: ${response.status}\n数据长度: ${JSON.stringify(data).length} 字符`;
                    testEl.className = 'test-item success';
                } else {
                    logEl.textContent = `❌ 仪表板数据获取失败\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                    testEl.className = 'test-item error';
                }
            } catch (error) {
                logEl.textContent = `❌ 仪表板数据异常: ${error.message}`;
                testEl.className = 'test-item error';
            }
        }

        async function testUsers() {
            const logEl = document.getElementById('users-log');
            const testEl = document.getElementById('users-test');
            
            try {
                logEl.textContent = '🔍 正在测试用户数据...';
                testEl.className = 'test-item pending';
                
                const response = await makeRequest('/users');
                const data = await response.json();
                
                if (response.ok) {
                    logEl.textContent = `✅ 用户数据获取成功！\n状态码: ${response.status}\n用户数量: ${data.length || 0}`;
                    testEl.className = 'test-item success';
                } else if (response.status === 401) {
                    logEl.textContent = `✅ 用户数据需要认证（预期行为）\n状态码: ${response.status}`;
                    testEl.className = 'test-item success';
                } else {
                    logEl.textContent = `❌ 用户数据获取失败\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                    testEl.className = 'test-item error';
                }
            } catch (error) {
                logEl.textContent = `❌ 用户数据异常: ${error.message}`;
                testEl.className = 'test-item error';
            }
        }

        async function testAdminLogin() {
            const logEl = document.getElementById('admin-login-log');
            const testEl = document.getElementById('admin-login-test');
            
            try {
                logEl.textContent = '🔍 正在测试管理员登录...';
                testEl.className = 'test-item pending';
                
                const response = await makeRequest('/admin/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'test',
                        password: 'test'
                    })
                });
                const data = await response.json();
                
                if (response.ok || response.status === 401) {
                    logEl.textContent = `✅ 管理员登录端点正常！\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                    testEl.className = 'test-item success';
                } else {
                    logEl.textContent = `❌ 管理员登录端点异常\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                    testEl.className = 'test-item error';
                }
            } catch (error) {
                logEl.textContent = `❌ 管理员登录异常: ${error.message}`;
                testEl.className = 'test-item error';
            }
        }

        async function runAllTests() {
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testDashboard();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testUsers();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testAdminLogin();
        }

        // 页面加载时显示配置信息
        window.onload = function() {
            console.log('🔧 后台管理系统API测试');
            console.log('API地址:', API_BASE_URL);
        };
    </script>
</body>
</html>
