<!DOCTYPE html>
<html>
<head>
    <title>后台管理系统API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>后台管理系统API测试</h1>
    
    <button onclick="testDashboard()">测试仪表板</button>
    <button onclick="testUsers()">测试用户列表</button>
    <button onclick="testAdminLogin()">测试管理员登录</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'https://api.dznovel.top/api';
        
        function addResult(title, success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<h3>${title}</h3><p>${message}</p>`;
            document.getElementById('results').appendChild(div);
        }
        
        async function testDashboard() {
            try {
                const response = await fetch(`${API_BASE}/dashboard?_api_path=dashboard`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('仪表板API', true, `成功获取数据: ${JSON.stringify(data, null, 2)}`);
                } else {
                    addResult('仪表板API', false, `失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                addResult('仪表板API', false, `异常: ${error.message}`);
            }
        }
        
        async function testUsers() {
            try {
                const response = await fetch(`${API_BASE}/users?_api_path=users`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('用户列表API', true, `成功获取 ${data.length} 个用户`);
                } else {
                    addResult('用户列表API', false, `失败: ${response.status} - ${JSON.stringify(data)}`);
                }
            } catch (error) {
                addResult('用户列表API', false, `异常: ${error.message}`);
            }
        }
        
        async function testAdminLogin() {
            try {
                const response = await fetch(`${API_BASE}/admin/login?_api_path=admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                
                if (response.ok) {
                    addResult('管理员登录API', true, `登录成功: ${data.message}`);
                } else {
                    addResult('管理员登录API', false, `登录失败: ${response.status} - ${data.message}`);
                }
            } catch (error) {
                addResult('管理员登录API', false, `异常: ${error.message}`);
            }
        }
    </script>
</body>
</html>
