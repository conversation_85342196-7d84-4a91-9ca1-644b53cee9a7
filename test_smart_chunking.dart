import 'dart:convert';
import 'package:http/http.dart' as http;

/// 测试智能分块上传功能
void main() async {
  print('🔍 开始测试智能分块上传功能...');
  
  final baseUrl = 'https://api.dznovel.top/api';
  
  // 模拟测试数据
  final testData = {
    'userSettings': {
      'theme': 'dark',
      'fontSize': 16,
      'autoSave': true,
    },
    'knowledgeDocuments': [
      {
        'id': 'doc1',
        'title': '测试文档1',
        'content': '这是一个测试文档的内容...',
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 'doc2',
        'title': '测试文档2',
        'content': '这是另一个测试文档的内容...',
        'createdAt': DateTime.now().toIso8601String(),
      }
    ],
    'novels': [
      {
        'id': 'novel1',
        'title': '测试小说1',
        'author': '测试作者',
        'content': '这是测试小说的内容...' * 100, // 模拟较大内容
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 'novel2',
        'title': '测试小说2',
        'author': '测试作者',
        'content': '这是另一个测试小说的内容...' * 100,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 'novel3',
        'title': '测试小说3',
        'author': '测试作者',
        'content': '第三个测试小说的内容...' * 100,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 'novel4',
        'title': '测试小说4',
        'author': '测试作者',
        'content': '第四个测试小说的内容...' * 100,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 'novel5',
        'title': '测试小说5',
        'author': '测试作者',
        'content': '第五个测试小说的内容...' * 100,
        'createdAt': DateTime.now().toIso8601String(),
      },
      {
        'id': 'novel6',
        'title': '测试小说6',
        'author': '测试作者',
        'content': '第六个测试小说的内容...' * 100,
        'createdAt': DateTime.now().toIso8601String(),
      }
    ]
  };
  
  // 计算总数据大小
  final totalDataString = jsonEncode(testData);
  final totalSizeKB = totalDataString.length / 1024;
  print('📊 总测试数据大小: ${totalSizeKB.toStringAsFixed(2)} KB');
  
  // 模拟分块创建
  final chunks = createTestDataChunks(testData);
  print('📦 分为 ${chunks.length} 个数据块:');
  
  for (int i = 0; i < chunks.length; i++) {
    final chunk = chunks[i];
    final chunkString = jsonEncode(chunk['data']);
    final chunkSizeKB = chunkString.length / 1024;
    print('   ${i + 1}. ${chunk['type']} - ${chunkSizeKB.toStringAsFixed(2)} KB');
  }
  
  // 测试分块上传端点（不需要真实Token，只测试端点可达性）
  print('\n🔍 测试分块上传端点可达性...');
  await testChunkUploadEndpoint(baseUrl);
  
  print('\n🎉 智能分块上传功能测试完成！');
}

/// 创建测试数据块
List<Map<String, dynamic>> createTestDataChunks(Map<String, dynamic> data) {
  final chunks = <Map<String, dynamic>>[];
  
  // 1. 用户设置块
  if (data['userSettings'] != null) {
    chunks.add({
      'type': 'userSettings',
      'data': {'userSettings': data['userSettings']},
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  // 2. 知识库文档块
  if (data['knowledgeDocuments'] != null) {
    chunks.add({
      'type': 'knowledgeDocuments',
      'data': {'knowledgeDocuments': data['knowledgeDocuments']},
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
  
  // 3. 小说数据块（每块最多5本）
  if (data['novels'] != null) {
    final novels = data['novels'] as List;
    final batchSize = 5;
    for (int i = 0; i < novels.length; i += batchSize) {
      final end = (i + batchSize < novels.length) ? i + batchSize : novels.length;
      final novelBatch = novels.sublist(i, end);
      
      chunks.add({
        'type': 'novels_batch_${(i ~/ batchSize) + 1}',
        'data': {'novels': novelBatch},
        'timestamp': DateTime.now().toIso8601String(),
        'batchInfo': {
          'batchNumber': (i ~/ batchSize) + 1,
          'totalBatches': (novels.length / batchSize).ceil(),
          'startIndex': i,
          'endIndex': end - 1,
        }
      });
    }
  }
  
  return chunks;
}

/// 测试分块上传端点
Future<void> testChunkUploadEndpoint(String baseUrl) async {
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/upload?_api_path=sync/upload'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token', // 测试Token
      },
      body: jsonEncode({
        'data': {'test': 'data'},
        'timestamp': DateTime.now().toIso8601String(),
        'chunkInfo': {
          'type': 'test',
          'index': 1,
          'total': 1,
        }
      }),
    ).timeout(Duration(seconds: 10));
    
    print('   状态码: ${response.statusCode}');
    
    if (response.statusCode == 401) {
      print('   ✅ 端点可达（预期的401认证错误）');
    } else if (response.statusCode == 200) {
      print('   ✅ 端点正常工作');
    } else {
      print('   ⚠️  意外的状态码');
    }
    
    if (response.body.length < 500) {
      print('   响应: ${response.body}');
    }
    
  } catch (e) {
    print('   ❌ 端点测试失败: $e');
  }
}
