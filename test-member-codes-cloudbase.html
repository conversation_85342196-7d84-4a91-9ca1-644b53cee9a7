<!DOCTYPE html>
<html>
<head>
    <title>CloudBase会员码测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>CloudBase会员码功能测试</h1>
    
    <button onclick="generateCodes()">1. 生成测试会员码</button>
    <button onclick="getMemberCodes()">2. 获取会员码列表</button>
    <button onclick="testDeleteCode()">3. 测试删除会员码</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'https://api.dznovel.top/api';
        let memberCodes = [];
        
        function addResult(title, success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<h3>${title}</h3><pre>${message}</pre>`;
            document.getElementById('results').appendChild(div);
        }
        
        async function generateCodes() {
            try {
                const response = await fetch(`${API_BASE}/member-codes/generate?_api_path=member-codes/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        packageId: 'permanent',
                        count: 3
                    })
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('生成会员码', true, 
                        `${data.message}\n生成的会员码:\n` + 
                        data.data.map(code => `- ${code.code}`).join('\n')
                    );
                } else {
                    addResult('生成会员码', false, `失败: ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                addResult('生成会员码', false, `异常: ${error.message}`);
            }
        }
        
        async function getMemberCodes() {
            try {
                const response = await fetch(`${API_BASE}/member-codes?_api_path=member-codes&page=1&size=10`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    memberCodes = data.data.memberCodes;
                    addResult('获取会员码列表', true, 
                        `成功获取 ${memberCodes.length} 个会员码:\n` + 
                        memberCodes.map(code => `- ${code.code} (ID: ${code.id}, 状态: ${code.status})`).join('\n')
                    );
                } else {
                    addResult('获取会员码列表', false, `失败: ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                addResult('获取会员码列表', false, `异常: ${error.message}`);
            }
        }
        
        async function testDeleteCode() {
            if (memberCodes.length === 0) {
                addResult('删除会员码', false, '请先获取会员码列表');
                return;
            }
            
            const codeToDelete = memberCodes[0];
            
            try {
                const response = await fetch(`${API_BASE}/member-codes/${codeToDelete.code}?_api_path=member-codes/${codeToDelete.code}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('删除会员码', true, `成功删除会员码: ${codeToDelete.code}\n响应: ${data.message}`);
                    
                    // 重新获取列表验证删除结果
                    setTimeout(() => {
                        getMemberCodes();
                    }, 1000);
                } else {
                    addResult('删除会员码', false, 
                        `删除失败 (状态码: ${response.status}):\n${JSON.stringify(data, null, 2)}`
                    );
                }
            } catch (error) {
                addResult('删除会员码', false, `异常: ${error.message}`);
            }
        }
        
        // 页面加载时显示说明
        window.onload = function() {
            addResult('使用说明', true, 
                '1. 点击"生成测试会员码"创建一些测试数据\n' +
                '2. 点击"获取会员码列表"查看CloudBase数据库中的会员码\n' +
                '3. 点击"测试删除会员码"删除第一个会员码并验证结果'
            );
        };
    </script>
</body>
</html>
