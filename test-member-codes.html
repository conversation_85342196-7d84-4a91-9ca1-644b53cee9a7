<!DOCTYPE html>
<html>
<head>
    <title>会员码数据结构测试</title>
</head>
<body>
    <h1>会员码数据结构测试</h1>
    <button onclick="testMemberCodes()">获取会员码数据</button>
    <pre id="result"></pre>

    <script>
        async function testMemberCodes() {
            try {
                const response = await fetch('https://api.dznovel.top/api/member-codes?_api_path=member-codes&page=1&size=3');
                const data = await response.json();
                
                document.getElementById('result').textContent = JSON.stringify(data, null, 2);
                
                console.log('会员码数据结构:', data);
                if (data.success && data.data.memberCodes.length > 0) {
                    console.log('第一个会员码的字段:', Object.keys(data.data.memberCodes[0]));
                }
            } catch (error) {
                document.getElementById('result').textContent = '错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
