import{d as fc,ak as su,al as Nn,am as Hn,an as hc,y as hr,ao as vc,ap as Un,aq as uu,o as cc,ar as dc,as as pc,at as gc,j as _c}from"./index-BQm3CBcS.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var bo=function(e,t){return bo=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(r[i]=n[i])},bo(e,t)};function N(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");bo(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var yc=function(){function e(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return e}(),mc=function(){function e(){this.browser=new yc,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return e}(),U=new mc;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(U.wxa=!0,U.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?U.worker=!0:!U.hasGlobalWindow||"Deno"in window?(U.node=!0,U.svgSupported=!0):wc(navigator.userAgent,U);function wc(e,t){var r=t.browser,n=e.match(/Firefox\/([\d.]+)/),i=e.match(/MSIE\s([\d.]+)/)||e.match(/Trident\/.+?rv:(([\d.]+))/),a=e.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(e);n&&(r.firefox=!0,r.version=n[1]),i&&(r.ie=!0,r.version=i[1]),a&&(r.edge=!0,r.version=a[1],r.newEdge=+a[1].split(".")[0]>18),o&&(r.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!r.ie&&!r.edge,t.pointerEventsSupported="onpointerdown"in window&&(r.edge||r.ie&&+r.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(r.ie&&"transition"in s||r.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||r.ie&&+r.version>=9}var ds=12,Sc="sans-serif",sr=ds+"px "+Sc,Tc=20,bc=100,Cc="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function Mc(e){var t={};if(typeof JSON>"u")return t;for(var r=0;r<e.length;r++){var n=String.fromCharCode(r+32),i=(e.charCodeAt(r)-Tc)/bc;t[n]=i}return t}var Dc=Mc(Cc),On={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var e,t;return function(r,n){if(!e){var i=On.createCanvas();e=i&&i.getContext("2d")}if(e)return t!==n&&(t=e.font=n||sr),e.measureText(r);r=r||"",n=n||sr;var a=/((?:\d+)?\.?\d*)px/.exec(n),o=a&&+a[1]||ds,s=0;if(n.indexOf("mono")>=0)s=o*r.length;else for(var u=0;u<r.length;u++){var l=Dc[r[u]];s+=l==null?o:l*o}return{width:s}}}(),loadImage:function(e,t,r){var n=new Image;return n.onload=t,n.onerror=r,n.src=e,n}},Vf=Ee(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(e,t){return e["[object "+t+"]"]=!0,e},{}),Gf=Ee(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(e,t){return e["[object "+t+"Array]"]=!0,e},{}),kn=Object.prototype.toString,ta=Array.prototype,Pc=ta.forEach,Rc=ta.filter,ps=ta.slice,Ac=ta.map,lu=(function(){}).constructor,Vn=lu?lu.prototype:null,gs="__proto__",xc=2311;function Yf(){return xc++}function Wf(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];typeof console<"u"&&console.error.apply(console,e)}function W(e){if(e==null||typeof e!="object")return e;var t=e,r=kn.call(e);if(r==="[object Array]"){if(!vn(e)){t=[];for(var n=0,i=e.length;n<i;n++)t[n]=W(e[n])}}else if(Gf[r]){if(!vn(e)){var a=e.constructor;if(a.from)t=a.from(e);else{t=new a(e.length);for(var n=0,i=e.length;n<i;n++)t[n]=e[n]}}}else if(!Vf[r]&&!vn(e)&&!Mo(e)){t={};for(var o in e)e.hasOwnProperty(o)&&o!==gs&&(t[o]=W(e[o]))}return t}function st(e,t,r){if(!B(t)||!B(e))return r?W(t):e;for(var n in t)if(t.hasOwnProperty(n)&&n!==gs){var i=e[n],a=t[n];B(a)&&B(i)&&!F(a)&&!F(i)&&!Mo(a)&&!Mo(i)&&!fu(a)&&!fu(i)&&!vn(a)&&!vn(i)?st(i,a,r):(r||!(n in e))&&(e[n]=W(t[n]))}return e}function L(e,t){if(Object.assign)Object.assign(e,t);else for(var r in t)t.hasOwnProperty(r)&&r!==gs&&(e[r]=t[r]);return e}function wt(e,t,r){for(var n=it(t),i=0,a=n.length;i<a;i++){var o=n[i];e[o]==null&&(e[o]=t[o])}return e}function nt(e,t){if(e){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r}return-1}function Ec(e,t){var r=e.prototype;function n(){}n.prototype=t.prototype,e.prototype=new n;for(var i in r)r.hasOwnProperty(i)&&(e.prototype[i]=r[i]);e.prototype.constructor=e,e.superClass=t}function ge(e,t,r){if(e="prototype"in e?e.prototype:e,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var n=Object.getOwnPropertyNames(t),i=0;i<n.length;i++){var a=n[i];a!=="constructor"&&e[a]==null&&(e[a]=t[a])}else wt(e,t)}function It(e){return!e||typeof e=="string"?!1:typeof e.length=="number"}function P(e,t,r){if(e&&t)if(e.forEach&&e.forEach===Pc)e.forEach(t,r);else if(e.length===+e.length)for(var n=0,i=e.length;n<i;n++)t.call(r,e[n],n,e);else for(var a in e)e.hasOwnProperty(a)&&t.call(r,e[a],a,e)}function X(e,t,r){if(!e)return[];if(!t)return Xf(e);if(e.map&&e.map===Ac)return e.map(t,r);for(var n=[],i=0,a=e.length;i<a;i++)n.push(t.call(r,e[i],i,e));return n}function Ee(e,t,r,n){if(e&&t){for(var i=0,a=e.length;i<a;i++)r=t.call(n,r,e[i],i,e);return r}}function Kt(e,t,r){if(!e)return[];if(!t)return Xf(e);if(e.filter&&e.filter===Rc)return e.filter(t,r);for(var n=[],i=0,a=e.length;i<a;i++)t.call(r,e[i],i,e)&&n.push(e[i]);return n}function it(e){if(!e)return[];if(Object.keys)return Object.keys(e);var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}function Lc(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return function(){return e.apply(t,r.concat(ps.call(arguments)))}}var ie=Vn&&at(Vn.bind)?Vn.call.bind(Vn.bind):Lc;function _s(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return function(){return e.apply(this,t.concat(ps.call(arguments)))}}function F(e){return Array.isArray?Array.isArray(e):kn.call(e)==="[object Array]"}function at(e){return typeof e=="function"}function H(e){return typeof e=="string"}function Co(e){return kn.call(e)==="[object String]"}function ut(e){return typeof e=="number"}function B(e){var t=typeof e;return t==="function"||!!e&&t==="object"}function fu(e){return!!Vf[kn.call(e)]}function Dt(e){return!!Gf[kn.call(e)]}function Mo(e){return typeof e=="object"&&typeof e.nodeType=="number"&&typeof e.ownerDocument=="object"}function ys(e){return e.colorStops!=null}function S1(e){return e.image!=null}function Ic(e){return e!==e}function T1(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=0,n=e.length;r<n;r++)if(e[r]!=null)return e[r]}function Y(e,t){return e??t}function wi(e,t,r){return e??t??r}function Xf(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return ps.apply(e,t)}function qf(e){if(typeof e=="number")return[e,e,e,e];var t=e.length;return t===2?[e[0],e[1],e[0],e[1]]:t===3?[e[0],e[1],e[2],e[1]]:e}function pe(e,t){if(!e)throw new Error(t)}function ce(e){return e==null?null:typeof e.trim=="function"?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var $f="__ec_primitive__";function Do(e){e[$f]=!0}function vn(e){return e[$f]}var Oc=function(){function e(){this.data={}}return e.prototype.delete=function(t){var r=this.has(t);return r&&delete this.data[t],r},e.prototype.has=function(t){return this.data.hasOwnProperty(t)},e.prototype.get=function(t){return this.data[t]},e.prototype.set=function(t,r){return this.data[t]=r,this},e.prototype.keys=function(){return it(this.data)},e.prototype.forEach=function(t){var r=this.data;for(var n in r)r.hasOwnProperty(n)&&t(r[n],n)},e}(),Zf=typeof Map=="function";function kc(){return Zf?new Map:new Oc}var Fc=function(){function e(t){var r=F(t);this.data=kc();var n=this;t instanceof e?t.each(i):t&&P(t,i);function i(a,o){r?n.set(a,o):n.set(o,a)}}return e.prototype.hasKey=function(t){return this.data.has(t)},e.prototype.get=function(t){return this.data.get(t)},e.prototype.set=function(t,r){return this.data.set(t,r),r},e.prototype.each=function(t,r){this.data.forEach(function(n,i){t.call(r,n,i)})},e.prototype.keys=function(){var t=this.data.keys();return Zf?Array.from(t):t},e.prototype.removeKey=function(t){this.data.delete(t)},e}();function Z(e){return new Fc(e)}function Bc(e,t){for(var r=new e.constructor(e.length+t.length),n=0;n<e.length;n++)r[n]=e[n];for(var i=e.length,n=0;n<t.length;n++)r[n+i]=t[n];return r}function ea(e,t){var r;if(Object.create)r=Object.create(e);else{var n=function(){};n.prototype=e,r=new n}return t&&L(r,t),r}function b1(e){var t=e.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Fr(e,t){return e.hasOwnProperty(t)}function Vt(){}var zc=180/Math.PI;function Nr(e,t){return e==null&&(e=0),t==null&&(t=0),[e,t]}function Nc(e){return[e[0],e[1]]}function hu(e,t,r){return e[0]=t[0]+r[0],e[1]=t[1]+r[1],e}function Hc(e,t,r){return e[0]=t[0]-r[0],e[1]=t[1]-r[1],e}function Uc(e){return Math.sqrt(Vc(e))}function Vc(e){return e[0]*e[0]+e[1]*e[1]}function ma(e,t,r){return e[0]=t[0]*r,e[1]=t[1]*r,e}function Gc(e,t){var r=Uc(t);return r===0?(e[0]=0,e[1]=0):(e[0]=t[0]/r,e[1]=t[1]/r),e}function Po(e,t){return Math.sqrt((e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1]))}var Yc=Po;function Wc(e,t){return(e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1])}var Lr=Wc;function C1(e,t,r,n){return e[0]=t[0]+n*(r[0]-t[0]),e[1]=t[1]+n*(r[1]-t[1]),e}function Ir(e,t,r){var n=t[0],i=t[1];return e[0]=r[0]*n+r[2]*i+r[4],e[1]=r[1]*n+r[3]*i+r[5],e}function Pr(e,t,r){return e[0]=Math.min(t[0],r[0]),e[1]=Math.min(t[1],r[1]),e}function Rr(e,t,r){return e[0]=Math.max(t[0],r[0]),e[1]=Math.max(t[1],r[1]),e}var vr=function(){function e(t,r){this.target=t,this.topTarget=r&&r.topTarget}return e}(),Xc=function(){function e(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return e.prototype._dragStart=function(t){for(var r=t.target;r&&!r.draggable;)r=r.parent||r.__hostTarget;r&&(this._draggingTarget=r,r.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new vr(r,t),"dragstart",t.event))},e.prototype._drag=function(t){var r=this._draggingTarget;if(r){var n=t.offsetX,i=t.offsetY,a=n-this._x,o=i-this._y;this._x=n,this._y=i,r.drift(a,o,t),this.handler.dispatchToElement(new vr(r,t),"drag",t.event);var s=this.handler.findHover(n,i,r).target,u=this._dropTarget;this._dropTarget=s,r!==s&&(u&&s!==u&&this.handler.dispatchToElement(new vr(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new vr(s,t),"dragenter",t.event))}},e.prototype._dragEnd=function(t){var r=this._draggingTarget;r&&(r.dragging=!1),this.handler.dispatchToElement(new vr(r,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new vr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},e}(),_e=function(){function e(t){t&&(this._$eventProcessor=t)}return e.prototype.on=function(t,r,n,i){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof r=="function"&&(i=n,n=r,r=null),!n||!t)return this;var o=this._$eventProcessor;r!=null&&o&&o.normalizeQuery&&(r=o.normalizeQuery(r)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===n)return this;var u={h:n,query:r,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=a[t].length-1,f=a[t][l];return f&&f.callAtLast?a[t].splice(l,0,u):a[t].push(u),this},e.prototype.isSilent=function(t){var r=this._$handlers;return!r||!r[t]||!r[t].length},e.prototype.off=function(t,r){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(r){if(n[t]){for(var i=[],a=0,o=n[t].length;a<o;a++)n[t][a].h!==r&&i.push(n[t][a]);n[t]=i}n[t]&&n[t].length===0&&delete n[t]}else delete n[t];return this},e.prototype.trigger=function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],a=this._$eventProcessor;if(i)for(var o=r.length,s=i.length,u=0;u<s;u++){var l=i[u];if(!(a&&a.filter&&l.query!=null&&!a.filter(t,l.query)))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,r[0]);break;case 2:l.h.call(l.ctx,r[0],r[1]);break;default:l.h.apply(l.ctx,r);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},e.prototype.triggerWithContext=function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],a=this._$eventProcessor;if(i)for(var o=r.length,s=r[o-1],u=i.length,l=0;l<u;l++){var f=i[l];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,r[0]);break;case 2:f.h.call(s,r[0],r[1]);break;default:f.h.apply(s,r.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},e}(),qc=Math.log(2);function Ro(e,t,r,n,i,a){var o=n+"-"+i,s=e.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var u=Math.round(Math.log((1<<s)-1&~i)/qc);return e[r][u]}for(var l=n|1<<r,f=r+1;n&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var d=1<<c;d&i||(h+=(v%2?-1:1)*e[r][c]*Ro(e,t-1,f,l,i|d,a),v++)}return a[o]=h,h}function vu(e,t){var r=[[e[0],e[1],1,0,0,0,-t[0]*e[0],-t[0]*e[1]],[0,0,0,e[0],e[1],1,-t[1]*e[0],-t[1]*e[1]],[e[2],e[3],1,0,0,0,-t[2]*e[2],-t[2]*e[3]],[0,0,0,e[2],e[3],1,-t[3]*e[2],-t[3]*e[3]],[e[4],e[5],1,0,0,0,-t[4]*e[4],-t[4]*e[5]],[0,0,0,e[4],e[5],1,-t[5]*e[4],-t[5]*e[5]],[e[6],e[7],1,0,0,0,-t[6]*e[6],-t[6]*e[7]],[0,0,0,e[6],e[7],1,-t[7]*e[6],-t[7]*e[7]]],n={},i=Ro(r,8,0,0,0,n);if(i!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*Ro(r,7,o===0?1:0,1<<o,1<<s,n)/i*t[o];return function(u,l,f){var h=l*a[6]+f*a[7]+1;u[0]=(l*a[0]+f*a[1]+a[2])/h,u[1]=(l*a[3]+f*a[4]+a[5])/h}}}var cu="___zrEVENTSAVED",wa=[];function M1(e,t,r,n,i){return Ao(wa,t,n,i,!0)&&Ao(e,r,wa[0],wa[1])}function Ao(e,t,r,n,i){if(t.getBoundingClientRect&&U.domSupported&&!Kf(t)){var a=t[cu]||(t[cu]={}),o=$c(t,a),s=Zc(o,a,i);if(s)return s(e,r,n),!0}return!1}function $c(e,t){var r=t.markers;if(r)return r;r=t.markers=[];for(var n=["left","right"],i=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,u=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",n[u]+":0",i[l]+":0",n[1-u]+":auto",i[1-l]+":auto",""].join("!important;"),e.appendChild(o),r.push(o)}return r}function Zc(e,t,r){for(var n=r?"invTrans":"trans",i=t[n],a=t.srcCoords,o=[],s=[],u=!0,l=0;l<4;l++){var f=e[l].getBoundingClientRect(),h=2*l,c=f.left,v=f.top;o.push(c,v),u=u&&a&&c===a[h]&&v===a[h+1],s.push(e[l].offsetLeft,e[l].offsetTop)}return u&&i?i:(t.srcCoords=o,t[n]=r?vu(s,o):vu(o,s))}function Kf(e){return e.nodeName.toUpperCase()==="CANVAS"}var Kc=/([&<>"'])/g,Qc={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function At(e){return e==null?"":(e+"").replace(Kc,function(t,r){return Qc[r]})}var Jc=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Sa=[],jc=U.browser.firefox&&+U.browser.version.split(".")[0]<39;function xo(e,t,r,n){return r=r||{},n?du(e,t,r):jc&&t.layerX!=null&&t.layerX!==t.offsetX?(r.zrX=t.layerX,r.zrY=t.layerY):t.offsetX!=null?(r.zrX=t.offsetX,r.zrY=t.offsetY):du(e,t,r),r}function du(e,t,r){if(U.domSupported&&e.getBoundingClientRect){var n=t.clientX,i=t.clientY;if(Kf(e)){var a=e.getBoundingClientRect();r.zrX=n-a.left,r.zrY=i-a.top;return}else if(Ao(Sa,e,n,i)){r.zrX=Sa[0],r.zrY=Sa[1];return}}r.zrX=r.zrY=0}function ms(e){return e||window.event}function qt(e,t,r){if(t=ms(t),t.zrX!=null)return t;var n=t.type,i=n&&n.indexOf("touch")>=0;if(i){var o=n!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&xo(e,o,t,r)}else{xo(e,t,t,r);var a=td(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&Jc.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function td(e){var t=e.wheelDelta;if(t)return t;var r=e.deltaX,n=e.deltaY;if(r==null||n==null)return t;var i=Math.abs(n!==0?n:r),a=n>0?-1:n<0?1:r>0?-1:1;return 3*i*a}function ed(e,t,r,n){e.addEventListener(t,r,n)}function rd(e,t,r,n){e.removeEventListener(t,r,n)}var nd=function(e){e.preventDefault(),e.stopPropagation(),e.cancelBubble=!0},id=function(){function e(){this._track=[]}return e.prototype.recognize=function(t,r,n){return this._doTrack(t,r,n),this._recognize(t)},e.prototype.clear=function(){return this._track.length=0,this},e.prototype._doTrack=function(t,r,n){var i=t.touches;if(i){for(var a={points:[],touches:[],target:r,event:t},o=0,s=i.length;o<s;o++){var u=i[o],l=xo(n,u,{});a.points.push([l.zrX,l.zrY]),a.touches.push(u)}this._track.push(a)}},e.prototype._recognize=function(t){for(var r in Ta)if(Ta.hasOwnProperty(r)){var n=Ta[r](this._track,t);if(n)return n}},e}();function pu(e){var t=e[1][0]-e[0][0],r=e[1][1]-e[0][1];return Math.sqrt(t*t+r*r)}function ad(e){return[(e[0][0]+e[1][0])/2,(e[0][1]+e[1][1])/2]}var Ta={pinch:function(e,t){var r=e.length;if(r){var n=(e[r-1]||{}).points,i=(e[r-2]||{}).points||n;if(i&&i.length>1&&n&&n.length>1){var a=pu(n)/pu(i);!isFinite(a)&&(a=1),t.pinchScale=a;var o=ad(n);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:e[0].target,event:t}}}}};function cn(){return[1,0,0,1,0,0]}function Qf(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=1,e[4]=0,e[5]=0,e}function od(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e}function Si(e,t,r){var n=t[0]*r[0]+t[2]*r[1],i=t[1]*r[0]+t[3]*r[1],a=t[0]*r[2]+t[2]*r[3],o=t[1]*r[2]+t[3]*r[3],s=t[0]*r[4]+t[2]*r[5]+t[4],u=t[1]*r[4]+t[3]*r[5]+t[5];return e[0]=n,e[1]=i,e[2]=a,e[3]=o,e[4]=s,e[5]=u,e}function gu(e,t,r){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4]+r[0],e[5]=t[5]+r[1],e}function sd(e,t,r,n){n===void 0&&(n=[0,0]);var i=t[0],a=t[2],o=t[4],s=t[1],u=t[3],l=t[5],f=Math.sin(r),h=Math.cos(r);return e[0]=i*h+s*f,e[1]=-i*f+s*h,e[2]=a*h+u*f,e[3]=-a*f+h*u,e[4]=h*(o-n[0])+f*(l-n[1])+n[0],e[5]=h*(l-n[1])-f*(o-n[0])+n[1],e}function ud(e,t,r){var n=r[0],i=r[1];return e[0]=t[0]*n,e[1]=t[1]*i,e[2]=t[2]*n,e[3]=t[3]*i,e[4]=t[4]*n,e[5]=t[5]*i,e}function Jf(e,t){var r=t[0],n=t[2],i=t[4],a=t[1],o=t[3],s=t[5],u=r*o-a*n;return u?(u=1/u,e[0]=o*u,e[1]=-a*u,e[2]=-n*u,e[3]=r*u,e[4]=(n*s-o*i)*u,e[5]=(a*i-r*s)*u,e):null}var $=function(){function e(t,r){this.x=t||0,this.y=r||0}return e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,r){return this.x=t,this.y=r,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,r){this.x+=t.x*r,this.y+=t.y*r},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var r=this.x-t.x,n=this.y-t.y;return Math.sqrt(r*r+n*n)},e.prototype.distanceSquare=function(t){var r=this.x-t.x,n=this.y-t.y;return r*r+n*n},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){if(t){var r=this.x,n=this.y;return this.x=t[0]*r+t[2]*n+t[4],this.y=t[1]*r+t[3]*n+t[5],this}},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,r,n){t.x=r,t.y=n},e.copy=function(t,r){t.x=r.x,t.y=r.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,r){return t.x*r.x+t.y*r.y},e.add=function(t,r,n){t.x=r.x+n.x,t.y=r.y+n.y},e.sub=function(t,r,n){t.x=r.x-n.x,t.y=r.y-n.y},e.scale=function(t,r,n){t.x=r.x*n,t.y=r.y*n},e.scaleAndAdd=function(t,r,n,i){t.x=r.x+n.x*i,t.y=r.y+n.y*i},e.lerp=function(t,r,n,i){var a=1-i;t.x=a*r.x+i*n.x,t.y=a*r.y+i*n.y},e}(),Gn=Math.min,Yn=Math.max,Fe=new $,Be=new $,ze=new $,Ne=new $,Yr=new $,Wr=new $,tt=function(){function e(t,r,n,i){n<0&&(t=t+n,n=-n),i<0&&(r=r+i,i=-i),this.x=t,this.y=r,this.width=n,this.height=i}return e.prototype.union=function(t){var r=Gn(t.x,this.x),n=Gn(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Yn(t.x+t.width,this.x+this.width)-r:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Yn(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=r,this.y=n},e.prototype.applyTransform=function(t){e.applyTransform(this,this,t)},e.prototype.calculateTransform=function(t){var r=this,n=t.width/r.width,i=t.height/r.height,a=cn();return gu(a,a,[-r.x,-r.y]),ud(a,a,[n,i]),gu(a,a,[t.x,t.y]),a},e.prototype.intersect=function(t,r){if(!t)return!1;t instanceof e||(t=e.create(t));var n=this,i=n.x,a=n.x+n.width,o=n.y,s=n.y+n.height,u=t.x,l=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<u||l<i||s<f||h<o);if(r){var v=1/0,d=0,_=Math.abs(a-u),p=Math.abs(l-i),g=Math.abs(s-f),y=Math.abs(h-o),m=Math.min(_,p),w=Math.min(g,y);a<u||l<i?m>d&&(d=m,_<p?$.set(Wr,-_,0):$.set(Wr,p,0)):m<v&&(v=m,_<p?$.set(Yr,_,0):$.set(Yr,-p,0)),s<f||h<o?w>d&&(d=w,g<y?$.set(Wr,0,-g):$.set(Wr,0,y)):m<v&&(v=m,g<y?$.set(Yr,0,g):$.set(Yr,0,-y))}return r&&$.copy(r,c?Yr:Wr),c},e.prototype.contain=function(t,r){var n=this;return t>=n.x&&t<=n.x+n.width&&r>=n.y&&r<=n.y+n.height},e.prototype.clone=function(){return new e(this.x,this.y,this.width,this.height)},e.prototype.copy=function(t){e.copy(this,t)},e.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},e.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},e.prototype.isZero=function(){return this.width===0||this.height===0},e.create=function(t){return new e(t.x,t.y,t.width,t.height)},e.copy=function(t,r){t.x=r.x,t.y=r.y,t.width=r.width,t.height=r.height},e.applyTransform=function(t,r,n){if(!n){t!==r&&e.copy(t,r);return}if(n[1]<1e-5&&n[1]>-1e-5&&n[2]<1e-5&&n[2]>-1e-5){var i=n[0],a=n[3],o=n[4],s=n[5];t.x=r.x*i+o,t.y=r.y*a+s,t.width=r.width*i,t.height=r.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}Fe.x=ze.x=r.x,Fe.y=Ne.y=r.y,Be.x=Ne.x=r.x+r.width,Be.y=ze.y=r.y+r.height,Fe.transform(n),Ne.transform(n),Be.transform(n),ze.transform(n),t.x=Gn(Fe.x,Be.x,ze.x,Ne.x),t.y=Gn(Fe.y,Be.y,ze.y,Ne.y);var u=Yn(Fe.x,Be.x,ze.x,Ne.x),l=Yn(Fe.y,Be.y,ze.y,Ne.y);t.width=u-t.x,t.height=l-t.y},e}(),jf="silent";function ld(e,t,r){return{type:e,event:r,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:r.zrX,offsetY:r.zrY,gestureEvent:r.gestureEvent,pinchX:r.pinchX,pinchY:r.pinchY,pinchScale:r.pinchScale,wheelDelta:r.zrDelta,zrByTouch:r.zrByTouch,which:r.which,stop:fd}}function fd(){nd(this.event)}var hd=function(e){N(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.handler=null,r}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(_e),Xr=function(){function e(t,r){this.x=t,this.y=r}return e}(),vd=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],ba=new tt(0,0,0,0),th=function(e){N(t,e);function t(r,n,i,a,o){var s=e.call(this)||this;return s._hovered=new Xr(0,0),s.storage=r,s.painter=n,s.painterRoot=a,s._pointerSize=o,i=i||new hd,s.proxy=null,s.setHandlerProxy(i),s._draggingMgr=new Xc(s),s}return t.prototype.setHandlerProxy=function(r){this.proxy&&this.proxy.dispose(),r&&(P(vd,function(n){r.on&&r.on(n,this[n],this)},this),r.handler=this),this.proxy=r},t.prototype.mousemove=function(r){var n=r.zrX,i=r.zrY,a=eh(this,n,i),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var u=this._hovered=a?new Xr(n,i):this.findHover(n,i),l=u.target,f=this.proxy;f.setCursor&&f.setCursor(l?l.cursor:"default"),s&&l!==s&&this.dispatchToElement(o,"mouseout",r),this.dispatchToElement(u,"mousemove",r),l&&l!==s&&this.dispatchToElement(u,"mouseover",r)},t.prototype.mouseout=function(r){var n=r.zrEventControl;n!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",r),n!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:r})},t.prototype.resize=function(){this._hovered=new Xr(0,0)},t.prototype.dispatch=function(r,n){var i=this[r];i&&i.call(this,n)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(r){var n=this.proxy;n.setCursor&&n.setCursor(r)},t.prototype.dispatchToElement=function(r,n,i){r=r||{};var a=r.target;if(!(a&&a.silent)){for(var o="on"+n,s=ld(n,r,i);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(n,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(n,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(u){typeof u[o]=="function"&&u[o].call(u,s),u.trigger&&u.trigger(n,s)}))}},t.prototype.findHover=function(r,n,i){var a=this.storage.getDisplayList(),o=new Xr(r,n);if(_u(a,o,r,n,i),this._pointerSize&&!o.target){for(var s=[],u=this._pointerSize,l=u/2,f=new tt(r-l,n-l,u,u),h=a.length-1;h>=0;h--){var c=a[h];c!==i&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(ba.copy(c.getBoundingRect()),c.transform&&ba.applyTransform(c.transform),ba.intersect(f)&&s.push(c))}if(s.length)for(var v=4,d=Math.PI/12,_=Math.PI*2,p=0;p<l;p+=v)for(var g=0;g<_;g+=d){var y=r+p*Math.cos(g),m=n+p*Math.sin(g);if(_u(s,o,y,m,i),o.target)return o}}return o},t.prototype.processGesture=function(r,n){this._gestureMgr||(this._gestureMgr=new id);var i=this._gestureMgr;n==="start"&&i.clear();var a=i.recognize(r,this.findHover(r.zrX,r.zrY,null).target,this.proxy.dom);if(n==="end"&&i.clear(),a){var o=a.type;r.gestureEvent=o;var s=new Xr;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(_e);P(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(e){th.prototype[e]=function(t){var r=t.zrX,n=t.zrY,i=eh(this,r,n),a,o;if((e!=="mouseup"||!i)&&(a=this.findHover(r,n),o=a.target),e==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(e==="mouseup")this._upEl=o;else if(e==="click"){if(this._downEl!==this._upEl||!this._downPoint||Yc(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,e,t)}});function cd(e,t,r){if(e[e.rectHover?"rectContain":"contain"](t,r)){for(var n=e,i=void 0,a=!1;n;){if(n.ignoreClip&&(a=!0),!a){var o=n.getClipPath();if(o&&!o.contain(t,r))return!1}n.silent&&(i=!0);var s=n.__hostTarget;n=s||n.parent}return i?jf:!0}return!1}function _u(e,t,r,n,i){for(var a=e.length-1;a>=0;a--){var o=e[a],s=void 0;if(o!==i&&!o.ignore&&(s=cd(o,r,n))&&(!t.topTarget&&(t.topTarget=o),s!==jf)){t.target=o;break}}}function eh(e,t,r){var n=e.painter;return t<0||t>n.getWidth()||r<0||r>n.getHeight()}var rh=32,qr=7;function dd(e){for(var t=0;e>=rh;)t|=e&1,e>>=1;return e+t}function yu(e,t,r,n){var i=t+1;if(i===r)return 1;if(n(e[i++],e[t])<0){for(;i<r&&n(e[i],e[i-1])<0;)i++;pd(e,t,i)}else for(;i<r&&n(e[i],e[i-1])>=0;)i++;return i-t}function pd(e,t,r){for(r--;t<r;){var n=e[t];e[t++]=e[r],e[r--]=n}}function mu(e,t,r,n,i){for(n===t&&n++;n<r;n++){for(var a=e[n],o=t,s=n,u;o<s;)u=o+s>>>1,i(a,e[u])<0?s=u:o=u+1;var l=n-o;switch(l){case 3:e[o+3]=e[o+2];case 2:e[o+2]=e[o+1];case 1:e[o+1]=e[o];break;default:for(;l>0;)e[o+l]=e[o+l-1],l--}e[o]=a}}function Ca(e,t,r,n,i,a){var o=0,s=0,u=1;if(a(e,t[r+i])>0){for(s=n-i;u<s&&a(e,t[r+i+u])>0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=i,u+=i}else{for(s=i+1;u<s&&a(e,t[r+i-u])<=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=i-u,u=i-l}for(o++;o<u;){var f=o+(u-o>>>1);a(e,t[r+f])>0?o=f+1:u=f}return u}function Ma(e,t,r,n,i,a){var o=0,s=0,u=1;if(a(e,t[r+i])<0){for(s=i+1;u<s&&a(e,t[r+i-u])<0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=i-u,u=i-l}else{for(s=n-i;u<s&&a(e,t[r+i+u])>=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=i,u+=i}for(o++;o<u;){var f=o+(u-o>>>1);a(e,t[r+f])<0?u=f:o=f+1}return u}function gd(e,t){var r=qr,n,i,a=0,o=[];n=[],i=[];function s(v,d){n[a]=v,i[a]=d,a+=1}function u(){for(;a>1;){var v=a-2;if(v>=1&&i[v-1]<=i[v]+i[v+1]||v>=2&&i[v-2]<=i[v]+i[v-1])i[v-1]<i[v+1]&&v--;else if(i[v]>i[v+1])break;f(v)}}function l(){for(;a>1;){var v=a-2;v>0&&i[v-1]<i[v+1]&&v--,f(v)}}function f(v){var d=n[v],_=i[v],p=n[v+1],g=i[v+1];i[v]=_+g,v===a-3&&(n[v+1]=n[v+2],i[v+1]=i[v+2]),a--;var y=Ma(e[p],e,d,_,0,t);d+=y,_-=y,_!==0&&(g=Ca(e[d+_-1],e,p,g,g-1,t),g!==0&&(_<=g?h(d,_,p,g):c(d,_,p,g)))}function h(v,d,_,p){var g=0;for(g=0;g<d;g++)o[g]=e[v+g];var y=0,m=_,w=v;if(e[w++]=e[m++],--p===0){for(g=0;g<d;g++)e[w+g]=o[y+g];return}if(d===1){for(g=0;g<p;g++)e[w+g]=e[m+g];e[w+p]=o[y];return}for(var T=r,S,b,C;;){S=0,b=0,C=!1;do if(t(e[m],o[y])<0){if(e[w++]=e[m++],b++,S=0,--p===0){C=!0;break}}else if(e[w++]=o[y++],S++,b=0,--d===1){C=!0;break}while((S|b)<T);if(C)break;do{if(S=Ma(e[m],o,y,d,0,t),S!==0){for(g=0;g<S;g++)e[w+g]=o[y+g];if(w+=S,y+=S,d-=S,d<=1){C=!0;break}}if(e[w++]=e[m++],--p===0){C=!0;break}if(b=Ca(o[y],e,m,p,0,t),b!==0){for(g=0;g<b;g++)e[w+g]=e[m+g];if(w+=b,m+=b,p-=b,p===0){C=!0;break}}if(e[w++]=o[y++],--d===1){C=!0;break}T--}while(S>=qr||b>=qr);if(C)break;T<0&&(T=0),T+=2}if(r=T,r<1&&(r=1),d===1){for(g=0;g<p;g++)e[w+g]=e[m+g];e[w+p]=o[y]}else{if(d===0)throw new Error;for(g=0;g<d;g++)e[w+g]=o[y+g]}}function c(v,d,_,p){var g=0;for(g=0;g<p;g++)o[g]=e[_+g];var y=v+d-1,m=p-1,w=_+p-1,T=0,S=0;if(e[w--]=e[y--],--d===0){for(T=w-(p-1),g=0;g<p;g++)e[T+g]=o[g];return}if(p===1){for(w-=d,y-=d,S=w+1,T=y+1,g=d-1;g>=0;g--)e[S+g]=e[T+g];e[w]=o[m];return}for(var b=r;;){var C=0,M=0,A=!1;do if(t(o[m],e[y])<0){if(e[w--]=e[y--],C++,M=0,--d===0){A=!0;break}}else if(e[w--]=o[m--],M++,C=0,--p===1){A=!0;break}while((C|M)<b);if(A)break;do{if(C=d-Ma(o[m],e,v,d,d-1,t),C!==0){for(w-=C,y-=C,d-=C,S=w+1,T=y+1,g=C-1;g>=0;g--)e[S+g]=e[T+g];if(d===0){A=!0;break}}if(e[w--]=o[m--],--p===1){A=!0;break}if(M=p-Ca(e[y],o,0,p,p-1,t),M!==0){for(w-=M,m-=M,p-=M,S=w+1,T=m+1,g=0;g<M;g++)e[S+g]=o[T+g];if(p<=1){A=!0;break}}if(e[w--]=e[y--],--d===0){A=!0;break}b--}while(C>=qr||M>=qr);if(A)break;b<0&&(b=0),b+=2}if(r=b,r<1&&(r=1),p===1){for(w-=d,y-=d,S=w+1,T=y+1,g=d-1;g>=0;g--)e[S+g]=e[T+g];e[w]=o[m]}else{if(p===0)throw new Error;for(T=w-(p-1),g=0;g<p;g++)e[T+g]=o[g]}}return{mergeRuns:u,forceMergeRuns:l,pushRun:s}}function Ti(e,t,r,n){r||(r=0),n||(n=e.length);var i=n-r;if(!(i<2)){var a=0;if(i<rh){a=yu(e,r,n,t),mu(e,r,n,r+a,t);return}var o=gd(e,t),s=dd(i);do{if(a=yu(e,r,n,t),a<s){var u=i;u>s&&(u=s),mu(e,r,r+u,r+a,t),a=u}o.pushRun(r,a),o.mergeRuns(),i-=a,r+=a}while(i!==0);o.forceMergeRuns()}}var ae=1,on=2,Mr=4,wu=!1;function Da(){wu||(wu=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Su(e,t){return e.zlevel===t.zlevel?e.z===t.z?e.z2-t.z2:e.z-t.z:e.zlevel-t.zlevel}var _d=function(){function e(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Su}return e.prototype.traverse=function(t,r){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,r)},e.prototype.getDisplayList=function(t,r){r=r||!1;var n=this._displayList;return(t||!n.length)&&this.updateDisplayList(r),n},e.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var r=this._roots,n=this._displayList,i=0,a=r.length;i<a;i++)this._updateAndAddDisplayable(r[i],null,t);n.length=this._displayListLen,Ti(n,Su)},e.prototype._updateAndAddDisplayable=function(t,r,n){if(!(t.ignore&&!n)){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)r=null;else if(i){r?r=r.slice():r=[];for(var a=i,o=t;a;)a.parent=o,a.updateTransform(),r.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),u=0;u<s.length;u++){var l=s[u];t.__dirty&&(l.__dirty|=ae),this._updateAndAddDisplayable(l,r,n)}t.__dirty=0}else{var f=t;r&&r.length?f.__clipPaths=r:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(Da(),f.z=0),isNaN(f.z2)&&(Da(),f.z2=0),isNaN(f.zlevel)&&(Da(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,r,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,r,n);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,r,n)}},e.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},e.prototype.delRoot=function(t){if(t instanceof Array){for(var r=0,n=t.length;r<n;r++)this.delRoot(t[r]);return}var i=nt(this._roots,t);i>=0&&this._roots.splice(i,1)},e.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},e.prototype.getRoots=function(){return this._roots},e.prototype.dispose=function(){this._displayList=null,this._roots=null},e}(),Eo;Eo=U.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(e){return setTimeout(e,16)};var dn={linear:function(e){return e},quadraticIn:function(e){return e*e},quadraticOut:function(e){return e*(2-e)},quadraticInOut:function(e){return(e*=2)<1?.5*e*e:-.5*(--e*(e-2)-1)},cubicIn:function(e){return e*e*e},cubicOut:function(e){return--e*e*e+1},cubicInOut:function(e){return(e*=2)<1?.5*e*e*e:.5*((e-=2)*e*e+2)},quarticIn:function(e){return e*e*e*e},quarticOut:function(e){return 1- --e*e*e*e},quarticInOut:function(e){return(e*=2)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)},quinticIn:function(e){return e*e*e*e*e},quinticOut:function(e){return--e*e*e*e*e+1},quinticInOut:function(e){return(e*=2)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2)},sinusoidalIn:function(e){return 1-Math.cos(e*Math.PI/2)},sinusoidalOut:function(e){return Math.sin(e*Math.PI/2)},sinusoidalInOut:function(e){return .5*(1-Math.cos(Math.PI*e))},exponentialIn:function(e){return e===0?0:Math.pow(1024,e-1)},exponentialOut:function(e){return e===1?1:1-Math.pow(2,-10*e)},exponentialInOut:function(e){return e===0?0:e===1?1:(e*=2)<1?.5*Math.pow(1024,e-1):.5*(-Math.pow(2,-10*(e-1))+2)},circularIn:function(e){return 1-Math.sqrt(1-e*e)},circularOut:function(e){return Math.sqrt(1- --e*e)},circularInOut:function(e){return(e*=2)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)},elasticIn:function(e){var t,r=.1,n=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=n/4):t=n*Math.asin(1/r)/(2*Math.PI),-(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)))},elasticOut:function(e){var t,r=.1,n=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=n/4):t=n*Math.asin(1/r)/(2*Math.PI),r*Math.pow(2,-10*e)*Math.sin((e-t)*(2*Math.PI)/n)+1)},elasticInOut:function(e){var t,r=.1,n=.4;return e===0?0:e===1?1:(!r||r<1?(r=1,t=n/4):t=n*Math.asin(1/r)/(2*Math.PI),(e*=2)<1?-.5*(r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)):r*Math.pow(2,-10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)*.5+1)},backIn:function(e){var t=1.70158;return e*e*((t+1)*e-t)},backOut:function(e){var t=1.70158;return--e*e*((t+1)*e+t)+1},backInOut:function(e){var t=2.5949095;return(e*=2)<1?.5*(e*e*((t+1)*e-t)):.5*((e-=2)*e*((t+1)*e+t)+2)},bounceIn:function(e){return 1-dn.bounceOut(1-e)},bounceOut:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},bounceInOut:function(e){return e<.5?dn.bounceIn(e*2)*.5:dn.bounceOut(e*2-1)*.5+.5}},Wn=Math.pow,Re=Math.sqrt,Oi=1e-8,nh=1e-4,Tu=Re(3),Xn=1/3,oe=Nr(),Nt=Nr(),Or=Nr();function Pe(e){return e>-Oi&&e<Oi}function ih(e){return e>Oi||e<-Oi}function mt(e,t,r,n,i){var a=1-i;return a*a*(a*e+3*i*t)+i*i*(i*n+3*a*r)}function bu(e,t,r,n,i){var a=1-i;return 3*(((t-e)*a+2*(r-t)*i)*a+(n-r)*i*i)}function ah(e,t,r,n,i,a){var o=n+3*(t-r)-e,s=3*(r-t*2+e),u=3*(t-e),l=e-i,f=s*s-3*o*u,h=s*u-9*o*l,c=u*u-3*s*l,v=0;if(Pe(f)&&Pe(h))if(Pe(s))a[0]=0;else{var d=-u/s;d>=0&&d<=1&&(a[v++]=d)}else{var _=h*h-4*f*c;if(Pe(_)){var p=h/f,d=-s/o+p,g=-p/2;d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g)}else if(_>0){var y=Re(_),m=f*s+1.5*o*(-h+y),w=f*s+1.5*o*(-h-y);m<0?m=-Wn(-m,Xn):m=Wn(m,Xn),w<0?w=-Wn(-w,Xn):w=Wn(w,Xn);var d=(-s-(m+w))/(3*o);d>=0&&d<=1&&(a[v++]=d)}else{var T=(2*f*s-3*o*h)/(2*Re(f*f*f)),S=Math.acos(T)/3,b=Re(f),C=Math.cos(S),d=(-s-2*b*C)/(3*o),g=(-s+b*(C+Tu*Math.sin(S)))/(3*o),M=(-s+b*(C-Tu*Math.sin(S)))/(3*o);d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g),M>=0&&M<=1&&(a[v++]=M)}}return v}function oh(e,t,r,n,i){var a=6*r-12*t+6*e,o=9*t+3*n-3*e-9*r,s=3*t-3*e,u=0;if(Pe(o)){if(ih(a)){var l=-s/a;l>=0&&l<=1&&(i[u++]=l)}}else{var f=a*a-4*o*s;if(Pe(f))i[0]=-a/(2*o);else if(f>0){var h=Re(f),l=(-a+h)/(2*o),c=(-a-h)/(2*o);l>=0&&l<=1&&(i[u++]=l),c>=0&&c<=1&&(i[u++]=c)}}return u}function ki(e,t,r,n,i,a){var o=(t-e)*i+e,s=(r-t)*i+t,u=(n-r)*i+r,l=(s-o)*i+o,f=(u-s)*i+s,h=(f-l)*i+l;a[0]=e,a[1]=o,a[2]=l,a[3]=h,a[4]=h,a[5]=f,a[6]=u,a[7]=n}function yd(e,t,r,n,i,a,o,s,u,l,f){var h,c=.005,v=1/0,d,_,p,g;oe[0]=u,oe[1]=l;for(var y=0;y<1;y+=.05)Nt[0]=mt(e,r,i,o,y),Nt[1]=mt(t,n,a,s,y),p=Lr(oe,Nt),p<v&&(h=y,v=p);v=1/0;for(var m=0;m<32&&!(c<nh);m++)d=h-c,_=h+c,Nt[0]=mt(e,r,i,o,d),Nt[1]=mt(t,n,a,s,d),p=Lr(Nt,oe),d>=0&&p<v?(h=d,v=p):(Or[0]=mt(e,r,i,o,_),Or[1]=mt(t,n,a,s,_),g=Lr(Or,oe),_<=1&&g<v?(h=_,v=g):c*=.5);return Re(v)}function md(e,t,r,n,i,a,o,s,u){for(var l=e,f=t,h=0,c=1/u,v=1;v<=u;v++){var d=v*c,_=mt(e,r,i,o,d),p=mt(t,n,a,s,d),g=_-l,y=p-f;h+=Math.sqrt(g*g+y*y),l=_,f=p}return h}function bt(e,t,r,n){var i=1-n;return i*(i*e+2*n*t)+n*n*r}function Cu(e,t,r,n){return 2*((1-n)*(t-e)+n*(r-t))}function wd(e,t,r,n,i){var a=e-2*t+r,o=2*(t-e),s=e-n,u=0;if(Pe(a)){if(ih(o)){var l=-s/o;l>=0&&l<=1&&(i[u++]=l)}}else{var f=o*o-4*a*s;if(Pe(f)){var l=-o/(2*a);l>=0&&l<=1&&(i[u++]=l)}else if(f>0){var h=Re(f),l=(-o+h)/(2*a),c=(-o-h)/(2*a);l>=0&&l<=1&&(i[u++]=l),c>=0&&c<=1&&(i[u++]=c)}}return u}function sh(e,t,r){var n=e+r-2*t;return n===0?.5:(e-t)/n}function Fi(e,t,r,n,i){var a=(t-e)*n+e,o=(r-t)*n+t,s=(o-a)*n+a;i[0]=e,i[1]=a,i[2]=s,i[3]=s,i[4]=o,i[5]=r}function Sd(e,t,r,n,i,a,o,s,u){var l,f=.005,h=1/0;oe[0]=o,oe[1]=s;for(var c=0;c<1;c+=.05){Nt[0]=bt(e,r,i,c),Nt[1]=bt(t,n,a,c);var v=Lr(oe,Nt);v<h&&(l=c,h=v)}h=1/0;for(var d=0;d<32&&!(f<nh);d++){var _=l-f,p=l+f;Nt[0]=bt(e,r,i,_),Nt[1]=bt(t,n,a,_);var v=Lr(Nt,oe);if(_>=0&&v<h)l=_,h=v;else{Or[0]=bt(e,r,i,p),Or[1]=bt(t,n,a,p);var g=Lr(Or,oe);p<=1&&g<h?(l=p,h=g):f*=.5}}return Re(h)}function Td(e,t,r,n,i,a,o){for(var s=e,u=t,l=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=bt(e,r,i,c),d=bt(t,n,a,c),_=v-s,p=d-u;l+=Math.sqrt(_*_+p*p),s=v,u=d}return l}var bd=/cubic-bezier\(([0-9,\.e ]+)\)/;function uh(e){var t=e&&bd.exec(e);if(t){var r=t[1].split(","),n=+ce(r[0]),i=+ce(r[1]),a=+ce(r[2]),o=+ce(r[3]);if(isNaN(n+i+a+o))return;var s=[];return function(u){return u<=0?0:u>=1?1:ah(0,n,a,1,u,s)&&mt(0,i,o,1,s[0])}}}var Cd=function(){function e(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Vt,this.ondestroy=t.ondestroy||Vt,this.onrestart=t.onrestart||Vt,t.easing&&this.setEasing(t.easing)}return e.prototype.step=function(t,r){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=r;return}var n=this._life,i=t-this._startTime-this._pausedTime,a=i/n;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var u=i%n;this._startTime=t-u,this._pausedTime=0,this.onrestart()}else return!0;return!1},e.prototype.pause=function(){this._paused=!0},e.prototype.resume=function(){this._paused=!1},e.prototype.setEasing=function(t){this.easing=t,this.easingFunc=at(t)?t:dn[t]||uh(t)},e}(),lh=function(){function e(t){this.value=t}return e}(),Md=function(){function e(){this._len=0}return e.prototype.insert=function(t){var r=new lh(t);return this.insertEntry(r),r},e.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},e.prototype.remove=function(t){var r=t.prev,n=t.next;r?r.next=n:this.head=n,n?n.prev=r:this.tail=r,t.next=t.prev=null,this._len--},e.prototype.len=function(){return this._len},e.prototype.clear=function(){this.head=this.tail=null,this._len=0},e}(),Fn=function(){function e(t){this._list=new Md,this._maxSize=10,this._map={},this._maxSize=t}return e.prototype.put=function(t,r){var n=this._list,i=this._map,a=null;if(i[t]==null){var o=n.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var u=n.head;n.remove(u),delete i[u.key],a=u.value,this._lastRemovedEntry=u}s?s.value=r:s=new lh(r),s.key=t,n.insertEntry(s),i[t]=s}return a},e.prototype.get=function(t){var r=this._map[t],n=this._list;if(r!=null)return r!==n.tail&&(n.remove(r),n.insertEntry(r)),r.value},e.prototype.clear=function(){this._list.clear(),this._map={}},e.prototype.len=function(){return this._list.len()},e}(),Mu={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Ae(e){return e=Math.round(e),e<0?0:e>255?255:e}function Lo(e){return e<0?0:e>1?1:e}function Pa(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?Ae(parseFloat(t)/100*255):Ae(parseInt(t,10))}function pn(e){var t=e;return t.length&&t.charAt(t.length-1)==="%"?Lo(parseFloat(t)/100):Lo(parseFloat(t))}function Ra(e,t,r){return r<0?r+=1:r>1&&(r-=1),r*6<1?e+(t-e)*r*6:r*2<1?t:r*3<2?e+(t-e)*(2/3-r)*6:e}function qn(e,t,r){return e+(t-e)*r}function Ft(e,t,r,n,i){return e[0]=t,e[1]=r,e[2]=n,e[3]=i,e}function Io(e,t){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e}var fh=new Fn(20),$n=null;function cr(e,t){$n&&Io($n,t),$n=fh.put(e,$n||t.slice())}function xe(e,t){if(e){t=t||[];var r=fh.get(e);if(r)return Io(t,r);e=e+"";var n=e.replace(/ /g,"").toLowerCase();if(n in Mu)return Io(t,Mu[n]),cr(e,t),t;var i=n.length;if(n.charAt(0)==="#"){if(i===4||i===5){var a=parseInt(n.slice(1,4),16);if(!(a>=0&&a<=4095)){Ft(t,0,0,0,1);return}return Ft(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,i===5?parseInt(n.slice(4),16)/15:1),cr(e,t),t}else if(i===7||i===9){var a=parseInt(n.slice(1,7),16);if(!(a>=0&&a<=16777215)){Ft(t,0,0,0,1);return}return Ft(t,(a&16711680)>>16,(a&65280)>>8,a&255,i===9?parseInt(n.slice(7),16)/255:1),cr(e,t),t}return}var o=n.indexOf("("),s=n.indexOf(")");if(o!==-1&&s+1===i){var u=n.substr(0,o),l=n.substr(o+1,s-(o+1)).split(","),f=1;switch(u){case"rgba":if(l.length!==4)return l.length===3?Ft(t,+l[0],+l[1],+l[2],1):Ft(t,0,0,0,1);f=pn(l.pop());case"rgb":if(l.length>=3)return Ft(t,Pa(l[0]),Pa(l[1]),Pa(l[2]),l.length===3?f:pn(l[3])),cr(e,t),t;Ft(t,0,0,0,1);return;case"hsla":if(l.length!==4){Ft(t,0,0,0,1);return}return l[3]=pn(l[3]),Du(l,t),cr(e,t),t;case"hsl":if(l.length!==3){Ft(t,0,0,0,1);return}return Du(l,t),cr(e,t),t;default:return}}Ft(t,0,0,0,1)}}function Du(e,t){var r=(parseFloat(e[0])%360+360)%360/360,n=pn(e[1]),i=pn(e[2]),a=i<=.5?i*(n+1):i+n-i*n,o=i*2-a;return t=t||[],Ft(t,Ae(Ra(o,a,r+1/3)*255),Ae(Ra(o,a,r)*255),Ae(Ra(o,a,r-1/3)*255),1),e.length===4&&(t[3]=e[3]),t}function Pu(e,t){var r=xe(e);if(r){for(var n=0;n<3;n++)r[n]=r[n]*(1-t)|0,r[n]>255?r[n]=255:r[n]<0&&(r[n]=0);return ws(r,r.length===4?"rgba":"rgb")}}function D1(e,t,r){if(!(!(t&&t.length)||!(e>=0&&e<=1))){var n=e*(t.length-1),i=Math.floor(n),a=Math.ceil(n),o=xe(t[i]),s=xe(t[a]),u=n-i,l=ws([Ae(qn(o[0],s[0],u)),Ae(qn(o[1],s[1],u)),Ae(qn(o[2],s[2],u)),Lo(qn(o[3],s[3],u))],"rgba");return r?{color:l,leftIndex:i,rightIndex:a,value:n}:l}}function ws(e,t){if(!(!e||!e.length)){var r=e[0]+","+e[1]+","+e[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(r+=","+e[3]),t+"("+r+")"}}function Bi(e,t){var r=xe(e);return r?(.299*r[0]+.587*r[1]+.114*r[2])*r[3]/255+(1-r[3])*t:0}var Ru=new Fn(100);function Au(e){if(H(e)){var t=Ru.get(e);return t||(t=Pu(e,-.1),Ru.put(e,t)),t}else if(ys(e)){var r=L({},e);return r.colorStops=X(e.colorStops,function(n){return{offset:n.offset,color:Pu(n.color,-.1)}}),r}return e}function Dd(e){return e.type==="linear"}function Pd(e){return e.type==="radial"}(function(){return U.hasGlobalWindow&&at(window.btoa)?function(e){return window.btoa(unescape(encodeURIComponent(e)))}:typeof Buffer<"u"?function(e){return Buffer.from(e).toString("base64")}:function(e){return null}})();var Oo=Array.prototype.slice;function ve(e,t,r){return(t-e)*r+e}function Aa(e,t,r,n){for(var i=t.length,a=0;a<i;a++)e[a]=ve(t[a],r[a],n);return e}function Rd(e,t,r,n){for(var i=t.length,a=i&&t[0].length,o=0;o<i;o++){e[o]||(e[o]=[]);for(var s=0;s<a;s++)e[o][s]=ve(t[o][s],r[o][s],n)}return e}function Zn(e,t,r,n){for(var i=t.length,a=0;a<i;a++)e[a]=t[a]+r[a]*n;return e}function xu(e,t,r,n){for(var i=t.length,a=i&&t[0].length,o=0;o<i;o++){e[o]||(e[o]=[]);for(var s=0;s<a;s++)e[o][s]=t[o][s]+r[o][s]*n}return e}function Ad(e,t){for(var r=e.length,n=t.length,i=r>n?t:e,a=Math.min(r,n),o=i[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(r,n);s++)i.push({offset:o.offset,color:o.color.slice()})}function xd(e,t,r){var n=e,i=t;if(!(!n.push||!i.push)){var a=n.length,o=i.length;if(a!==o){var s=a>o;if(s)n.length=o;else for(var u=a;u<o;u++)n.push(r===1?i[u]:Oo.call(i[u]))}for(var l=n[0]&&n[0].length,u=0;u<n.length;u++)if(r===1)isNaN(n[u])&&(n[u]=i[u]);else for(var f=0;f<l;f++)isNaN(n[u][f])&&(n[u][f]=i[u][f])}}function bi(e){if(It(e)){var t=e.length;if(It(e[0])){for(var r=[],n=0;n<t;n++)r.push(Oo.call(e[n]));return r}return Oo.call(e)}return e}function Ci(e){return e[0]=Math.floor(e[0])||0,e[1]=Math.floor(e[1])||0,e[2]=Math.floor(e[2])||0,e[3]=e[3]==null?1:e[3],"rgba("+e.join(",")+")"}function Ed(e){return It(e&&e[0])?2:1}var Kn=0,Mi=1,hh=2,sn=3,ko=4,Fo=5,Eu=6;function Lu(e){return e===ko||e===Fo}function Qn(e){return e===Mi||e===hh}var $r=[0,0,0,0],Ld=function(){function e(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return e.prototype.isFinished=function(){return this._finished},e.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},e.prototype.needsAnimate=function(){return this.keyframes.length>=1},e.prototype.getAdditiveTrack=function(){return this._additiveTrack},e.prototype.addKeyframe=function(t,r,n){this._needsSort=!0;var i=this.keyframes,a=i.length,o=!1,s=Eu,u=r;if(It(r)){var l=Ed(r);s=l,(l===1&&!ut(r[0])||l===2&&!ut(r[0][0]))&&(o=!0)}else if(ut(r)&&!Ic(r))s=Kn;else if(H(r))if(!isNaN(+r))s=Kn;else{var f=xe(r);f&&(u=f,s=sn)}else if(ys(r)){var h=L({},u);h.colorStops=X(r.colorStops,function(v){return{offset:v.offset,color:xe(v.color)}}),Dd(r)?s=ko:Pd(r)&&(s=Fo),u=h}a===0?this.valType=s:(s!==this.valType||s===Eu)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:u,rawValue:r,percent:0};return n&&(c.easing=n,c.easingFunc=at(n)?n:dn[n]||uh(n)),i.push(c),c},e.prototype.prepare=function(t,r){var n=this.keyframes;this._needsSort&&n.sort(function(_,p){return _.time-p.time});for(var i=this.valType,a=n.length,o=n[a-1],s=this.discrete,u=Qn(i),l=Lu(i),f=0;f<a;f++){var h=n[f],c=h.value,v=o.value;h.percent=h.time/t,s||(u&&f!==a-1?xd(c,v,i):l&&Ad(c.colorStops,v.colorStops))}if(!s&&i!==Fo&&r&&this.needsAnimate()&&r.needsAnimate()&&i===r.valType&&!r._finished){this._additiveTrack=r;for(var d=n[0].value,f=0;f<a;f++)i===Kn?n[f].additiveValue=n[f].value-d:i===sn?n[f].additiveValue=Zn([],n[f].value,d,-1):Qn(i)&&(n[f].additiveValue=i===Mi?Zn([],n[f].value,d,-1):xu([],n[f].value,d,-1))}},e.prototype.step=function(t,r){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n=this._additiveTrack!=null,i=n?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,u=this.propName,l=a===sn,f,h=this._lastFr,c=Math.min,v,d;if(s===1)v=d=o[0];else{if(r<0)f=0;else if(r<this._lastFrP){var _=c(h+1,s-1);for(f=_;f>=0&&!(o[f].percent<=r);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>r);f++);f=c(f-1,s-2)}d=o[f+1],v=o[f]}if(v&&d){this._lastFr=f,this._lastFrP=r;var p=d.percent-v.percent,g=p===0?1:c((r-v.percent)/p,1);d.easingFunc&&(g=d.easingFunc(g));var y=n?this._additiveValue:l?$r:t[u];if((Qn(a)||l)&&!y&&(y=this._additiveValue=[]),this.discrete)t[u]=g<1?v.rawValue:d.rawValue;else if(Qn(a))a===Mi?Aa(y,v[i],d[i],g):Rd(y,v[i],d[i],g);else if(Lu(a)){var m=v[i],w=d[i],T=a===ko;t[u]={type:T?"linear":"radial",x:ve(m.x,w.x,g),y:ve(m.y,w.y,g),colorStops:X(m.colorStops,function(b,C){var M=w.colorStops[C];return{offset:ve(b.offset,M.offset,g),color:Ci(Aa([],b.color,M.color,g))}}),global:w.global},T?(t[u].x2=ve(m.x2,w.x2,g),t[u].y2=ve(m.y2,w.y2,g)):t[u].r=ve(m.r,w.r,g)}else if(l)Aa(y,v[i],d[i],g),n||(t[u]=Ci(y));else{var S=ve(v[i],d[i],g);n?this._additiveValue=S:t[u]=S}n&&this._addToTarget(t)}}},e.prototype._addToTarget=function(t){var r=this.valType,n=this.propName,i=this._additiveValue;r===Kn?t[n]=t[n]+i:r===sn?(xe(t[n],$r),Zn($r,$r,i,1),t[n]=Ci($r)):r===Mi?Zn(t[n],t[n],i,1):r===hh&&xu(t[n],t[n],i,1)},e}(),Ss=function(){function e(t,r,n,i){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=r,r&&i){Wf("Can' use additive animation on looped animation.");return}this._additiveAnimators=i,this._allowDiscrete=n}return e.prototype.getMaxTime=function(){return this._maxTime},e.prototype.getDelay=function(){return this._delay},e.prototype.getLoop=function(){return this._loop},e.prototype.getTarget=function(){return this._target},e.prototype.changeTarget=function(t){this._target=t},e.prototype.when=function(t,r,n){return this.whenWithKeys(t,r,it(r),n)},e.prototype.whenWithKeys=function(t,r,n,i){for(var a=this._tracks,o=0;o<n.length;o++){var s=n[o],u=a[s];if(!u){u=a[s]=new Ld(s);var l=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];l=c&&c.value,f.valType===sn&&l&&(l=Ci(l))}else l=this._target[s];if(l==null)continue;t>0&&u.addKeyframe(0,bi(l),i),this._trackKeys.push(s)}u.addKeyframe(t,bi(r[s]),i)}return this._maxTime=Math.max(this._maxTime,t),this},e.prototype.pause=function(){this._clip.pause(),this._paused=!0},e.prototype.resume=function(){this._clip.resume(),this._paused=!1},e.prototype.isPaused=function(){return!!this._paused},e.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},e.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var r=t.length,n=0;n<r;n++)t[n].call(this)},e.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,r=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,r)for(var n=0;n<r.length;n++)r[n].call(this)},e.prototype._setTracksFinished=function(){for(var t=this._tracks,r=this._trackKeys,n=0;n<r.length;n++)t[r[n]].setFinished()},e.prototype._getAdditiveTrack=function(t){var r,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var a=n[i].getTrack(t);a&&(r=a)}return r},e.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var r=this,n=[],i=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,f=l.length;if(s.prepare(i,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[f-1];h&&(r._target[s.propName]=h.rawValue),s.setFinished()}else n.push(s)}if(n.length||this._force){var c=new Cd({life:i,loop:this._loop,delay:this._delay||0,onframe:function(v){r._started=2;var d=r._additiveAnimators;if(d){for(var _=!1,p=0;p<d.length;p++)if(d[p]._clip){_=!0;break}_||(r._additiveAnimators=null)}for(var p=0;p<n.length;p++)n[p].step(r._target,v);var g=r._onframeCbs;if(g)for(var p=0;p<g.length;p++)g[p](r._target,v)},ondestroy:function(){r._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},e.prototype.stop=function(t){if(this._clip){var r=this._clip;t&&r.onframe(1),this._abortedCallback()}},e.prototype.delay=function(t){return this._delay=t,this},e.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},e.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},e.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},e.prototype.getClip=function(){return this._clip},e.prototype.getTrack=function(t){return this._tracks[t]},e.prototype.getTracks=function(){var t=this;return X(this._trackKeys,function(r){return t._tracks[r]})},e.prototype.stopTracks=function(t,r){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,a=0;a<t.length;a++){var o=n[t[a]];o&&!o.isFinished()&&(r?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<i.length;a++)if(!n[i[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},e.prototype.saveTo=function(t,r,n){if(t){r=r||this._trackKeys;for(var i=0;i<r.length;i++){var a=r[i],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,u=s[n?0:s.length-1];u&&(t[a]=bi(u.rawValue))}}}},e.prototype.__changeFinalValue=function(t,r){r=r||it(t);for(var n=0;n<r.length;n++){var i=r[n],a=this._tracks[i];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[i]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},e}();function Ar(){return new Date().getTime()}var Id=function(e){N(t,e);function t(r){var n=e.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,r=r||{},n.stage=r.stage||{},n}return t.prototype.addClip=function(r){r.animation&&this.removeClip(r),this._head?(this._tail.next=r,r.prev=this._tail,r.next=null,this._tail=r):this._head=this._tail=r,r.animation=this},t.prototype.addAnimator=function(r){r.animation=this;var n=r.getClip();n&&this.addClip(n)},t.prototype.removeClip=function(r){if(r.animation){var n=r.prev,i=r.next;n?n.next=i:this._head=i,i?i.prev=n:this._tail=n,r.next=r.prev=r.animation=null}},t.prototype.removeAnimator=function(r){var n=r.getClip();n&&this.removeClip(n),r.animation=null},t.prototype.update=function(r){for(var n=Ar()-this._pausedTime,i=n-this._time,a=this._head;a;){var o=a.next,s=a.step(n,i);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=n,r||(this.trigger("frame",i),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var r=this;this._running=!0;function n(){r._running&&(Eo(n),!r._paused&&r.update())}Eo(n)},t.prototype.start=function(){this._running||(this._time=Ar(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Ar(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Ar()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var r=this._head;r;){var n=r.next;r.prev=r.next=r.animation=null,r=n}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(r,n){n=n||{},this.start();var i=new Ss(r,n.loop);return this.addAnimator(i),i},t}(_e),Od=300,xa=U.domSupported,Ea=function(){var e=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],r={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},n=X(e,function(i){var a=i.replace("mouse","pointer");return r.hasOwnProperty(a)?a:i});return{mouse:e,touch:t,pointer:n}}(),Iu={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Ou=!1;function Bo(e){var t=e.pointerType;return t==="pen"||t==="touch"}function kd(e){e.touching=!0,e.touchTimer!=null&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)}function La(e){e&&(e.zrByTouch=!0)}function Fd(e,t){return qt(e.dom,new Bd(e,t),!0)}function vh(e,t){for(var r=t,n=!1;r&&r.nodeType!==9&&!(n=r.domBelongToZr||r!==t&&r===e.painterRoot);)r=r.parentNode;return n}var Bd=function(){function e(t,r){this.stopPropagation=Vt,this.stopImmediatePropagation=Vt,this.preventDefault=Vt,this.type=r.type,this.target=this.currentTarget=t.dom,this.pointerType=r.pointerType,this.clientX=r.clientX,this.clientY=r.clientY}return e}(),$t={mousedown:function(e){e=qt(this.dom,e),this.__mayPointerCapture=[e.zrX,e.zrY],this.trigger("mousedown",e)},mousemove:function(e){e=qt(this.dom,e);var t=this.__mayPointerCapture;t&&(e.zrX!==t[0]||e.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",e)},mouseup:function(e){e=qt(this.dom,e),this.__togglePointerCapture(!1),this.trigger("mouseup",e)},mouseout:function(e){e=qt(this.dom,e);var t=e.toElement||e.relatedTarget;vh(this,t)||(this.__pointerCapturing&&(e.zrEventControl="no_globalout"),this.trigger("mouseout",e))},wheel:function(e){Ou=!0,e=qt(this.dom,e),this.trigger("mousewheel",e)},mousewheel:function(e){Ou||(e=qt(this.dom,e),this.trigger("mousewheel",e))},touchstart:function(e){e=qt(this.dom,e),La(e),this.__lastTouchMoment=new Date,this.handler.processGesture(e,"start"),$t.mousemove.call(this,e),$t.mousedown.call(this,e)},touchmove:function(e){e=qt(this.dom,e),La(e),this.handler.processGesture(e,"change"),$t.mousemove.call(this,e)},touchend:function(e){e=qt(this.dom,e),La(e),this.handler.processGesture(e,"end"),$t.mouseup.call(this,e),+new Date-+this.__lastTouchMoment<Od&&$t.click.call(this,e)},pointerdown:function(e){$t.mousedown.call(this,e)},pointermove:function(e){Bo(e)||$t.mousemove.call(this,e)},pointerup:function(e){$t.mouseup.call(this,e)},pointerout:function(e){Bo(e)||$t.mouseout.call(this,e)}};P(["click","dblclick","contextmenu"],function(e){$t[e]=function(t){t=qt(this.dom,t),this.trigger(e,t)}});var zo={pointermove:function(e){Bo(e)||zo.mousemove.call(this,e)},pointerup:function(e){zo.mouseup.call(this,e)},mousemove:function(e){this.trigger("mousemove",e)},mouseup:function(e){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",e),t&&(e.zrEventControl="only_globalout",this.trigger("mouseout",e))}};function zd(e,t){var r=t.domHandlers;U.pointerEventsSupported?P(Ea.pointer,function(n){Di(t,n,function(i){r[n].call(e,i)})}):(U.touchEventsSupported&&P(Ea.touch,function(n){Di(t,n,function(i){r[n].call(e,i),kd(t)})}),P(Ea.mouse,function(n){Di(t,n,function(i){i=ms(i),t.touching||r[n].call(e,i)})}))}function Nd(e,t){U.pointerEventsSupported?P(Iu.pointer,r):U.touchEventsSupported||P(Iu.mouse,r);function r(n){function i(a){a=ms(a),vh(e,a.target)||(a=Fd(e,a),t.domHandlers[n].call(e,a))}Di(t,n,i,{capture:!0})}}function Di(e,t,r,n){e.mounted[t]=r,e.listenerOpts[t]=n,ed(e.domTarget,t,r,n)}function Ia(e){var t=e.mounted;for(var r in t)t.hasOwnProperty(r)&&rd(e.domTarget,r,t[r],e.listenerOpts[r]);e.mounted={}}var ku=function(){function e(t,r){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=r}return e}(),Hd=function(e){N(t,e);function t(r,n){var i=e.call(this)||this;return i.__pointerCapturing=!1,i.dom=r,i.painterRoot=n,i._localHandlerScope=new ku(r,$t),xa&&(i._globalHandlerScope=new ku(document,zo)),zd(i,i._localHandlerScope),i}return t.prototype.dispose=function(){Ia(this._localHandlerScope),xa&&Ia(this._globalHandlerScope)},t.prototype.setCursor=function(r){this.dom.style&&(this.dom.style.cursor=r||"default")},t.prototype.__togglePointerCapture=function(r){if(this.__mayPointerCapture=null,xa&&+this.__pointerCapturing^+r){this.__pointerCapturing=r;var n=this._globalHandlerScope;r?Nd(this,n):Ia(n)}},t}(_e),ch=1;U.hasGlobalWindow&&(ch=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Fu=ch,No=.4,Ho="#333",Uo="#ccc",Ud="#eee",Bu=Qf,zu=5e-5;function He(e){return e>zu||e<-zu}var Ue=[],dr=[],Oa=cn(),ka=Math.abs,Ts=function(){function e(){}return e.prototype.getLocalTransform=function(t){return e.getLocalTransform(this,t)},e.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},e.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},e.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},e.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},e.prototype.needLocalTransform=function(){return He(this.rotation)||He(this.x)||He(this.y)||He(this.scaleX-1)||He(this.scaleY-1)||He(this.skewX)||He(this.skewY)},e.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,r=this.needLocalTransform(),n=this.transform;if(!(r||t)){n&&(Bu(n),this.invTransform=null);return}n=n||cn(),r?this.getLocalTransform(n):Bu(n),t&&(r?Si(n,t,n):od(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)},e.prototype._resolveGlobalScaleRatio=function(t){var r=this.globalScaleRatio;if(r!=null&&r!==1){this.getGlobalScale(Ue);var n=Ue[0]<0?-1:1,i=Ue[1]<0?-1:1,a=((Ue[0]-n)*r+n)/Ue[0]||0,o=((Ue[1]-i)*r+i)/Ue[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||cn(),Jf(this.invTransform,t)},e.prototype.getComputedTransform=function(){for(var t=this,r=[];t;)r.push(t),t=t.parent;for(;t=r.pop();)t.updateTransform();return this.transform},e.prototype.setLocalTransform=function(t){if(t){var r=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),a=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(a),r=Math.sqrt(r),this.skewX=a,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=n,this.originX=0,this.originY=0}},e.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,r=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||cn(),Si(dr,t.invTransform,r),r=dr);var n=this.originX,i=this.originY;(n||i)&&(Oa[4]=n,Oa[5]=i,Si(dr,r,Oa),dr[4]-=n,dr[5]-=i,r=dr),this.setLocalTransform(r)}},e.prototype.getGlobalScale=function(t){var r=this.transform;return t=t||[],r?(t[0]=Math.sqrt(r[0]*r[0]+r[1]*r[1]),t[1]=Math.sqrt(r[2]*r[2]+r[3]*r[3]),r[0]<0&&(t[0]=-t[0]),r[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},e.prototype.transformCoordToLocal=function(t,r){var n=[t,r],i=this.invTransform;return i&&Ir(n,n,i),n},e.prototype.transformCoordToGlobal=function(t,r){var n=[t,r],i=this.transform;return i&&Ir(n,n,i),n},e.prototype.getLineScale=function(){var t=this.transform;return t&&ka(t[0]-1)>1e-10&&ka(t[3]-1)>1e-10?Math.sqrt(ka(t[0]*t[3]-t[2]*t[1])):1},e.prototype.copyTransform=function(t){Vd(this,t)},e.getLocalTransform=function(t,r){r=r||[];var n=t.originX||0,i=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(n||i||s||u){var d=n+s,_=i+u;r[4]=-d*a-c*_*o,r[5]=-_*o-v*d*a}else r[4]=r[5]=0;return r[0]=a,r[3]=o,r[1]=v*a,r[2]=c*o,l&&sd(r,r,l),r[4]+=n+f,r[5]+=i+h,r},e.initDefaultProps=function(){var t=e.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),e}(),Dn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Vd(e,t){for(var r=0;r<Dn.length;r++){var n=Dn[r];e[n]=t[n]}}var Nu={};function Et(e,t){t=t||sr;var r=Nu[t];r||(r=Nu[t]=new Fn(500));var n=r.get(e);return n==null&&(n=On.measureText(e,t).width,r.put(e,n)),n}function Hu(e,t,r,n){var i=Et(e,t),a=bs(t),o=un(0,i,r),s=Dr(0,a,n),u=new tt(o,s,i,a);return u}function Gd(e,t,r,n){var i=((e||"")+"").split(`
`),a=i.length;if(a===1)return Hu(i[0],t,r,n);for(var o=new tt(0,0,0,0),s=0;s<i.length;s++){var u=Hu(i[s],t,r,n);s===0?o.copy(u):o.union(u)}return o}function un(e,t,r){return r==="right"?e-=t:r==="center"&&(e-=t/2),e}function Dr(e,t,r){return r==="middle"?e-=t/2:r==="bottom"&&(e-=t),e}function bs(e){return Et("国",e)}function Pn(e,t){return typeof e=="string"?e.lastIndexOf("%")>=0?parseFloat(e)/100*t:parseFloat(e):e}function dh(e,t,r){var n=t.position||"inside",i=t.distance!=null?t.distance:5,a=r.height,o=r.width,s=a/2,u=r.x,l=r.y,f="left",h="top";if(n instanceof Array)u+=Pn(n[0],r.width),l+=Pn(n[1],r.height),f=null,h=null;else switch(n){case"left":u-=i,l+=s,f="right",h="middle";break;case"right":u+=i+o,l+=s,h="middle";break;case"top":u+=o/2,l-=i,f="center",h="bottom";break;case"bottom":u+=o/2,l+=a+i,f="center";break;case"inside":u+=o/2,l+=s,f="center",h="middle";break;case"insideLeft":u+=i,l+=s,h="middle";break;case"insideRight":u+=o-i,l+=s,f="right",h="middle";break;case"insideTop":u+=o/2,l+=i,f="center";break;case"insideBottom":u+=o/2,l+=a-i,f="center",h="bottom";break;case"insideTopLeft":u+=i,l+=i;break;case"insideTopRight":u+=o-i,l+=i,f="right";break;case"insideBottomLeft":u+=i,l+=a-i,h="bottom";break;case"insideBottomRight":u+=o-i,l+=a-i,f="right",h="bottom";break}return e=e||{},e.x=u,e.y=l,e.align=f,e.verticalAlign=h,e}var Fa="__zr_normal__",Ba=Dn.concat(["ignore"]),Yd=Ee(Dn,function(e,t){return e[t]=!0,e},{ignore:!1}),pr={},Wd=new tt(0,0,0,0),ra=function(){function e(t){this.id=Yf(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return e.prototype._init=function(t){this.attr(t)},e.prototype.drift=function(t,r,n){switch(this.draggable){case"horizontal":r=0;break;case"vertical":t=0;break}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=r,this.decomposeTransform(),this.markRedraw()},e.prototype.beforeUpdate=function(){},e.prototype.afterUpdate=function(){},e.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},e.prototype.updateInnerText=function(t){var r=this._textContent;if(r&&(!r.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,a=r.innerTransformable,o=void 0,s=void 0,u=!1;a.parent=i?this:null;var l=!1;if(a.copyTransform(r),n.position!=null){var f=Wd;n.layoutRect?f.copy(n.layoutRect):f.copy(this.getBoundingRect()),i||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(pr,n,f):dh(pr,n,f),a.x=pr.x,a.y=pr.y,o=pr.align,s=pr.verticalAlign;var h=n.origin;if(h&&n.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=Pn(h[0],f.width),v=Pn(h[1],f.height)),l=!0,a.originX=-a.x+c+(i?0:f.x),a.originY=-a.y+v+(i?0:f.y)}}n.rotation!=null&&(a.rotation=n.rotation);var d=n.offset;d&&(a.x+=d[0],a.y+=d[1],l||(a.originX=-d[0],a.originY=-d[1]));var _=n.inside==null?typeof n.position=="string"&&n.position.indexOf("inside")>=0:n.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,y=void 0,m=void 0;_&&this.canBeInsideText()?(g=n.insideFill,y=n.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(y==null||y==="auto")&&(y=this.getInsideTextStroke(g),m=!0)):(g=n.outsideFill,y=n.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(y==null||y==="auto")&&(y=this.getOutsideStroke(g),m=!0)),g=g||"#000",(g!==p.fill||y!==p.stroke||m!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(u=!0,p.fill=g,p.stroke=y,p.autoStroke=m,p.align=o,p.verticalAlign=s,r.setDefaultTextStyle(p)),r.__dirty|=ae,u&&r.dirtyStyle(!0)}},e.prototype.canBeInsideText=function(){return!0},e.prototype.getInsideTextFill=function(){return"#fff"},e.prototype.getInsideTextStroke=function(t){return"#000"},e.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Uo:Ho},e.prototype.getOutsideStroke=function(t){var r=this.__zr&&this.__zr.getBackgroundColor(),n=typeof r=="string"&&xe(r);n||(n=[255,255,255,1]);for(var i=n[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(a?0:255)*(1-i);return n[3]=1,ws(n,"rgba")},e.prototype.traverse=function(t,r){},e.prototype.attrKV=function(t,r){t==="textConfig"?this.setTextConfig(r):t==="textContent"?this.setTextContent(r):t==="clipPath"?this.setClipPath(r):t==="extra"?(this.extra=this.extra||{},L(this.extra,r)):this[t]=r},e.prototype.hide=function(){this.ignore=!0,this.markRedraw()},e.prototype.show=function(){this.ignore=!1,this.markRedraw()},e.prototype.attr=function(t,r){if(typeof t=="string")this.attrKV(t,r);else if(B(t))for(var n=t,i=it(n),a=0;a<i.length;a++){var o=i[a];this.attrKV(o,t[o])}return this.markRedraw(),this},e.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var r=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],a=i.__fromStateTransition;if(!(i.getLoop()||a&&a!==Fa)){var o=i.targetName,s=o?r[o]:r;i.saveTo(s)}}},e.prototype._innerSaveToNormal=function(t){var r=this._normalState;r||(r=this._normalState={}),t.textConfig&&!r.textConfig&&(r.textConfig=this.textConfig),this._savePrimaryToNormal(t,r,Ba)},e.prototype._savePrimaryToNormal=function(t,r,n){for(var i=0;i<n.length;i++){var a=n[i];t[a]!=null&&!(a in r)&&(r[a]=this[a])}},e.prototype.hasState=function(){return this.currentStates.length>0},e.prototype.getState=function(t){return this.states[t]},e.prototype.ensureState=function(t){var r=this.states;return r[t]||(r[t]={}),r[t]},e.prototype.clearStates=function(t){this.useState(Fa,!1,t)},e.prototype.useState=function(t,r,n,i){var a=t===Fa,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,u=this.stateTransition;if(!(nt(s,t)>=0&&(r||s.length===1))){var l;if(this.stateProxy&&!a&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),!l&&!a){Wf("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(l);var f=!!(l&&l.hoverLayer||i);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,r,!n&&!this.__inHover&&u&&u.duration>0,u);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,r,n,f),c&&c.useState(t,r,n,f),a?(this.currentStates=[],this._normalState={}):r?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ae),l}}},e.prototype.useStates=function(t,r,n){if(!t.length)this.clearStates();else{var i=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var u=0;u<o;u++)if(t[u]!==a[u]){s=!1;break}}if(s)return;for(var u=0;u<o;u++){var l=t[u],f=void 0;this.stateProxy&&(f=this.stateProxy(l,t)),f||(f=this.states[l]),f&&i.push(f)}var h=i[o-1],c=!!(h&&h.hoverLayer||n);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(i),d=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!r&&!this.__inHover&&d&&d.duration>0,d);var _=this._textContent,p=this._textGuide;_&&_.useStates(t,r,c),p&&p.useStates(t,r,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ae)}},e.prototype.isSilent=function(){for(var t=this.silent,r=this.parent;!t&&r;){if(r.silent){t=!0;break}r=r.parent}return t},e.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var r=this.animators[t];r.targetName&&r.changeTarget(this[r.targetName])}},e.prototype.removeState=function(t){var r=nt(this.currentStates,t);if(r>=0){var n=this.currentStates.slice();n.splice(r,1),this.useStates(n)}},e.prototype.replaceState=function(t,r,n){var i=this.currentStates.slice(),a=nt(i,t),o=nt(i,r)>=0;a>=0?o?i.splice(a,1):i[a]=r:n&&!o&&i.push(r),this.useStates(i)},e.prototype.toggleState=function(t,r){r?this.useState(t,!0):this.removeState(t)},e.prototype._mergeStates=function(t){for(var r={},n,i=0;i<t.length;i++){var a=t[i];L(r,a),a.textConfig&&(n=n||{},L(n,a.textConfig))}return n&&(r.textConfig=n),r},e.prototype._applyStateObj=function(t,r,n,i,a,o){var s=!(r&&i);r&&r.textConfig?(this.textConfig=L({},i?this.textConfig:n.textConfig),L(this.textConfig,r.textConfig)):s&&n.textConfig&&(this.textConfig=n.textConfig);for(var u={},l=!1,f=0;f<Ba.length;f++){var h=Ba[f],c=a&&Yd[h];r&&r[h]!=null?c?(l=!0,u[h]=r[h]):this[h]=r[h]:s&&n[h]!=null&&(c?(l=!0,u[h]=n[h]):this[h]=n[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],d=v.targetName;v.getLoop()||v.__changeFinalValue(d?(r||n)[d]:r||n)}l&&this._transitionState(t,u,o)},e.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var r=this.__zr;r&&t.addSelfToZr(r),t.__zr=r,t.__hostTarget=this}},e.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},e.prototype.getClipPath=function(){return this._clipPath},e.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},e.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},e.prototype.getTextContent=function(){return this._textContent},e.prototype.setTextContent=function(t){var r=this._textContent;r!==t&&(r&&r!==t&&this.removeTextContent(),t.innerTransformable=new Ts,this._attachComponent(t),this._textContent=t,this.markRedraw())},e.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),L(this.textConfig,t),this.markRedraw()},e.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},e.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},e.prototype.getTextGuideLine=function(){return this._textGuide},e.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},e.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},e.prototype.markRedraw=function(){this.__dirty|=ae;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},e.prototype.dirty=function(){this.markRedraw()},e.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var r=this._textContent,n=this._textGuide;r&&(r.__inHover=t),n&&(n.__inHover=t)},e.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var r=this.animators;if(r)for(var n=0;n<r.length;n++)t.animation.addAnimator(r[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},e.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var r=this.animators;if(r)for(var n=0;n<r.length;n++)t.animation.removeAnimator(r[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},e.prototype.animate=function(t,r,n){var i=t?this[t]:this,a=new Ss(i,r,n);return t&&(a.targetName=t),this.addAnimator(a,t),a},e.prototype.addAnimator=function(t,r){var n=this.__zr,i=this;t.during(function(){i.updateDuringAnimation(r)}).done(function(){var a=i.animators,o=nt(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},e.prototype.updateDuringAnimation=function(t){this.markRedraw()},e.prototype.stopAnimation=function(t,r){for(var n=this.animators,i=n.length,a=[],o=0;o<i;o++){var s=n[o];!t||t===s.scope?s.stop(r):a.push(s)}return this.animators=a,this},e.prototype.animateTo=function(t,r,n){za(this,t,r,n)},e.prototype.animateFrom=function(t,r,n){za(this,t,r,n,!0)},e.prototype._transitionState=function(t,r,n,i){for(var a=za(this,r,n,i),o=0;o<a.length;o++)a[o].__fromStateTransition=t},e.prototype.getBoundingRect=function(){return null},e.prototype.getPaintRect=function(){return null},e.initDefaultProps=function(){var t=e.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=ae;function r(n,i,a,o){Object.defineProperty(t,n,{get:function(){if(!this[i]){var u=this[i]=[];s(this,u)}return this[i]},set:function(u){this[a]=u[0],this[o]=u[1],this[i]=u,s(this,u)}});function s(u,l){Object.defineProperty(l,0,{get:function(){return u[a]},set:function(f){u[a]=f}}),Object.defineProperty(l,1,{get:function(){return u[o]},set:function(f){u[o]=f}})}}Object.defineProperty&&(r("position","_legacyPos","x","y"),r("scale","_legacyScale","scaleX","scaleY"),r("origin","_legacyOrigin","originX","originY"))}(),e}();ge(ra,_e);ge(ra,Ts);function za(e,t,r,n,i){r=r||{};var a=[];ph(e,"",e,t,r,n,a,i);var o=a.length,s=!1,u=r.done,l=r.aborted,f=function(){s=!0,o--,o<=0&&(s?u&&u():l&&l())},h=function(){o--,o<=0&&(s?u&&u():l&&l())};o||u&&u(),a.length>0&&r.during&&a[0].during(function(d,_){r.during(_)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),r.force&&v.duration(r.duration),v.start(r.easing)}return a}function Na(e,t,r){for(var n=0;n<r;n++)e[n]=t[n]}function Xd(e){return It(e[0])}function qd(e,t,r){if(It(t[r]))if(It(e[r])||(e[r]=[]),Dt(t[r])){var n=t[r].length;e[r].length!==n&&(e[r]=new t[r].constructor(n),Na(e[r],t[r],n))}else{var i=t[r],a=e[r],o=i.length;if(Xd(i))for(var s=i[0].length,u=0;u<o;u++)a[u]?Na(a[u],i[u],s):a[u]=Array.prototype.slice.call(i[u]);else Na(a,i,o);a.length=i.length}else e[r]=t[r]}function $d(e,t){return e===t||It(e)&&It(t)&&Zd(e,t)}function Zd(e,t){var r=e.length;if(r!==t.length)return!1;for(var n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0}function ph(e,t,r,n,i,a,o,s){for(var u=it(n),l=i.duration,f=i.delay,h=i.additive,c=i.setToFinal,v=!B(a),d=e.animators,_=[],p=0;p<u.length;p++){var g=u[p],y=n[g];if(y!=null&&r[g]!=null&&(v||a[g]))if(B(y)&&!It(y)&&!ys(y)){if(t){s||(r[g]=y,e.updateDuringAnimation(t));continue}ph(e,g,r[g],y,i,a&&a[g],o,s)}else _.push(g);else s||(r[g]=y,e.updateDuringAnimation(t),_.push(g))}var m=_.length;if(!h&&m)for(var w=0;w<d.length;w++){var T=d[w];if(T.targetName===t){var S=T.stopTracks(_);if(S){var b=nt(d,T);d.splice(b,1)}}}if(i.force||(_=Kt(_,function(D){return!$d(n[D],r[D])}),m=_.length),m>0||i.force&&!o.length){var C=void 0,M=void 0,A=void 0;if(s){M={},c&&(C={});for(var w=0;w<m;w++){var g=_[w];M[g]=r[g],c?C[g]=n[g]:r[g]=n[g]}}else if(c){A={};for(var w=0;w<m;w++){var g=_[w];A[g]=bi(r[g]),qd(r,n,g)}}var T=new Ss(r,!1,!1,h?Kt(d,function(R){return R.targetName===t}):null);T.targetName=t,i.scope&&(T.scope=i.scope),c&&C&&T.whenWithKeys(0,C,_),A&&T.whenWithKeys(0,A,_),T.whenWithKeys(l??500,s?M:n,_).delay(f||0),e.addAnimator(T,t),o.push(T)}}var Hr=function(e){N(t,e);function t(r){var n=e.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(r),n}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(r){return this._children[r]},t.prototype.childOfName=function(r){for(var n=this._children,i=0;i<n.length;i++)if(n[i].name===r)return n[i]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(r){return r&&r!==this&&r.parent!==this&&(this._children.push(r),this._doAdd(r)),this},t.prototype.addBefore=function(r,n){if(r&&r!==this&&r.parent!==this&&n&&n.parent===this){var i=this._children,a=i.indexOf(n);a>=0&&(i.splice(a,0,r),this._doAdd(r))}return this},t.prototype.replace=function(r,n){var i=nt(this._children,r);return i>=0&&this.replaceAt(n,i),this},t.prototype.replaceAt=function(r,n){var i=this._children,a=i[n];if(r&&r!==this&&r.parent!==this&&r!==a){i[n]=r,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(r)}return this},t.prototype._doAdd=function(r){r.parent&&r.parent.remove(r),r.parent=this;var n=this.__zr;n&&n!==r.__zr&&r.addSelfToZr(n),n&&n.refresh()},t.prototype.remove=function(r){var n=this.__zr,i=this._children,a=nt(i,r);return a<0?this:(i.splice(a,1),r.parent=null,n&&r.removeSelfFromZr(n),n&&n.refresh(),this)},t.prototype.removeAll=function(){for(var r=this._children,n=this.__zr,i=0;i<r.length;i++){var a=r[i];n&&a.removeSelfFromZr(n),a.parent=null}return r.length=0,this},t.prototype.eachChild=function(r,n){for(var i=this._children,a=0;a<i.length;a++){var o=i[a];r.call(n,o,a)}return this},t.prototype.traverse=function(r,n){for(var i=0;i<this._children.length;i++){var a=this._children[i],o=r.call(n,a);a.isGroup&&!o&&a.traverse(r,n)}return this},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var n=0;n<this._children.length;n++){var i=this._children[n];i.addSelfToZr(r)}},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var n=0;n<this._children.length;n++){var i=this._children[n];i.removeSelfFromZr(r)}},t.prototype.getBoundingRect=function(r){for(var n=new tt(0,0,0,0),i=r||this._children,a=[],o=null,s=0;s<i.length;s++){var u=i[s];if(!(u.ignore||u.invisible)){var l=u.getBoundingRect(),f=u.getLocalTransform(a);f?(tt.applyTransform(n,l,f),o=o||n.clone(),o.union(n)):(o=o||l.clone(),o.union(l))}}return o||n},t}(ra);Hr.prototype.type="group";/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Pi={},gh={};function Kd(e){delete gh[e]}function Qd(e){if(!e)return!1;if(typeof e=="string")return Bi(e,1)<No;if(e.colorStops){for(var t=e.colorStops,r=0,n=t.length,i=0;i<n;i++)r+=Bi(t[i].color,1);return r/=n,r<No}return!1}var Jd=function(){function e(t,r,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=r,this.id=t;var a=new _d,o=n.renderer||"canvas";Pi[o]||(o=it(Pi)[0]),n.useDirtyRect=n.useDirtyRect==null?!1:n.useDirtyRect;var s=new Pi[o](r,a,n,t),u=n.ssr||s.ssrOnly;this.storage=a,this.painter=s;var l=!U.node&&!U.worker&&!u?new Hd(s.getViewportRoot(),s.root):null,f=n.useCoarsePointer,h=f==null||f==="auto"?U.touchEventsSupported:!!f,c=44,v;h&&(v=Y(n.pointerSize,c)),this.handler=new th(a,s,l,s.root,v),this.animation=new Id({stage:{update:u?null:function(){return i._flush(!0)}}}),u||this.animation.start()}return e.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},e.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},e.prototype.configLayer=function(t,r){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,r),this.refresh())},e.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=Qd(t))},e.prototype.getBackgroundColor=function(){return this._backgroundColor},e.prototype.setDarkMode=function(t){this._darkMode=t},e.prototype.isDarkMode=function(){return this._darkMode},e.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},e.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},e.prototype.flush=function(){this._disposed||this._flush(!1)},e.prototype._flush=function(t){var r,n=Ar();this._needsRefresh&&(r=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(r=!0,this.refreshHoverImmediately());var i=Ar();r?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},e.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},e.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},e.prototype.refreshHover=function(){this._needsRefreshHover=!0},e.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},e.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},e.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},e.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},e.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},e.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},e.prototype.findHover=function(t,r){if(!this._disposed)return this.handler.findHover(t,r)},e.prototype.on=function(t,r,n){return this._disposed||this.handler.on(t,r,n),this},e.prototype.off=function(t,r){this._disposed||this.handler.off(t,r)},e.prototype.trigger=function(t,r){this._disposed||this.handler.trigger(t,r)},e.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),r=0;r<t.length;r++)t[r]instanceof Hr&&t[r].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},e.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,Kd(this.id))},e}();function Uu(e,t){var r=new Jd(Yf(),e,t);return gh[r.id]=r,r}function P1(e,t){Pi[e]=t}var Vu=1e-4,_h=20;function jd(e){return e.replace(/^\s+|\s+$/g,"")}function R1(e,t,r,n){var i=t[0],a=t[1],o=r[0],s=r[1],u=a-i,l=s-o;if(u===0)return l===0?o:(o+s)/2;if(n)if(u>0){if(e<=i)return o;if(e>=a)return s}else{if(e>=i)return o;if(e<=a)return s}else{if(e===i)return o;if(e===a)return s}return(e-i)/u*l+o}function De(e,t){switch(e){case"center":case"middle":e="50%";break;case"left":case"top":e="0%";break;case"right":case"bottom":e="100%";break}return H(e)?jd(e).match(/%$/)?parseFloat(e)/100*t:parseFloat(e):e==null?NaN:+e}function Vo(e,t,r){return t==null&&(t=10),t=Math.min(Math.max(0,t),_h),e=(+e).toFixed(t),r?e:+e}function xr(e){if(e=+e,isNaN(e))return 0;if(e>1e-14){for(var t=1,r=0;r<15;r++,t*=10)if(Math.round(e*t)/t===e)return r}return tp(e)}function tp(e){var t=e.toString().toLowerCase(),r=t.indexOf("e"),n=r>0?+t.slice(r+1):0,i=r>0?r:t.length,a=t.indexOf("."),o=a<0?0:i-1-a;return Math.max(0,o-n)}function A1(e,t){var r=Math.log,n=Math.LN10,i=Math.floor(r(e[1]-e[0])/n),a=Math.round(r(Math.abs(t[1]-t[0]))/n),o=Math.min(Math.max(-i+a,0),20);return isFinite(o)?o:20}function x1(e,t){var r=Ee(e,function(v,d){return v+(isNaN(d)?0:d)},0);if(r===0)return[];for(var n=Math.pow(10,t),i=X(e,function(v){return(isNaN(v)?0:v)/r*n*100}),a=n*100,o=X(i,function(v){return Math.floor(v)}),s=Ee(o,function(v,d){return v+d},0),u=X(i,function(v,d){return v-o[d]});s<a;){for(var l=Number.NEGATIVE_INFINITY,f=null,h=0,c=u.length;h<c;++h)u[h]>l&&(l=u[h],f=h);++o[f],u[f]=0,++s}return X(o,function(v){return v/n})}function ep(e,t){var r=Math.max(xr(e),xr(t)),n=e+t;return r>_h?n:Vo(n,r)}function E1(e){var t=Math.PI*2;return(e%t+t)%t}function L1(e){return e>-Vu&&e<Vu}var rp=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Ur(e){if(e instanceof Date)return e;if(H(e)){var t=rp.exec(e);if(!t)return new Date(NaN);if(t[8]){var r=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(r-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,r,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(e==null)return new Date(NaN);return new Date(Math.round(e))}function I1(e){return Math.pow(10,yh(e))}function yh(e){if(e===0)return 0;var t=Math.floor(Math.log(e)/Math.LN10);return e/Math.pow(10,t)>=10&&t++,t}function O1(e,t){var r=yh(e),n=Math.pow(10,r),i=e/n,a;return i<1.5?a=1:i<2.5?a=2:i<4?a=3:i<7?a=5:a=10,e=a*n,r>=-20?+e.toFixed(r<0?-r:0):e}function zi(e){var t=parseFloat(e);return t==e&&(t!==0||!H(e)||e.indexOf("x")<=0)?t:NaN}function np(e){return!isNaN(zi(e))}function mh(){return Math.round(Math.random()*9)}function wh(e,t){return t===0?e:wh(t,e%t)}function Gu(e,t){return e==null?t:t==null?e:e*t/wh(e,t)}function xt(e){throw new Error(e)}function Yu(e,t,r){return(t-e)*r+e}var Sh="series\0",ip="\0_ec_\0";function Lt(e){return e instanceof Array?e:e==null?[]:[e]}function Wu(e,t,r){if(e){e[t]=e[t]||{},e.emphasis=e.emphasis||{},e.emphasis[t]=e.emphasis[t]||{};for(var n=0,i=r.length;n<i;n++){var a=r[n];!e.emphasis[t].hasOwnProperty(a)&&e[t].hasOwnProperty(a)&&(e.emphasis[t][a]=e[t][a])}}}var Xu=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function na(e){return B(e)&&!F(e)&&!(e instanceof Date)?e.value:e}function k1(e){return B(e)&&!(e instanceof Array)}function ap(e,t,r){var n=r==="normalMerge",i=r==="replaceMerge",a=r==="replaceAll";e=e||[],t=(t||[]).slice();var o=Z();P(t,function(u,l){if(!B(u)){t[l]=null;return}});var s=op(e,o,r);return(n||i)&&sp(s,e,o,t),n&&up(s,t),n||i?lp(s,t,i):a&&fp(s,t),hp(s),s}function op(e,t,r){var n=[];if(r==="replaceAll")return n;for(var i=0;i<e.length;i++){var a=e[i];a&&a.id!=null&&t.set(a.id,i),n.push({existing:r==="replaceMerge"||Rn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return n}function sp(e,t,r,n){P(n,function(i,a){if(!(!i||i.id==null)){var o=gn(i.id),s=r.get(o);if(s!=null){var u=e[s];pe(!u.newOption,'Duplicated option on id "'+o+'".'),u.newOption=i,u.existing=t[s],n[a]=null}}})}function up(e,t){P(t,function(r,n){if(!(!r||r.name==null))for(var i=0;i<e.length;i++){var a=e[i].existing;if(!e[i].newOption&&a&&(a.id==null||r.id==null)&&!Rn(r)&&!Rn(a)&&Th("name",a,r)){e[i].newOption=r,t[n]=null;return}}})}function lp(e,t,r){P(t,function(n){if(n){for(var i,a=0;(i=e[a])&&(i.newOption||Rn(i.existing)||i.existing&&n.id!=null&&!Th("id",n,i.existing));)a++;i?(i.newOption=n,i.brandNew=r):e.push({newOption:n,brandNew:r,existing:null,keyInfo:null}),a++}})}function fp(e,t){P(t,function(r){e.push({newOption:r,brandNew:!0,existing:null,keyInfo:null})})}function hp(e){var t=Z();P(e,function(r){var n=r.existing;n&&t.set(n.id,r)}),P(e,function(r){var n=r.newOption;pe(!n||n.id==null||!t.get(n.id)||t.get(n.id)===r,"id duplicates: "+(n&&n.id)),n&&n.id!=null&&t.set(n.id,r),!r.keyInfo&&(r.keyInfo={})}),P(e,function(r,n){var i=r.existing,a=r.newOption,o=r.keyInfo;if(B(a)){if(o.name=a.name!=null?gn(a.name):i?i.name:Sh+n,i)o.id=gn(i.id);else if(a.id!=null)o.id=gn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,r)}})}function Th(e,t,r){var n=ur(t[e],null),i=ur(r[e],null);return n!=null&&i!=null&&n===i}function gn(e){return ur(e,"")}function ur(e,t){return e==null?t:H(e)?e:ut(e)||Co(e)?e+"":t}function bh(e){var t=e.name;return!!(t&&t.indexOf(Sh))}function Rn(e){return e&&e.id!=null&&gn(e.id).indexOf(ip)===0}function vp(e,t,r){P(e,function(n){var i=n.newOption;B(i)&&(n.keyInfo.mainType=t,n.keyInfo.subType=cp(t,i,n.existing,r))})}function cp(e,t,r,n){var i=t.type?t.type:r?r.subType:n.determineSubType(e,t);return i}function ia(e,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return F(t.dataIndex)?X(t.dataIndex,function(r){return e.indexOfRawIndex(r)}):e.indexOfRawIndex(t.dataIndex);if(t.name!=null)return F(t.name)?X(t.name,function(r){return e.indexOfName(r)}):e.indexOfName(t.name)}function Ot(){var e="__ec_inner_"+dp++;return function(t){return t[e]||(t[e]={})}}var dp=mh();function Ha(e,t,r){var n=Ch(t,r),i=n.mainTypeSpecified,a=n.queryOptionMap,o=n.others,s=o,u=r?r.defaultMainType:null;return!i&&u&&a.set(u,{}),a.each(function(l,f){var h=aa(e,f,l,{useDefault:u===f,enableAll:r&&r.enableAll!=null?r.enableAll:!0,enableNone:r&&r.enableNone!=null?r.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function Ch(e,t){var r;if(H(e)){var n={};n[e+"Index"]=0,r=n}else r=e;var i=Z(),a={},o=!1;return P(r,function(s,u){if(u==="dataIndex"||u==="dataIndexInside"){a[u]=s;return}var l=u.match(/^(\w+)(Index|Id|Name)$/)||[],f=l[1],h=(l[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&nt(t.includeMainTypes,f)<0)){o=o||!!f;var c=i.get(f)||i.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:i,others:a}}var Cs={useDefault:!0,enableAll:!1,enableNone:!1};function aa(e,t,r,n){n=n||Cs;var i=r.index,a=r.id,o=r.name,s={models:null,specified:i!=null||a!=null||o!=null};if(!s.specified){var u=void 0;return s.models=n.useDefault&&(u=e.getComponent(t))?[u]:[],s}return i==="none"||i===!1?(pe(n.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(i==="all"&&(pe(n.enableAll,'`"all"` is not a valid value on index option.'),i=a=o=null),s.models=e.queryComponents({mainType:t,index:i,id:a,name:o}),s)}function Mh(e,t,r){e.setAttribute?e.setAttribute(t,r):e[t]=r}function pp(e,t){return e.getAttribute?e.getAttribute(t):e[t]}function F1(e){return e==="auto"?U.domSupported?"html":"richText":e||"html"}function B1(e,t,r,n,i){var a=t==null||t==="auto";if(n==null)return n;if(ut(n)){var o=Yu(r||0,n,i);return Vo(o,a?Math.max(xr(r||0),xr(n)):t)}else{if(H(n))return i<1?r:n;for(var s=[],u=r,l=n,f=Math.max(u?u.length:0,l.length),h=0;h<f;++h){var c=e.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(i<1&&u?u:l)[h];else{var v=u&&u[h]?u[h]:0,d=l[h],o=Yu(v,d,i);s[h]=Vo(o,a?Math.max(xr(v),xr(d)):t)}}return s}}var gp=".",Ve="___EC__COMPONENT__CONTAINER___",Dh="___EC__EXTENDED_CLASS___";function se(e){var t={main:"",sub:""};if(e){var r=e.split(gp);t.main=r[0]||"",t.sub=r[1]||""}return t}function _p(e){pe(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e),'componentType "'+e+'" illegal')}function yp(e){return!!(e&&e[Dh])}function Ms(e,t){e.$constructor=e,e.extend=function(r){var n=this,i;return mp(n)?i=function(a){N(o,a);function o(){return a.apply(this,arguments)||this}return o}(n):(i=function(){(r.$constructor||n).apply(this,arguments)},Ec(i,this)),L(i.prototype,r),i[Dh]=!0,i.extend=this.extend,i.superCall=Tp,i.superApply=bp,i.superClass=n,i}}function mp(e){return at(e)&&/^class\s/.test(Function.prototype.toString.call(e))}function Ph(e,t){e.extend=t.extend}var wp=Math.round(Math.random()*10);function Sp(e){var t=["__\0is_clz",wp++].join("_");e.prototype[t]=!0,e.isInstance=function(r){return!!(r&&r[t])}}function Tp(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return this.superClass.prototype[t].apply(e,r)}function bp(e,t,r){return this.superClass.prototype[t].apply(e,r)}function Ds(e){var t={};e.registerClass=function(n){var i=n.type||n.prototype.type;if(i){_p(i),n.prototype.type=i;var a=se(i);if(!a.sub)t[a.main]=n;else if(a.sub!==Ve){var o=r(a);o[a.sub]=n}}return n},e.getClass=function(n,i,a){var o=t[n];if(o&&o[Ve]&&(o=i?o[i]:null),a&&!o)throw new Error(i?"Component "+n+"."+(i||"")+" is used but not imported.":n+".type should be specified.");return o},e.getClassesByMainType=function(n){var i=se(n),a=[],o=t[i.main];return o&&o[Ve]?P(o,function(s,u){u!==Ve&&a.push(s)}):a.push(o),a},e.hasClass=function(n){var i=se(n);return!!t[i.main]},e.getAllClassMainTypes=function(){var n=[];return P(t,function(i,a){n.push(a)}),n},e.hasSubTypes=function(n){var i=se(n),a=t[i.main];return a&&a[Ve]};function r(n){var i=t[n.main];return(!i||!i[Ve])&&(i=t[n.main]={},i[Ve]=!0),i}}function An(e,t){for(var r=0;r<e.length;r++)e[r][1]||(e[r][1]=e[r][0]);return t=t||!1,function(n,i,a){for(var o={},s=0;s<e.length;s++){var u=e[s][1];if(!(i&&nt(i,u)>=0||a&&nt(a,u)<0)){var l=n.getShallow(u,t);l!=null&&(o[e[s][0]]=l)}}return o}}var Cp=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],Mp=An(Cp),Dp=function(){function e(){}return e.prototype.getAreaStyle=function(t,r){return Mp(this,t,r)},e}(),Go=new Fn(50);function Pp(e){if(typeof e=="string"){var t=Go.get(e);return t&&t.image}else return e}function Rh(e,t,r,n,i){if(e)if(typeof e=="string"){if(t&&t.__zrImageSrc===e||!r)return t;var a=Go.get(e),o={hostEl:r,cb:n,cbPayload:i};return a?(t=a.image,!oa(t)&&a.pending.push(o)):(t=On.loadImage(e,qu,qu),t.__zrImageSrc=e,Go.put(e,t.__cachedImgObj={image:t,pending:[o]})),t}else return e;else return t}function qu(){var e=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<e.pending.length;t++){var r=e.pending[t],n=r.cb;n&&n(this,r.cbPayload),r.hostEl.dirty()}e.pending.length=0}function oa(e){return e&&e.width&&e.height}var Ua=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Rp(e,t,r,n,i,a){if(!r){e.text="",e.isTruncated=!1;return}var o=(t+"").split(`
`);a=Ah(r,n,i,a);for(var s=!1,u={},l=0,f=o.length;l<f;l++)xh(u,o[l],a),o[l]=u.textLine,s=s||u.isTruncated;e.text=o.join(`
`),e.isTruncated=s}function Ah(e,t,r,n){n=n||{};var i=L({},n);i.font=t,r=Y(r,"..."),i.maxIterations=Y(n.maxIterations,2);var a=i.minChar=Y(n.minChar,0);i.cnCharWidth=Et("国",t);var o=i.ascCharWidth=Et("a",t);i.placeholder=Y(n.placeholder,"");for(var s=e=Math.max(0,e-1),u=0;u<a&&s>=o;u++)s-=o;var l=Et(r,t);return l>s&&(r="",l=0),s=e-l,i.ellipsis=r,i.ellipsisWidth=l,i.contentWidth=s,i.containerWidth=e,i}function xh(e,t,r){var n=r.containerWidth,i=r.font,a=r.contentWidth;if(!n){e.textLine="",e.isTruncated=!1;return}var o=Et(t,i);if(o<=n){e.textLine=t,e.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=r.maxIterations){t+=r.ellipsis;break}var u=s===0?Ap(t,a,r.ascCharWidth,r.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,u),o=Et(t,i)}t===""&&(t=r.placeholder),e.textLine=t,e.isTruncated=!0}function Ap(e,t,r,n){for(var i=0,a=0,o=e.length;a<o&&i<t;a++){var s=e.charCodeAt(a);i+=0<=s&&s<=127?r:n}return a}function xp(e,t){e!=null&&(e+="");var r=t.overflow,n=t.padding,i=t.font,a=r==="truncate",o=bs(i),s=Y(t.lineHeight,o),u=!!t.backgroundColor,l=t.lineOverflow==="truncate",f=!1,h=t.width,c;h!=null&&(r==="break"||r==="breakAll")?c=e?Eh(e,t.font,h,r==="breakAll",0).lines:[]:c=e?e.split(`
`):[];var v=c.length*s,d=Y(t.height,v);if(v>d&&l){var _=Math.floor(d/s);f=f||c.length>_,c=c.slice(0,_)}if(e&&a&&h!=null)for(var p=Ah(h,i,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},y=0;y<c.length;y++)xh(g,c[y],p),c[y]=g.textLine,f=f||g.isTruncated;for(var m=d,w=0,y=0;y<c.length;y++)w=Math.max(Et(c[y],i),w);h==null&&(h=w);var T=w;return n&&(m+=n[0]+n[2],T+=n[1]+n[3],h+=n[1]+n[3]),u&&(T=h),{lines:c,height:d,outerWidth:T,outerHeight:m,lineHeight:s,calculatedLineHeight:o,contentWidth:w,contentHeight:v,width:h,isTruncated:f}}var Ep=function(){function e(){}return e}(),$u=function(){function e(t){this.tokens=[],t&&(this.tokens=t)}return e}(),Lp=function(){function e(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return e}();function Ip(e,t){var r=new Lp;if(e!=null&&(e+=""),!e)return r;for(var n=t.width,i=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&n!=null?{width:n,accumWidth:0,breakAll:a==="breakAll"}:null,s=Ua.lastIndex=0,u;(u=Ua.exec(e))!=null;){var l=u.index;l>s&&Va(r,e.substring(s,l),t,o),Va(r,u[2],t,o,u[1]),s=Ua.lastIndex}s<e.length&&Va(r,e.substring(s,e.length),t,o);var f=[],h=0,c=0,v=t.padding,d=a==="truncate",_=t.lineOverflow==="truncate",p={};function g(j,ot,K){j.width=ot,j.lineHeight=K,h+=K,c=Math.max(c,ot)}t:for(var y=0;y<r.lines.length;y++){for(var m=r.lines[y],w=0,T=0,S=0;S<m.tokens.length;S++){var b=m.tokens[S],C=b.styleName&&t.rich[b.styleName]||{},M=b.textPadding=C.padding,A=M?M[1]+M[3]:0,D=b.font=C.font||t.font;b.contentHeight=bs(D);var R=Y(C.height,b.contentHeight);if(b.innerHeight=R,M&&(R+=M[0]+M[2]),b.height=R,b.lineHeight=wi(C.lineHeight,t.lineHeight,R),b.align=C&&C.align||t.align,b.verticalAlign=C&&C.verticalAlign||"middle",_&&i!=null&&h+b.lineHeight>i){var I=r.lines.length;S>0?(m.tokens=m.tokens.slice(0,S),g(m,T,w),r.lines=r.lines.slice(0,y+1)):r.lines=r.lines.slice(0,y),r.isTruncated=r.isTruncated||r.lines.length<I;break t}var x=C.width,O=x==null||x==="auto";if(typeof x=="string"&&x.charAt(x.length-1)==="%")b.percentWidth=x,f.push(b),b.contentWidth=Et(b.text,D);else{if(O){var E=C.backgroundColor,V=E&&E.image;V&&(V=Pp(V),oa(V)&&(b.width=Math.max(b.width,V.width*R/V.height)))}var q=d&&n!=null?n-T:null;q!=null&&q<b.width?!O||q<A?(b.text="",b.width=b.contentWidth=0):(Rp(p,b.text,q-A,D,t.ellipsis,{minChar:t.truncateMinChar}),b.text=p.text,r.isTruncated=r.isTruncated||p.isTruncated,b.width=b.contentWidth=Et(b.text,D)):b.contentWidth=Et(b.text,D)}b.width+=A,T+=b.width,C&&(w=Math.max(w,b.lineHeight))}g(m,T,w)}r.outerWidth=r.width=Y(n,c),r.outerHeight=r.height=Y(i,h),r.contentHeight=h,r.contentWidth=c,v&&(r.outerWidth+=v[1]+v[3],r.outerHeight+=v[0]+v[2]);for(var y=0;y<f.length;y++){var b=f[y],J=b.percentWidth;b.width=parseInt(J,10)/100*r.width}return r}function Va(e,t,r,n,i){var a=t==="",o=i&&r.rich[i]||{},s=e.lines,u=o.font||r.font,l=!1,f,h;if(n){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var d=Pn(o.width,n.width)+v;s.length>0&&d+n.accumWidth>n.width&&(f=t.split(`
`),l=!0),n.accumWidth=d}else{var _=Eh(t,u,n.width,n.breakAll,n.accumWidth);n.accumWidth=_.accumWidth+v,h=_.linesWidths,f=_.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var g=f[p],y=new Ep;if(y.styleName=i,y.text=g,y.isLineHolder=!g&&!a,typeof o.width=="number"?y.width=o.width:y.width=h?h[p]:Et(g,u),!p&&!l){var m=(s[s.length-1]||(s[0]=new $u)).tokens,w=m.length;w===1&&m[0].isLineHolder?m[0]=y:(g||!w||a)&&m.push(y)}else s.push(new $u([y]))}}function Op(e){var t=e.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var kp=Ee(",&?/;] ".split(""),function(e,t){return e[t]=!0,e},{});function Fp(e){return Op(e)?!!kp[e]:!0}function Eh(e,t,r,n,i){for(var a=[],o=[],s="",u="",l=0,f=0,h=0;h<e.length;h++){var c=e.charAt(h);if(c===`
`){u&&(s+=u,f+=l),a.push(s),o.push(f),s="",u="",l=0,f=0;continue}var v=Et(c,t),d=n?!1:!Fp(c);if(a.length?f+v>r:i+f+v>r){f?(s||u)&&(d?(s||(s=u,u="",l=0,f=l),a.push(s),o.push(f-l),u+=c,l+=v,s="",f=l):(u&&(s+=u,u="",l=0),a.push(s),o.push(f),s=c,f=v)):d?(a.push(u),o.push(l),u=c,l=v):(a.push(c),o.push(v));continue}f+=v,d?(u+=c,l+=v):(u&&(s+=u,u="",l=0),s+=c)}return!a.length&&!s&&(s=e,u="",l=0),u&&(s+=u),s&&(a.push(s),o.push(f)),a.length===1&&(f+=i),{accumWidth:f,lines:a,linesWidths:o}}var Yo="__zr_style_"+Math.round(Math.random()*10),ir={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},sa={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};ir[Yo]=!0;var Zu=["z","z2","invisible"],Bp=["invisible"],Bn=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype._init=function(r){for(var n=it(r),i=0;i<n.length;i++){var a=n[i];a==="style"?this.useStyle(r[a]):e.prototype.attrKV.call(this,a,r[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(r,n,i,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&zp(this,r,n)||o&&!o[0]&&!o[3])return!1;if(i&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var u=this.parent;u;){if(u.ignore)return!1;u=u.parent}return!0},t.prototype.contain=function(r,n){return this.rectContain(r,n)},t.prototype.traverse=function(r,n){r.call(n,this)},t.prototype.rectContain=function(r,n){var i=this.transformCoordToLocal(r,n),a=this.getBoundingRect();return a.contain(i[0],i[1])},t.prototype.getPaintRect=function(){var r=this._paintRect;if(!this._paintRect||this.__dirty){var n=this.transform,i=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,u=a.shadowOffsetY||0;r=this._paintRect||(this._paintRect=new tt(0,0,0,0)),n?tt.applyTransform(r,i,n):r.copy(i),(o||s||u)&&(r.width+=o*2+Math.abs(s),r.height+=o*2+Math.abs(u),r.x=Math.min(r.x,r.x+s-o),r.y=Math.min(r.y,r.y+u-o));var l=this.dirtyRectTolerance;r.isZero()||(r.x=Math.floor(r.x-l),r.y=Math.floor(r.y-l),r.width=Math.ceil(r.width+1+l*2),r.height=Math.ceil(r.height+1+l*2))}return r},t.prototype.setPrevPaintRect=function(r){r?(this._prevPaintRect=this._prevPaintRect||new tt(0,0,0,0),this._prevPaintRect.copy(r)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(r){return this.animate("style",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(r,n){r!=="style"?e.prototype.attrKV.call(this,r,n):this.style?this.setStyle(n):this.useStyle(n)},t.prototype.setStyle=function(r,n){return typeof r=="string"?this.style[r]=n:L(this.style,r),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(r){r||this.markRedraw(),this.__dirty|=on,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&on)},t.prototype.styleUpdated=function(){this.__dirty&=~on},t.prototype.createStyle=function(r){return ea(ir,r)},t.prototype.useStyle=function(r){r[Yo]||(r=this.createStyle(r)),this.__inHover?this.__hoverStyle=r:this.style=r,this.dirtyStyle()},t.prototype.isStyleObject=function(r){return r[Yo]},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var n=this._normalState;r.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(r,n,Zu)},t.prototype._applyStateObj=function(r,n,i,a,o,s){e.prototype._applyStateObj.call(this,r,n,i,a,o,s);var u=!(n&&a),l;if(n&&n.style?o?a?l=n.style:(l=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(l,n.style)):(l=this._mergeStyle(this.createStyle(),a?this.style:i.style),this._mergeStyle(l,n.style)):u&&(l=i.style),l)if(o){var f=this.style;if(this.style=this.createStyle(u?{}:f),u)for(var h=it(f),c=0;c<h.length;c++){var v=h[c];v in l&&(l[v]=l[v],this.style[v]=f[v])}for(var d=it(l),c=0;c<d.length;c++){var v=d[c];this.style[v]=this.style[v]}this._transitionState(r,{style:l},s,this.getAnimationStyleProps())}else this.useStyle(l);for(var _=this.__inHover?Bp:Zu,c=0;c<_.length;c++){var v=_[c];n&&n[v]!=null?this[v]=n[v]:u&&i[v]!=null&&(this[v]=i[v])}},t.prototype._mergeStates=function(r){for(var n=e.prototype._mergeStates.call(this,r),i,a=0;a<r.length;a++){var o=r[a];o.style&&(i=i||{},this._mergeStyle(i,o.style))}return i&&(n.style=i),n},t.prototype._mergeStyle=function(r,n){return L(r,n),r},t.prototype.getAnimationStyleProps=function(){return sa},t.initDefaultProps=function(){var r=t.prototype;r.type="displayable",r.invisible=!1,r.z=0,r.z2=0,r.zlevel=0,r.culling=!1,r.cursor="pointer",r.rectHover=!1,r.incremental=!1,r._rect=null,r.dirtyRectTolerance=0,r.__dirty=ae|on}(),t}(ra),Ga=new tt(0,0,0,0),Ya=new tt(0,0,0,0);function zp(e,t,r){return Ga.copy(e.getBoundingRect()),e.transform&&Ga.applyTransform(e.transform),Ya.width=t,Ya.height=r,!Ga.intersect(Ya)}var Ht=Math.min,Ut=Math.max,Wa=Math.sin,Xa=Math.cos,Ge=Math.PI*2,Jn=Nr(),jn=Nr(),ti=Nr();function Ku(e,t,r,n,i,a){i[0]=Ht(e,r),i[1]=Ht(t,n),a[0]=Ut(e,r),a[1]=Ut(t,n)}var Qu=[],Ju=[];function Np(e,t,r,n,i,a,o,s,u,l){var f=oh,h=mt,c=f(e,r,i,o,Qu);u[0]=1/0,u[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var v=0;v<c;v++){var d=h(e,r,i,o,Qu[v]);u[0]=Ht(d,u[0]),l[0]=Ut(d,l[0])}c=f(t,n,a,s,Ju);for(var v=0;v<c;v++){var _=h(t,n,a,s,Ju[v]);u[1]=Ht(_,u[1]),l[1]=Ut(_,l[1])}u[0]=Ht(e,u[0]),l[0]=Ut(e,l[0]),u[0]=Ht(o,u[0]),l[0]=Ut(o,l[0]),u[1]=Ht(t,u[1]),l[1]=Ut(t,l[1]),u[1]=Ht(s,u[1]),l[1]=Ut(s,l[1])}function Hp(e,t,r,n,i,a,o,s){var u=sh,l=bt,f=Ut(Ht(u(e,r,i),1),0),h=Ut(Ht(u(t,n,a),1),0),c=l(e,r,i,f),v=l(t,n,a,h);o[0]=Ht(e,i,c),o[1]=Ht(t,a,v),s[0]=Ut(e,i,c),s[1]=Ut(t,a,v)}function Up(e,t,r,n,i,a,o,s,u){var l=Pr,f=Rr,h=Math.abs(i-a);if(h%Ge<1e-4&&h>1e-4){s[0]=e-r,s[1]=t-n,u[0]=e+r,u[1]=t+n;return}if(Jn[0]=Xa(i)*r+e,Jn[1]=Wa(i)*n+t,jn[0]=Xa(a)*r+e,jn[1]=Wa(a)*n+t,l(s,Jn,jn),f(u,Jn,jn),i=i%Ge,i<0&&(i=i+Ge),a=a%Ge,a<0&&(a=a+Ge),i>a&&!o?a+=Ge:i<a&&o&&(i+=Ge),o){var c=a;a=i,i=c}for(var v=0;v<a;v+=Math.PI/2)v>i&&(ti[0]=Xa(v)*r+e,ti[1]=Wa(v)*n+t,l(s,ti,s),f(u,ti,u))}var G={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Ye=[],We=[],jt=[],we=[],te=[],ee=[],qa=Math.min,$a=Math.max,Xe=Math.cos,qe=Math.sin,fe=Math.abs,Wo=Math.PI,Me=Wo*2,Za=typeof Float32Array<"u",Zr=[];function Ka(e){var t=Math.round(e/Wo*1e8)/1e8;return t%2*Wo}function Vp(e,t){var r=Ka(e[0]);r<0&&(r+=Me);var n=r-e[0],i=e[1];i+=n,!t&&i-r>=Me?i=r+Me:t&&r-i>=Me?i=r-Me:!t&&r>i?i=r+(Me-Ka(r-i)):t&&r<i&&(i=r-(Me-Ka(i-r))),e[0]=r,e[1]=i}var Br=function(){function e(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return e.prototype.increaseVersion=function(){this._version++},e.prototype.getVersion=function(){return this._version},e.prototype.setScale=function(t,r,n){n=n||0,n>0&&(this._ux=fe(n/Fu/t)||0,this._uy=fe(n/Fu/r)||0)},e.prototype.setDPR=function(t){this.dpr=t},e.prototype.setContext=function(t){this._ctx=t},e.prototype.getContext=function(){return this._ctx},e.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},e.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},e.prototype.moveTo=function(t,r){return this._drawPendingPt(),this.addData(G.M,t,r),this._ctx&&this._ctx.moveTo(t,r),this._x0=t,this._y0=r,this._xi=t,this._yi=r,this},e.prototype.lineTo=function(t,r){var n=fe(t-this._xi),i=fe(r-this._yi),a=n>this._ux||i>this._uy;if(this.addData(G.L,t,r),this._ctx&&a&&this._ctx.lineTo(t,r),a)this._xi=t,this._yi=r,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=r,this._pendingPtDist=o)}return this},e.prototype.bezierCurveTo=function(t,r,n,i,a,o){return this._drawPendingPt(),this.addData(G.C,t,r,n,i,a,o),this._ctx&&this._ctx.bezierCurveTo(t,r,n,i,a,o),this._xi=a,this._yi=o,this},e.prototype.quadraticCurveTo=function(t,r,n,i){return this._drawPendingPt(),this.addData(G.Q,t,r,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,r,n,i),this._xi=n,this._yi=i,this},e.prototype.arc=function(t,r,n,i,a,o){this._drawPendingPt(),Zr[0]=i,Zr[1]=a,Vp(Zr,o),i=Zr[0],a=Zr[1];var s=a-i;return this.addData(G.A,t,r,n,n,i,s,0,o?0:1),this._ctx&&this._ctx.arc(t,r,n,i,a,o),this._xi=Xe(a)*n+t,this._yi=qe(a)*n+r,this},e.prototype.arcTo=function(t,r,n,i,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,r,n,i,a),this},e.prototype.rect=function(t,r,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,r,n,i),this.addData(G.R,t,r,n,i),this},e.prototype.closePath=function(){this._drawPendingPt(),this.addData(G.Z);var t=this._ctx,r=this._x0,n=this._y0;return t&&t.closePath(),this._xi=r,this._yi=n,this},e.prototype.fill=function(t){t&&t.fill(),this.toStatic()},e.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},e.prototype.len=function(){return this._len},e.prototype.setData=function(t){var r=t.length;!(this.data&&this.data.length===r)&&Za&&(this.data=new Float32Array(r));for(var n=0;n<r;n++)this.data[n]=t[n];this._len=r},e.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var r=t.length,n=0,i=this._len,a=0;a<r;a++)n+=t[a].len();Za&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(var a=0;a<r;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[i++]=o[s];this._len=i},e.prototype.addData=function(t,r,n,i,a,o,s,u,l){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},e.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},e.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],r=0;r<this._len;r++)t[r]=this.data[r];this.data=t}},e.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,Za&&this._len>11&&(this.data=new Float32Array(t)))}},e.prototype.getBoundingRect=function(){jt[0]=jt[1]=te[0]=te[1]=Number.MAX_VALUE,we[0]=we[1]=ee[0]=ee[1]=-Number.MAX_VALUE;var t=this.data,r=0,n=0,i=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],u=o===1;switch(u&&(r=t[o],n=t[o+1],i=r,a=n),s){case G.M:r=i=t[o++],n=a=t[o++],te[0]=i,te[1]=a,ee[0]=i,ee[1]=a;break;case G.L:Ku(r,n,t[o],t[o+1],te,ee),r=t[o++],n=t[o++];break;case G.C:Np(r,n,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],te,ee),r=t[o++],n=t[o++];break;case G.Q:Hp(r,n,t[o++],t[o++],t[o],t[o+1],te,ee),r=t[o++],n=t[o++];break;case G.A:var l=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],d=t[o++]+v;o+=1;var _=!t[o++];u&&(i=Xe(v)*h+l,a=qe(v)*c+f),Up(l,f,h,c,v,d,_,te,ee),r=Xe(d)*h+l,n=qe(d)*c+f;break;case G.R:i=r=t[o++],a=n=t[o++];var p=t[o++],g=t[o++];Ku(i,a,i+p,a+g,te,ee);break;case G.Z:r=i,n=a;break}Pr(jt,jt,te),Rr(we,we,ee)}return o===0&&(jt[0]=jt[1]=we[0]=we[1]=0),new tt(jt[0],jt[1],we[0]-jt[0],we[1]-jt[1])},e.prototype._calculateLength=function(){var t=this.data,r=this._len,n=this._ux,i=this._uy,a=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,f=0,h=0,c=0;c<r;){var v=t[c++],d=c===1;d&&(a=t[c],o=t[c+1],s=a,u=o);var _=-1;switch(v){case G.M:a=s=t[c++],o=u=t[c++];break;case G.L:{var p=t[c++],g=t[c++],y=p-a,m=g-o;(fe(y)>n||fe(m)>i||c===r-1)&&(_=Math.sqrt(y*y+m*m),a=p,o=g);break}case G.C:{var w=t[c++],T=t[c++],p=t[c++],g=t[c++],S=t[c++],b=t[c++];_=md(a,o,w,T,p,g,S,b,10),a=S,o=b;break}case G.Q:{var w=t[c++],T=t[c++],p=t[c++],g=t[c++];_=Td(a,o,w,T,p,g,10),a=p,o=g;break}case G.A:var C=t[c++],M=t[c++],A=t[c++],D=t[c++],R=t[c++],I=t[c++],x=I+R;c+=1,d&&(s=Xe(R)*A+C,u=qe(R)*D+M),_=$a(A,D)*qa(Me,Math.abs(I)),a=Xe(x)*A+C,o=qe(x)*D+M;break;case G.R:{s=a=t[c++],u=o=t[c++];var O=t[c++],E=t[c++];_=O*2+E*2;break}case G.Z:{var y=s-a,m=u-o;_=Math.sqrt(y*y+m*m),a=s,o=u;break}}_>=0&&(l[h++]=_,f+=_)}return this._pathLen=f,f},e.prototype.rebuildPath=function(t,r){var n=this.data,i=this._ux,a=this._uy,o=this._len,s,u,l,f,h,c,v=r<1,d,_,p=0,g=0,y,m=0,w,T;if(!(v&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,_=this._pathLen,y=r*_,!y)))t:for(var S=0;S<o;){var b=n[S++],C=S===1;switch(C&&(l=n[S],f=n[S+1],s=l,u=f),b!==G.L&&m>0&&(t.lineTo(w,T),m=0),b){case G.M:s=l=n[S++],u=f=n[S++],t.moveTo(l,f);break;case G.L:{h=n[S++],c=n[S++];var M=fe(h-l),A=fe(c-f);if(M>i||A>a){if(v){var D=d[g++];if(p+D>y){var R=(y-p)/D;t.lineTo(l*(1-R)+h*R,f*(1-R)+c*R);break t}p+=D}t.lineTo(h,c),l=h,f=c,m=0}else{var I=M*M+A*A;I>m&&(w=h,T=c,m=I)}break}case G.C:{var x=n[S++],O=n[S++],E=n[S++],V=n[S++],q=n[S++],J=n[S++];if(v){var D=d[g++];if(p+D>y){var R=(y-p)/D;ki(l,x,E,q,R,Ye),ki(f,O,V,J,R,We),t.bezierCurveTo(Ye[1],We[1],Ye[2],We[2],Ye[3],We[3]);break t}p+=D}t.bezierCurveTo(x,O,E,V,q,J),l=q,f=J;break}case G.Q:{var x=n[S++],O=n[S++],E=n[S++],V=n[S++];if(v){var D=d[g++];if(p+D>y){var R=(y-p)/D;Fi(l,x,E,R,Ye),Fi(f,O,V,R,We),t.quadraticCurveTo(Ye[1],We[1],Ye[2],We[2]);break t}p+=D}t.quadraticCurveTo(x,O,E,V),l=E,f=V;break}case G.A:var j=n[S++],ot=n[S++],K=n[S++],pt=n[S++],gt=n[S++],Yt=n[S++],Ie=n[S++],Oe=!n[S++],fr=K>pt?K:pt,Rt=fe(K-pt)>.001,lt=gt+Yt,k=!1;if(v){var D=d[g++];p+D>y&&(lt=gt+Yt*(y-p)/D,k=!0),p+=D}if(Rt&&t.ellipse?t.ellipse(j,ot,K,pt,Ie,gt,lt,Oe):t.arc(j,ot,fr,gt,lt,Oe),k)break t;C&&(s=Xe(gt)*K+j,u=qe(gt)*pt+ot),l=Xe(lt)*K+j,f=qe(lt)*pt+ot;break;case G.R:s=l=n[S],u=f=n[S+1],h=n[S++],c=n[S++];var z=n[S++],ke=n[S++];if(v){var D=d[g++];if(p+D>y){var _t=y-p;t.moveTo(h,c),t.lineTo(h+qa(_t,z),c),_t-=z,_t>0&&t.lineTo(h+z,c+qa(_t,ke)),_t-=ke,_t>0&&t.lineTo(h+$a(z-_t,0),c+ke),_t-=z,_t>0&&t.lineTo(h,c+$a(ke-_t,0));break t}p+=D}t.rect(h,c,z,ke);break;case G.Z:if(v){var D=d[g++];if(p+D>y){var R=(y-p)/D;t.lineTo(l*(1-R)+s*R,f*(1-R)+u*R);break t}p+=D}t.closePath(),l=s,f=u}}},e.prototype.clone=function(){var t=new e,r=this.data;return t.data=r.slice?r.slice():Array.prototype.slice.call(r),t._len=this._len,t},e.CMD=G,e.initDefaultProps=function(){var t=e.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),e}();function gr(e,t,r,n,i,a,o){if(i===0)return!1;var s=i,u=0,l=e;if(o>t+s&&o>n+s||o<t-s&&o<n-s||a>e+s&&a>r+s||a<e-s&&a<r-s)return!1;if(e!==r)u=(t-n)/(e-r),l=(e*n-r*t)/(e-r);else return Math.abs(a-e)<=s/2;var f=u*a-o+l,h=f*f/(u*u+1);return h<=s/2*s/2}function Gp(e,t,r,n,i,a,o,s,u,l,f){if(u===0)return!1;var h=u;if(f>t+h&&f>n+h&&f>a+h&&f>s+h||f<t-h&&f<n-h&&f<a-h&&f<s-h||l>e+h&&l>r+h&&l>i+h&&l>o+h||l<e-h&&l<r-h&&l<i-h&&l<o-h)return!1;var c=yd(e,t,r,n,i,a,o,s,l,f);return c<=h/2}function Yp(e,t,r,n,i,a,o,s,u){if(o===0)return!1;var l=o;if(u>t+l&&u>n+l&&u>a+l||u<t-l&&u<n-l&&u<a-l||s>e+l&&s>r+l&&s>i+l||s<e-l&&s<r-l&&s<i-l)return!1;var f=Sd(e,t,r,n,i,a,s,u);return f<=l/2}var ju=Math.PI*2;function ei(e){return e%=ju,e<0&&(e+=ju),e}var Kr=Math.PI*2;function Wp(e,t,r,n,i,a,o,s,u){if(o===0)return!1;var l=o;s-=e,u-=t;var f=Math.sqrt(s*s+u*u);if(f-l>r||f+l<r)return!1;if(Math.abs(n-i)%Kr<1e-4)return!0;if(a){var h=n;n=ei(i),i=ei(h)}else n=ei(n),i=ei(i);n>i&&(i+=Kr);var c=Math.atan2(u,s);return c<0&&(c+=Kr),c>=n&&c<=i||c+Kr>=n&&c+Kr<=i}function $e(e,t,r,n,i,a){if(a>t&&a>n||a<t&&a<n||n===t)return 0;var o=(a-t)/(n-t),s=n<t?1:-1;(o===1||o===0)&&(s=n<t?.5:-.5);var u=o*(r-e)+e;return u===i?1/0:u>i?s:0}var Se=Br.CMD,Ze=Math.PI*2,Xp=1e-4;function qp(e,t){return Math.abs(e-t)<Xp}var yt=[-1,-1,-1],zt=[-1,-1];function $p(){var e=zt[0];zt[0]=zt[1],zt[1]=e}function Zp(e,t,r,n,i,a,o,s,u,l){if(l>t&&l>n&&l>a&&l>s||l<t&&l<n&&l<a&&l<s)return 0;var f=ah(t,n,a,s,l,yt);if(f===0)return 0;for(var h=0,c=-1,v=void 0,d=void 0,_=0;_<f;_++){var p=yt[_],g=p===0||p===1?.5:1,y=mt(e,r,i,o,p);y<u||(c<0&&(c=oh(t,n,a,s,zt),zt[1]<zt[0]&&c>1&&$p(),v=mt(t,n,a,s,zt[0]),c>1&&(d=mt(t,n,a,s,zt[1]))),c===2?p<zt[0]?h+=v<t?g:-g:p<zt[1]?h+=d<v?g:-g:h+=s<d?g:-g:p<zt[0]?h+=v<t?g:-g:h+=s<v?g:-g)}return h}function Kp(e,t,r,n,i,a,o,s){if(s>t&&s>n&&s>a||s<t&&s<n&&s<a)return 0;var u=wd(t,n,a,s,yt);if(u===0)return 0;var l=sh(t,n,a);if(l>=0&&l<=1){for(var f=0,h=bt(t,n,a,l),c=0;c<u;c++){var v=yt[c]===0||yt[c]===1?.5:1,d=bt(e,r,i,yt[c]);d<o||(yt[c]<l?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=yt[0]===0||yt[0]===1?.5:1,d=bt(e,r,i,yt[0]);return d<o?0:a<t?v:-v}}function Qp(e,t,r,n,i,a,o,s){if(s-=t,s>r||s<-r)return 0;var u=Math.sqrt(r*r-s*s);yt[0]=-u,yt[1]=u;var l=Math.abs(n-i);if(l<1e-4)return 0;if(l>=Ze-1e-4){n=0,i=Ze;var f=a?1:-1;return o>=yt[0]+e&&o<=yt[1]+e?f:0}if(n>i){var h=n;n=i,i=h}n<0&&(n+=Ze,i+=Ze);for(var c=0,v=0;v<2;v++){var d=yt[v];if(d+e>o){var _=Math.atan2(s,d),f=a?1:-1;_<0&&(_=Ze+_),(_>=n&&_<=i||_+Ze>=n&&_+Ze<=i)&&(_>Math.PI/2&&_<Math.PI*1.5&&(f=-f),c+=f)}}return c}function Lh(e,t,r,n,i){for(var a=e.data,o=e.len(),s=0,u=0,l=0,f=0,h=0,c,v,d=0;d<o;){var _=a[d++],p=d===1;switch(_===Se.M&&d>1&&(r||(s+=$e(u,l,f,h,n,i))),p&&(u=a[d],l=a[d+1],f=u,h=l),_){case Se.M:f=a[d++],h=a[d++],u=f,l=h;break;case Se.L:if(r){if(gr(u,l,a[d],a[d+1],t,n,i))return!0}else s+=$e(u,l,a[d],a[d+1],n,i)||0;u=a[d++],l=a[d++];break;case Se.C:if(r){if(Gp(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,n,i))return!0}else s+=Zp(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],n,i)||0;u=a[d++],l=a[d++];break;case Se.Q:if(r){if(Yp(u,l,a[d++],a[d++],a[d],a[d+1],t,n,i))return!0}else s+=Kp(u,l,a[d++],a[d++],a[d],a[d+1],n,i)||0;u=a[d++],l=a[d++];break;case Se.A:var g=a[d++],y=a[d++],m=a[d++],w=a[d++],T=a[d++],S=a[d++];d+=1;var b=!!(1-a[d++]);c=Math.cos(T)*m+g,v=Math.sin(T)*w+y,p?(f=c,h=v):s+=$e(u,l,c,v,n,i);var C=(n-g)*w/m+g;if(r){if(Wp(g,y,w,T,T+S,b,t,C,i))return!0}else s+=Qp(g,y,w,T,T+S,b,C,i);u=Math.cos(T+S)*m+g,l=Math.sin(T+S)*w+y;break;case Se.R:f=u=a[d++],h=l=a[d++];var M=a[d++],A=a[d++];if(c=f+M,v=h+A,r){if(gr(f,h,c,h,t,n,i)||gr(c,h,c,v,t,n,i)||gr(c,v,f,v,t,n,i)||gr(f,v,f,h,t,n,i))return!0}else s+=$e(c,h,c,v,n,i),s+=$e(f,v,f,h,n,i);break;case Se.Z:if(r){if(gr(u,l,f,h,t,n,i))return!0}else s+=$e(u,l,f,h,n,i);u=f,l=h;break}}return!r&&!qp(l,h)&&(s+=$e(u,l,f,h,n,i)||0),s!==0}function Jp(e,t,r){return Lh(e,0,!1,t,r)}function jp(e,t,r,n){return Lh(e,t,!0,r,n)}var Ih=wt({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ir),tg={style:wt({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},sa.style)},Qa=Dn.concat(["invisible","culling","z","z2","zlevel","parent"]),Q=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.update=function(){var r=this;e.prototype.update.call(this);var n=this.style;if(n.decal){var i=this._decalEl=this._decalEl||new t;i.buildPath===t.prototype.buildPath&&(i.buildPath=function(u){r.buildPath(u,r.shape)}),i.silent=!0;var a=i.style;for(var o in n)a[o]!==n[o]&&(a[o]=n[o]);a.fill=n.fill?n.decal:null,a.decal=null,a.shadowColor=null,n.strokeFirst&&(a.stroke=null);for(var s=0;s<Qa.length;++s)i[Qa[s]]=this[Qa[s]];i.__dirty|=ae}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(r){var n=it(r);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var a=0;a<n.length;a++){var o=n[a],s=r[o];o==="style"?this.style?L(this.style,s):this.useStyle(s):o==="shape"?L(this.shape,s):e.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var r=this.style.fill;if(r!=="none"){if(H(r)){var n=Bi(r,0);return n>.5?Ho:n>.2?Ud:Uo}else if(r)return Uo}return Ho},t.prototype.getInsideTextStroke=function(r){var n=this.style.fill;if(H(n)){var i=this.__zr,a=!!(i&&i.isDarkMode()),o=Bi(r,0)<No;if(a===o)return n}},t.prototype.buildPath=function(r,n,i){},t.prototype.pathUpdated=function(){this.__dirty&=~Mr},t.prototype.getUpdatedPathProxy=function(r){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,r),this.path},t.prototype.createPathProxy=function(){this.path=new Br(!1)},t.prototype.hasStroke=function(){var r=this.style,n=r.stroke;return!(n==null||n==="none"||!(r.lineWidth>0))},t.prototype.hasFill=function(){var r=this.style,n=r.fill;return n!=null&&n!=="none"},t.prototype.getBoundingRect=function(){var r=this._rect,n=this.style,i=!r;if(i){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&Mr)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),r=o.getBoundingRect()}if(this._rect=r,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=r.clone());if(this.__dirty||i){s.copy(r);var u=n.strokeNoScale?this.getLineScale():1,l=n.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;l=Math.max(l,f??4)}u>1e-10&&(s.width+=l/u,s.height+=l/u,s.x-=l/u/2,s.y-=l/u/2)}return s}return r},t.prototype.contain=function(r,n){var i=this.transformCoordToLocal(r,n),a=this.getBoundingRect(),o=this.style;if(r=i[0],n=i[1],a.contain(r,n)){var s=this.path;if(this.hasStroke()){var u=o.lineWidth,l=o.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(this.hasFill()||(u=Math.max(u,this.strokeContainThreshold)),jp(s,u/l,r,n)))return!0}if(this.hasFill())return Jp(s,r,n)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Mr,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(r){return this.animate("shape",r)},t.prototype.updateDuringAnimation=function(r){r==="style"?this.dirtyStyle():r==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(r,n){r==="shape"?this.setShape(n):e.prototype.attrKV.call(this,r,n)},t.prototype.setShape=function(r,n){var i=this.shape;return i||(i=this.shape={}),typeof r=="string"?i[r]=n:L(i,r),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Mr)},t.prototype.createStyle=function(r){return ea(Ih,r)},t.prototype._innerSaveToNormal=function(r){e.prototype._innerSaveToNormal.call(this,r);var n=this._normalState;r.shape&&!n.shape&&(n.shape=L({},this.shape))},t.prototype._applyStateObj=function(r,n,i,a,o,s){e.prototype._applyStateObj.call(this,r,n,i,a,o,s);var u=!(n&&a),l;if(n&&n.shape?o?a?l=n.shape:(l=L({},i.shape),L(l,n.shape)):(l=L({},a?this.shape:i.shape),L(l,n.shape)):u&&(l=i.shape),l)if(o){this.shape=L({},this.shape);for(var f={},h=it(l),c=0;c<h.length;c++){var v=h[c];typeof l[v]=="object"?this.shape[v]=l[v]:f[v]=l[v]}this._transitionState(r,{shape:f},s)}else this.shape=l,this.dirtyShape()},t.prototype._mergeStates=function(r){for(var n=e.prototype._mergeStates.call(this,r),i,a=0;a<r.length;a++){var o=r[a];o.shape&&(i=i||{},this._mergeStyle(i,o.shape))}return i&&(n.shape=i),n},t.prototype.getAnimationStyleProps=function(){return tg},t.prototype.isZeroArea=function(){return!1},t.extend=function(r){var n=function(a){N(o,a);function o(s){var u=a.call(this,s)||this;return r.init&&r.init.call(u,s),u}return o.prototype.getDefaultStyle=function(){return W(r.style)},o.prototype.getDefaultShape=function(){return W(r.shape)},o}(t);for(var i in r)typeof r[i]=="function"&&(n.prototype[i]=r[i]);return n},t.initDefaultProps=function(){var r=t.prototype;r.type="path",r.strokeContainThreshold=5,r.segmentIgnoreThreshold=0,r.subPixelOptimize=!1,r.autoBatch=!1,r.__dirty=ae|on|Mr}(),t}(Bn),eg=wt({strokeFirst:!0,font:sr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Ih),Ni=function(e){N(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var r=this.style,n=r.stroke;return n!=null&&n!=="none"&&r.lineWidth>0},t.prototype.hasFill=function(){var r=this.style,n=r.fill;return n!=null&&n!=="none"},t.prototype.createStyle=function(r){return ea(eg,r)},t.prototype.setBoundingRect=function(r){this._rect=r},t.prototype.getBoundingRect=function(){var r=this.style;if(!this._rect){var n=r.text;n!=null?n+="":n="";var i=Gd(n,r.font,r.textAlign,r.textBaseline);if(i.x+=r.x||0,i.y+=r.y||0,this.hasStroke()){var a=r.lineWidth;i.x-=a/2,i.y-=a/2,i.width+=a,i.height+=a}this._rect=i}return this._rect},t.initDefaultProps=function(){var r=t.prototype;r.dirtyRectTolerance=10}(),t}(Bn);Ni.prototype.type="tspan";var rg=wt({x:0,y:0},ir),ng={style:wt({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},sa.style)};function ig(e){return!!(e&&typeof e!="string"&&e.width&&e.height)}var lr=function(e){N(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.createStyle=function(r){return ea(rg,r)},t.prototype._getSize=function(r){var n=this.style,i=n[r];if(i!=null)return i;var a=ig(n.image)?n.image:this.__image;if(!a)return 0;var o=r==="width"?"height":"width",s=n[o];return s==null?a[r]:a[r]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return ng},t.prototype.getBoundingRect=function(){var r=this.style;return this._rect||(this._rect=new tt(r.x||0,r.y||0,this.getWidth(),this.getHeight())),this._rect},t}(Bn);lr.prototype.type="image";function ag(e,t){var r=t.x,n=t.y,i=t.width,a=t.height,o=t.r,s,u,l,f;i<0&&(r=r+i,i=-i),a<0&&(n=n+a,a=-a),typeof o=="number"?s=u=l=f=o:o instanceof Array?o.length===1?s=u=l=f=o[0]:o.length===2?(s=l=o[0],u=f=o[1]):o.length===3?(s=o[0],u=f=o[1],l=o[2]):(s=o[0],u=o[1],l=o[2],f=o[3]):s=u=l=f=0;var h;s+u>i&&(h=s+u,s*=i/h,u*=i/h),l+f>i&&(h=l+f,l*=i/h,f*=i/h),u+l>a&&(h=u+l,u*=a/h,l*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),e.moveTo(r+s,n),e.lineTo(r+i-u,n),u!==0&&e.arc(r+i-u,n+u,u,-Math.PI/2,0),e.lineTo(r+i,n+a-l),l!==0&&e.arc(r+i-l,n+a-l,l,0,Math.PI/2),e.lineTo(r+f,n+a),f!==0&&e.arc(r+f,n+a-f,f,Math.PI/2,Math.PI),e.lineTo(r,n+s),s!==0&&e.arc(r+s,n+s,s,Math.PI,Math.PI*1.5)}var Er=Math.round;function Oh(e,t,r){if(t){var n=t.x1,i=t.x2,a=t.y1,o=t.y2;e.x1=n,e.x2=i,e.y1=a,e.y2=o;var s=r&&r.lineWidth;return s&&(Er(n*2)===Er(i*2)&&(e.x1=e.x2=rr(n,s,!0)),Er(a*2)===Er(o*2)&&(e.y1=e.y2=rr(a,s,!0))),e}}function kh(e,t,r){if(t){var n=t.x,i=t.y,a=t.width,o=t.height;e.x=n,e.y=i,e.width=a,e.height=o;var s=r&&r.lineWidth;return s&&(e.x=rr(n,s,!0),e.y=rr(i,s,!0),e.width=Math.max(rr(n+a,s,!1)-e.x,a===0?0:1),e.height=Math.max(rr(i+o,s,!1)-e.y,o===0?0:1)),e}}function rr(e,t,r){if(!t)return e;var n=Er(e*2);return(n+Er(t))%2===0?n/2:(n+(r?1:-1))/2}var og=function(){function e(){this.x=0,this.y=0,this.width=0,this.height=0}return e}(),sg={},ue=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new og},t.prototype.buildPath=function(r,n){var i,a,o,s;if(this.subPixelOptimize){var u=kh(sg,n,this.style);i=u.x,a=u.y,o=u.width,s=u.height,u.r=n.r,n=u}else i=n.x,a=n.y,o=n.width,s=n.height;n.r?ag(r,n):r.rect(i,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(Q);ue.prototype.type="rect";var tl={fill:"#000"},el=2,ug={style:wt({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},sa.style)},zr=function(e){N(t,e);function t(r){var n=e.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=tl,n.attr(r),n}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){e.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var r=0;r<this._children.length;r++){var n=this._children[r];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},t.prototype.updateTransform=function(){var r=this.innerTransformable;r?(r.updateTransform(),r.transform&&(this.transform=r.transform)):e.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(r){var n=this.innerTransformable;return n?n.getLocalTransform(r):e.prototype.getLocalTransform.call(this,r)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),e.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,cg(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(r){e.prototype.addSelfToZr.call(this,r);for(var n=0;n<this._children.length;n++)this._children[n].__zr=r},t.prototype.removeSelfFromZr=function(r){e.prototype.removeSelfFromZr.call(this,r);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var r=new tt(0,0,0,0),n=this._children,i=[],a=null,o=0;o<n.length;o++){var s=n[o],u=s.getBoundingRect(),l=s.getLocalTransform(i);l?(r.copy(u),r.applyTransform(l),a=a||r.clone(),a.union(r)):(a=a||u.clone(),a.union(u))}this._rect=a||r}return this._rect},t.prototype.setDefaultTextStyle=function(r){this._defaultStyle=r||tl},t.prototype.setTextContent=function(r){},t.prototype._mergeStyle=function(r,n){if(!n)return r;var i=n.rich,a=r.rich||i&&{};return L(r,n),i&&a?(this._mergeRich(a,i),r.rich=a):a&&(r.rich=a),r},t.prototype._mergeRich=function(r,n){for(var i=it(n),a=0;a<i.length;a++){var o=i[a];r[o]=r[o]||{},L(r[o],n[o])}},t.prototype.getAnimationStyleProps=function(){return ug},t.prototype._getOrCreateChild=function(r){var n=this._children[this._childCursor];return(!n||!(n instanceof r))&&(n=new r),this._children[this._childCursor++]=n,n.__zr=this.__zr,n.parent=this,n},t.prototype._updatePlainTexts=function(){var r=this.style,n=r.font||sr,i=r.padding,a=ul(r),o=xp(a,r),s=Ja(r),u=!!r.backgroundColor,l=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var _=r.x||0,p=r.y||0,g=r.align||d.align||"left",y=r.verticalAlign||d.verticalAlign||"top",m=_,w=Dr(p,o.contentHeight,y);if(s||i){var T=un(_,f,g),S=Dr(p,l,y);s&&this._renderBackground(r,r,T,S,f,l)}w+=v/2,i&&(m=sl(_,g,i),y==="top"?w+=i[0]:y==="bottom"&&(w-=i[2]));for(var b=0,C=!1,M=ol("fill"in r?r.fill:(C=!0,d.fill)),A=al("stroke"in r?r.stroke:!u&&(!d.autoStroke||C)?(b=el,d.stroke):null),D=r.textShadowBlur>0,R=r.width!=null&&(r.overflow==="truncate"||r.overflow==="break"||r.overflow==="breakAll"),I=o.calculatedLineHeight,x=0;x<c.length;x++){var O=this._getOrCreateChild(Ni),E=O.createStyle();O.useStyle(E),E.text=c[x],E.x=m,E.y=w,E.textAlign=g,E.textBaseline="middle",E.opacity=r.opacity,E.strokeFirst=!0,D&&(E.shadowBlur=r.textShadowBlur||0,E.shadowColor=r.textShadowColor||"transparent",E.shadowOffsetX=r.textShadowOffsetX||0,E.shadowOffsetY=r.textShadowOffsetY||0),E.stroke=A,E.fill=M,A&&(E.lineWidth=r.lineWidth||b,E.lineDash=r.lineDash,E.lineDashOffset=r.lineDashOffset||0),E.font=n,nl(E,r),w+=v,R&&O.setBoundingRect(new tt(un(E.x,h,E.textAlign),Dr(E.y,I,E.textBaseline),h,I))}},t.prototype._updateRichTexts=function(){var r=this.style,n=ul(r),i=Ip(n,r),a=i.width,o=i.outerWidth,s=i.outerHeight,u=r.padding,l=r.x||0,f=r.y||0,h=this._defaultStyle,c=r.align||h.align,v=r.verticalAlign||h.verticalAlign;this.isTruncated=!!i.isTruncated;var d=un(l,o,c),_=Dr(f,s,v),p=d,g=_;u&&(p+=u[3],g+=u[0]);var y=p+a;Ja(r)&&this._renderBackground(r,r,d,_,o,s);for(var m=!!r.backgroundColor,w=0;w<i.lines.length;w++){for(var T=i.lines[w],S=T.tokens,b=S.length,C=T.lineHeight,M=T.width,A=0,D=p,R=y,I=b-1,x=void 0;A<b&&(x=S[A],!x.align||x.align==="left");)this._placeToken(x,r,C,g,D,"left",m),M-=x.width,D+=x.width,A++;for(;I>=0&&(x=S[I],x.align==="right");)this._placeToken(x,r,C,g,R,"right",m),M-=x.width,R-=x.width,I--;for(D+=(a-(D-p)-(y-R)-M)/2;A<=I;)x=S[A],this._placeToken(x,r,C,g,D+x.width/2,"center",m),D+=x.width,A++;g+=C}},t.prototype._placeToken=function(r,n,i,a,o,s,u){var l=n.rich[r.styleName]||{};l.text=r.text;var f=r.verticalAlign,h=a+i/2;f==="top"?h=a+r.height/2:f==="bottom"&&(h=a+i-r.height/2);var c=!r.isLineHolder&&Ja(l);c&&this._renderBackground(l,n,s==="right"?o-r.width:s==="center"?o-r.width/2:o,h-r.height/2,r.width,r.height);var v=!!l.backgroundColor,d=r.textPadding;d&&(o=sl(o,s,d),h-=r.height/2-d[0]-r.innerHeight/2);var _=this._getOrCreateChild(Ni),p=_.createStyle();_.useStyle(p);var g=this._defaultStyle,y=!1,m=0,w=ol("fill"in l?l.fill:"fill"in n?n.fill:(y=!0,g.fill)),T=al("stroke"in l?l.stroke:"stroke"in n?n.stroke:!v&&!u&&(!g.autoStroke||y)?(m=el,g.stroke):null),S=l.textShadowBlur>0||n.textShadowBlur>0;p.text=r.text,p.x=o,p.y=h,S&&(p.shadowBlur=l.textShadowBlur||n.textShadowBlur||0,p.shadowColor=l.textShadowColor||n.textShadowColor||"transparent",p.shadowOffsetX=l.textShadowOffsetX||n.textShadowOffsetX||0,p.shadowOffsetY=l.textShadowOffsetY||n.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=r.font||sr,p.opacity=wi(l.opacity,n.opacity,1),nl(p,l),T&&(p.lineWidth=wi(l.lineWidth,n.lineWidth,m),p.lineDash=Y(l.lineDash,n.lineDash),p.lineDashOffset=n.lineDashOffset||0,p.stroke=T),w&&(p.fill=w);var b=r.contentWidth,C=r.contentHeight;_.setBoundingRect(new tt(un(p.x,b,p.textAlign),Dr(p.y,C,p.textBaseline),b,C))},t.prototype._renderBackground=function(r,n,i,a,o,s){var u=r.backgroundColor,l=r.borderWidth,f=r.borderColor,h=u&&u.image,c=u&&!h,v=r.borderRadius,d=this,_,p;if(c||r.lineHeight||l&&f){_=this._getOrCreateChild(ue),_.useStyle(_.createStyle()),_.style.fill=null;var g=_.shape;g.x=i,g.y=a,g.width=o,g.height=s,g.r=v,_.dirtyShape()}if(c){var y=_.style;y.fill=u||null,y.fillOpacity=Y(r.fillOpacity,1)}else if(h){p=this._getOrCreateChild(lr),p.onload=function(){d.dirtyStyle()};var m=p.style;m.image=u.image,m.x=i,m.y=a,m.width=o,m.height=s}if(l&&f){var y=_.style;y.lineWidth=l,y.stroke=f,y.strokeOpacity=Y(r.strokeOpacity,1),y.lineDash=r.borderDash,y.lineDashOffset=r.borderDashOffset||0,_.strokeContainThreshold=0,_.hasFill()&&_.hasStroke()&&(y.strokeFirst=!0,y.lineWidth*=2)}var w=(_||p).style;w.shadowBlur=r.shadowBlur||0,w.shadowColor=r.shadowColor||"transparent",w.shadowOffsetX=r.shadowOffsetX||0,w.shadowOffsetY=r.shadowOffsetY||0,w.opacity=wi(r.opacity,n.opacity,1)},t.makeFont=function(r){var n="";return vg(r)&&(n=[r.fontStyle,r.fontWeight,hg(r.fontSize),r.fontFamily||"sans-serif"].join(" ")),n&&ce(n)||r.textFont||r.font},t}(Bn),lg={left:!0,right:1,center:1},fg={top:1,bottom:1,middle:1},rl=["fontStyle","fontWeight","fontSize","fontFamily"];function hg(e){return typeof e=="string"&&(e.indexOf("px")!==-1||e.indexOf("rem")!==-1||e.indexOf("em")!==-1)?e:isNaN(+e)?ds+"px":e+"px"}function nl(e,t){for(var r=0;r<rl.length;r++){var n=rl[r],i=t[n];i!=null&&(e[n]=i)}}function vg(e){return e.fontSize!=null||e.fontFamily||e.fontWeight}function cg(e){return il(e),P(e.rich,il),e}function il(e){if(e){e.font=zr.makeFont(e);var t=e.align;t==="middle"&&(t="center"),e.align=t==null||lg[t]?t:"left";var r=e.verticalAlign;r==="center"&&(r="middle"),e.verticalAlign=r==null||fg[r]?r:"top";var n=e.padding;n&&(e.padding=qf(e.padding))}}function al(e,t){return e==null||t<=0||e==="transparent"||e==="none"?null:e.image||e.colorStops?"#000":e}function ol(e){return e==null||e==="none"?null:e.image||e.colorStops?"#000":e}function sl(e,t,r){return t==="right"?e-r[1]:t==="center"?e+r[3]/2-r[1]/2:e+r[3]}function ul(e){var t=e.text;return t!=null&&(t+=""),t}function Ja(e){return!!(e.backgroundColor||e.lineHeight||e.borderWidth&&e.borderColor)}var Gt=Ot(),z1=function(e,t,r,n){if(n){var i=Gt(n);i.dataIndex=r,i.dataType=t,i.seriesIndex=e,i.ssrType="chart",n.type==="group"&&n.traverse(function(a){var o=Gt(a);o.seriesIndex=e,o.dataIndex=r,o.dataType=t,o.ssrType="chart"})}},ll=1,fl={},Fh=Ot(),Ps=Ot(),Rs=0,ua=1,la=2,Le=["emphasis","blur","select"],hl=["normal","emphasis","blur","select"],dg=10,pg=9,ar="highlight",Ri="downplay",_n="select",Ai="unselect",yn="toggleSelect";function _r(e){return e!=null&&e!=="none"}function fa(e,t,r){e.onHoverStateChange&&(e.hoverState||0)!==r&&e.onHoverStateChange(t),e.hoverState=r}function Bh(e){fa(e,"emphasis",la)}function zh(e){e.hoverState===la&&fa(e,"normal",Rs)}function As(e){fa(e,"blur",ua)}function Nh(e){e.hoverState===ua&&fa(e,"normal",Rs)}function gg(e){e.selected=!0}function _g(e){e.selected=!1}function vl(e,t,r){t(e,r)}function ye(e,t,r){vl(e,t,r),e.isGroup&&e.traverse(function(n){vl(n,t,r)})}function N1(e,t){switch(t){case"emphasis":e.hoverState=la;break;case"normal":e.hoverState=Rs;break;case"blur":e.hoverState=ua;break;case"select":e.selected=!0}}function yg(e,t,r,n){for(var i=e.style,a={},o=0;o<t.length;o++){var s=t[o],u=i[s];a[s]=u??(n&&n[s])}for(var o=0;o<e.animators.length;o++){var l=e.animators[o];l.__fromStateTransition&&l.__fromStateTransition.indexOf(r)<0&&l.targetName==="style"&&l.saveTo(a,t)}return a}function mg(e,t,r,n){var i=r&&nt(r,"select")>=0,a=!1;if(e instanceof Q){var o=Fh(e),s=i&&o.selectFill||o.normalFill,u=i&&o.selectStroke||o.normalStroke;if(_r(s)||_r(u)){n=n||{};var l=n.style||{};l.fill==="inherit"?(a=!0,n=L({},n),l=L({},l),l.fill=s):!_r(l.fill)&&_r(s)?(a=!0,n=L({},n),l=L({},l),l.fill=Au(s)):!_r(l.stroke)&&_r(u)&&(a||(n=L({},n),l=L({},l)),l.stroke=Au(u)),n.style=l}}if(n&&n.z2==null){a||(n=L({},n));var f=e.z2EmphasisLift;n.z2=e.z2+(f??dg)}return n}function wg(e,t,r){if(r&&r.z2==null){r=L({},r);var n=e.z2SelectLift;r.z2=e.z2+(n??pg)}return r}function Sg(e,t,r){var n=nt(e.currentStates,t)>=0,i=e.style.opacity,a=n?null:yg(e,["opacity"],t,{opacity:1});r=r||{};var o=r.style||{};return o.opacity==null&&(r=L({},r),o=L({opacity:n?i:a.opacity*.1},o),r.style=o),r}function ja(e,t){var r=this.states[e];if(this.style){if(e==="emphasis")return mg(this,e,t,r);if(e==="blur")return Sg(this,e,r);if(e==="select")return wg(this,e,r)}return r}function Tg(e){e.stateProxy=ja;var t=e.getTextContent(),r=e.getTextGuideLine();t&&(t.stateProxy=ja),r&&(r.stateProxy=ja)}function cl(e,t){!Gh(e,t)&&!e.__highByOuter&&ye(e,Bh)}function dl(e,t){!Gh(e,t)&&!e.__highByOuter&&ye(e,zh)}function Xo(e,t){e.__highByOuter|=1<<(t||0),ye(e,Bh)}function qo(e,t){!(e.__highByOuter&=~(1<<(t||0)))&&ye(e,zh)}function bg(e){ye(e,As)}function Hh(e){ye(e,Nh)}function Uh(e){ye(e,gg)}function Vh(e){ye(e,_g)}function Gh(e,t){return e.__highDownSilentOnTouch&&t.zrByTouch}function Yh(e){var t=e.getModel(),r=[],n=[];t.eachComponent(function(i,a){var o=Ps(a),s=i==="series",u=s?e.getViewOfSeriesModel(a):e.getViewOfComponentModel(a);!s&&n.push(u),o.isBlured&&(u.group.traverse(function(l){Nh(l)}),s&&r.push(a)),o.isBlured=!1}),P(n,function(i){i&&i.toggleBlurSeries&&i.toggleBlurSeries(r,!1,t)})}function $o(e,t,r,n){var i=n.getModel();r=r||"coordinateSystem";function a(l,f){for(var h=0;h<f.length;h++){var c=l.getItemGraphicEl(f[h]);c&&Hh(c)}}if(e!=null&&!(!t||t==="none")){var o=i.getSeriesByIndex(e),s=o.coordinateSystem;s&&s.master&&(s=s.master);var u=[];i.eachSeries(function(l){var f=o===l,h=l.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(r==="series"&&!f||r==="coordinateSystem"&&!c||t==="series"&&f)){var v=n.getViewOfSeriesModel(l);if(v.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||As(p)}),It(t))a(l.getData(),t);else if(B(t))for(var d=it(t),_=0;_<d.length;_++)a(l.getData(d[_]),t[d[_]]);u.push(l),Ps(l).isBlured=!0}}),i.eachComponent(function(l,f){if(l!=="series"){var h=n.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(u,!0,i)}})}}function Zo(e,t,r){if(!(e==null||t==null)){var n=r.getModel().getComponent(e,t);if(n){Ps(n).isBlured=!0;var i=r.getViewOfComponentModel(n);!i||!i.focusBlurEnabled||i.group.traverse(function(a){As(a)})}}}function Cg(e,t,r){var n=e.seriesIndex,i=e.getData(t.dataType);if(i){var a=ia(i,t);a=(F(a)?a[0]:a)||0;var o=i.getItemGraphicEl(a);if(!o)for(var s=i.count(),u=0;!o&&u<s;)o=i.getItemGraphicEl(u++);if(o){var l=Gt(o);$o(n,l.focus,l.blurScope,r)}else{var f=e.get(["emphasis","focus"]),h=e.get(["emphasis","blurScope"]);f!=null&&$o(n,f,h,r)}}}function xs(e,t,r,n){var i={focusSelf:!1,dispatchers:null};if(e==null||e==="series"||t==null||r==null)return i;var a=n.getModel().getComponent(e,t);if(!a)return i;var o=n.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return i;for(var s=o.findHighDownDispatchers(r),u,l=0;l<s.length;l++)if(Gt(s[l]).focus==="self"){u=!0;break}return{focusSelf:u,dispatchers:s}}function Mg(e,t,r){var n=Gt(e),i=xs(n.componentMainType,n.componentIndex,n.componentHighDownName,r),a=i.dispatchers,o=i.focusSelf;a?(o&&Zo(n.componentMainType,n.componentIndex,r),P(a,function(s){return cl(s,t)})):($o(n.seriesIndex,n.focus,n.blurScope,r),n.focus==="self"&&Zo(n.componentMainType,n.componentIndex,r),cl(e,t))}function Dg(e,t,r){Yh(r);var n=Gt(e),i=xs(n.componentMainType,n.componentIndex,n.componentHighDownName,r).dispatchers;i?P(i,function(a){return dl(a,t)}):dl(e,t)}function Pg(e,t,r){if(Qo(t)){var n=t.dataType,i=e.getData(n),a=ia(i,t);F(a)||(a=[a]),e[t.type===yn?"toggleSelect":t.type===_n?"select":"unselect"](a,n)}}function pl(e){var t=e.getAllData();P(t,function(r){var n=r.data,i=r.type;n.eachItemGraphicEl(function(a,o){e.isSelected(o,i)?Uh(a):Vh(a)})})}function Rg(e){var t=[];return e.eachSeries(function(r){var n=r.getAllData();P(n,function(i){i.data;var a=i.type,o=r.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:r.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function Ag(e,t,r){Wh(e,!0),ye(e,Tg),Eg(e,t,r)}function xg(e){Wh(e,!1)}function H1(e,t,r,n){n?xg(e):Ag(e,t,r)}function Eg(e,t,r){var n=Gt(e);t!=null?(n.focus=t,n.blurScope=r):n.focus&&(n.focus=null)}var gl=["emphasis","blur","select"],Lg={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function U1(e,t,r,n){r=r||"itemStyle";for(var i=0;i<gl.length;i++){var a=gl[i],o=t.getModel([a,r]),s=e.ensureState(a);s.style=o[Lg[r]]()}}function Wh(e,t){var r=t===!1,n=e;e.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=e.highDownSilentOnTouch),(!r||n.__highDownDispatcher)&&(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!r)}function Ko(e){return!!(e&&e.__highDownDispatcher)}function Ig(e){var t=fl[e];return t==null&&ll<=32&&(t=fl[e]=ll++),t}function Qo(e){var t=e.type;return t===_n||t===Ai||t===yn}function _l(e){var t=e.type;return t===ar||t===Ri}function Og(e){var t=Fh(e);t.normalFill=e.style.fill,t.normalStroke=e.style.stroke;var r=e.states.select||{};t.selectFill=r.style&&r.style.fill||null,t.selectStroke=r.style&&r.style.stroke||null}var yr=Br.CMD,kg=[[],[],[]],yl=Math.sqrt,Fg=Math.atan2;function Bg(e,t){if(t){var r=e.data,n=e.len(),i,a,o,s,u,l,f=yr.M,h=yr.C,c=yr.L,v=yr.R,d=yr.A,_=yr.Q;for(o=0,s=0;o<n;){switch(i=r[o++],s=o,a=0,i){case f:a=1;break;case c:a=1;break;case h:a=3;break;case _:a=2;break;case d:var p=t[4],g=t[5],y=yl(t[0]*t[0]+t[1]*t[1]),m=yl(t[2]*t[2]+t[3]*t[3]),w=Fg(-t[1]/m,t[0]/y);r[o]*=y,r[o++]+=p,r[o]*=m,r[o++]+=g,r[o++]*=y,r[o++]*=m,r[o++]+=w,r[o++]+=w,o+=2,s=o;break;case v:l[0]=r[o++],l[1]=r[o++],Ir(l,l,t),r[s++]=l[0],r[s++]=l[1],l[0]+=r[o++],l[1]+=r[o++],Ir(l,l,t),r[s++]=l[0],r[s++]=l[1]}for(u=0;u<a;u++){var T=kg[u];T[0]=r[o++],T[1]=r[o++],Ir(T,T,t),r[s++]=T[0],r[s++]=T[1]}}e.increaseVersion()}}var to=Math.sqrt,ri=Math.sin,ni=Math.cos,Qr=Math.PI;function ml(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])}function Jo(e,t){return(e[0]*t[0]+e[1]*t[1])/(ml(e)*ml(t))}function wl(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(Jo(e,t))}function Sl(e,t,r,n,i,a,o,s,u,l,f){var h=u*(Qr/180),c=ni(h)*(e-r)/2+ri(h)*(t-n)/2,v=-1*ri(h)*(e-r)/2+ni(h)*(t-n)/2,d=c*c/(o*o)+v*v/(s*s);d>1&&(o*=to(d),s*=to(d));var _=(i===a?-1:1)*to((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,p=_*o*v/s,g=_*-s*c/o,y=(e+r)/2+ni(h)*p-ri(h)*g,m=(t+n)/2+ri(h)*p+ni(h)*g,w=wl([1,0],[(c-p)/o,(v-g)/s]),T=[(c-p)/o,(v-g)/s],S=[(-1*c-p)/o,(-1*v-g)/s],b=wl(T,S);if(Jo(T,S)<=-1&&(b=Qr),Jo(T,S)>=1&&(b=0),b<0){var C=Math.round(b/Qr*1e6)/1e6;b=Qr*2+C%2*Qr}f.addData(l,y,m,o,s,w,b,h,a)}var zg=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,Ng=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Hg(e){var t=new Br;if(!e)return t;var r=0,n=0,i=r,a=n,o,s=Br.CMD,u=e.match(zg);if(!u)return t;for(var l=0;l<u.length;l++){for(var f=u[l],h=f.charAt(0),c=void 0,v=f.match(Ng)||[],d=v.length,_=0;_<d;_++)v[_]=parseFloat(v[_]);for(var p=0;p<d;){var g=void 0,y=void 0,m=void 0,w=void 0,T=void 0,S=void 0,b=void 0,C=r,M=n,A=void 0,D=void 0;switch(h){case"l":r+=v[p++],n+=v[p++],c=s.L,t.addData(c,r,n);break;case"L":r=v[p++],n=v[p++],c=s.L,t.addData(c,r,n);break;case"m":r+=v[p++],n+=v[p++],c=s.M,t.addData(c,r,n),i=r,a=n,h="l";break;case"M":r=v[p++],n=v[p++],c=s.M,t.addData(c,r,n),i=r,a=n,h="L";break;case"h":r+=v[p++],c=s.L,t.addData(c,r,n);break;case"H":r=v[p++],c=s.L,t.addData(c,r,n);break;case"v":n+=v[p++],c=s.L,t.addData(c,r,n);break;case"V":n=v[p++],c=s.L,t.addData(c,r,n);break;case"C":c=s.C,t.addData(c,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),r=v[p-2],n=v[p-1];break;case"c":c=s.C,t.addData(c,v[p++]+r,v[p++]+n,v[p++]+r,v[p++]+n,v[p++]+r,v[p++]+n),r+=v[p-2],n+=v[p-1];break;case"S":g=r,y=n,A=t.len(),D=t.data,o===s.C&&(g+=r-D[A-4],y+=n-D[A-3]),c=s.C,C=v[p++],M=v[p++],r=v[p++],n=v[p++],t.addData(c,g,y,C,M,r,n);break;case"s":g=r,y=n,A=t.len(),D=t.data,o===s.C&&(g+=r-D[A-4],y+=n-D[A-3]),c=s.C,C=r+v[p++],M=n+v[p++],r+=v[p++],n+=v[p++],t.addData(c,g,y,C,M,r,n);break;case"Q":C=v[p++],M=v[p++],r=v[p++],n=v[p++],c=s.Q,t.addData(c,C,M,r,n);break;case"q":C=v[p++]+r,M=v[p++]+n,r+=v[p++],n+=v[p++],c=s.Q,t.addData(c,C,M,r,n);break;case"T":g=r,y=n,A=t.len(),D=t.data,o===s.Q&&(g+=r-D[A-4],y+=n-D[A-3]),r=v[p++],n=v[p++],c=s.Q,t.addData(c,g,y,r,n);break;case"t":g=r,y=n,A=t.len(),D=t.data,o===s.Q&&(g+=r-D[A-4],y+=n-D[A-3]),r+=v[p++],n+=v[p++],c=s.Q,t.addData(c,g,y,r,n);break;case"A":m=v[p++],w=v[p++],T=v[p++],S=v[p++],b=v[p++],C=r,M=n,r=v[p++],n=v[p++],c=s.A,Sl(C,M,r,n,S,b,m,w,T,c,t);break;case"a":m=v[p++],w=v[p++],T=v[p++],S=v[p++],b=v[p++],C=r,M=n,r+=v[p++],n+=v[p++],c=s.A,Sl(C,M,r,n,S,b,m,w,T,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),r=i,n=a),o=c}return t.toStatic(),t}var Xh=function(e){N(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.applyTransform=function(r){},t}(Q);function qh(e){return e.setData!=null}function $h(e,t){var r=Hg(e),n=L({},t);return n.buildPath=function(i){if(qh(i)){i.setData(r.data);var a=i.getContext();a&&i.rebuildPath(a,1)}else{var a=i;r.rebuildPath(a,1)}},n.applyTransform=function(i){Bg(r,i),this.dirtyShape()},n}function Ug(e,t){return new Xh($h(e,t))}function Vg(e,t){var r=$h(e,t),n=function(i){N(a,i);function a(o){var s=i.call(this,o)||this;return s.applyTransform=r.applyTransform,s.buildPath=r.buildPath,s}return a}(Xh);return n}function Gg(e,t){for(var r=[],n=e.length,i=0;i<n;i++){var a=e[i];r.push(a.getUpdatedPathProxy(!0))}var o=new Q(t);return o.createPathProxy(),o.buildPath=function(s){if(qh(s)){s.appendPath(r);var u=s.getContext();u&&s.rebuildPath(u,1)}},o}var Yg=function(){function e(){this.cx=0,this.cy=0,this.r=0}return e}(),ha=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Yg},t.prototype.buildPath=function(r,n){r.moveTo(n.cx+n.r,n.cy),r.arc(n.cx,n.cy,n.r,0,Math.PI*2)},t}(Q);ha.prototype.type="circle";var Wg=function(){function e(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return e}(),Es=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Wg},t.prototype.buildPath=function(r,n){var i=.5522848,a=n.cx,o=n.cy,s=n.rx,u=n.ry,l=s*i,f=u*i;r.moveTo(a-s,o),r.bezierCurveTo(a-s,o-f,a-l,o-u,a,o-u),r.bezierCurveTo(a+l,o-u,a+s,o-f,a+s,o),r.bezierCurveTo(a+s,o+f,a+l,o+u,a,o+u),r.bezierCurveTo(a-l,o+u,a-s,o+f,a-s,o),r.closePath()},t}(Q);Es.prototype.type="ellipse";var Zh=Math.PI,eo=Zh*2,Ke=Math.sin,mr=Math.cos,Xg=Math.acos,ft=Math.atan2,Tl=Math.abs,mn=Math.sqrt,ln=Math.max,re=Math.min,Xt=1e-4;function qg(e,t,r,n,i,a,o,s){var u=r-e,l=n-t,f=o-i,h=s-a,c=h*u-f*l;if(!(c*c<Xt))return c=(f*(t-a)-h*(e-i))/c,[e+c*u,t+c*l]}function ii(e,t,r,n,i,a,o){var s=e-r,u=t-n,l=(o?a:-a)/mn(s*s+u*u),f=l*u,h=-l*s,c=e+f,v=t+h,d=r+f,_=n+h,p=(c+d)/2,g=(v+_)/2,y=d-c,m=_-v,w=y*y+m*m,T=i-a,S=c*_-d*v,b=(m<0?-1:1)*mn(ln(0,T*T*w-S*S)),C=(S*m-y*b)/w,M=(-S*y-m*b)/w,A=(S*m+y*b)/w,D=(-S*y+m*b)/w,R=C-p,I=M-g,x=A-p,O=D-g;return R*R+I*I>x*x+O*O&&(C=A,M=D),{cx:C,cy:M,x0:-f,y0:-h,x1:C*(i/T-1),y1:M*(i/T-1)}}function $g(e){var t;if(F(e)){var r=e.length;if(!r)return e;r===1?t=[e[0],e[0],0,0]:r===2?t=[e[0],e[0],e[1],e[1]]:r===3?t=e.concat(e[2]):t=e}else t=[e,e,e,e];return t}function Zg(e,t){var r,n=ln(t.r,0),i=ln(t.r0||0,0),a=n>0,o=i>0;if(!(!a&&!o)){if(a||(n=i,i=0),i>n){var s=n;n=i,i=s}var u=t.startAngle,l=t.endAngle;if(!(isNaN(u)||isNaN(l))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=Tl(l-u),d=v>eo&&v%eo;if(d>Xt&&(v=d),!(n>Xt))e.moveTo(f,h);else if(v>eo-Xt)e.moveTo(f+n*mr(u),h+n*Ke(u)),e.arc(f,h,n,u,l,!c),i>Xt&&(e.moveTo(f+i*mr(l),h+i*Ke(l)),e.arc(f,h,i,l,u,c));else{var _=void 0,p=void 0,g=void 0,y=void 0,m=void 0,w=void 0,T=void 0,S=void 0,b=void 0,C=void 0,M=void 0,A=void 0,D=void 0,R=void 0,I=void 0,x=void 0,O=n*mr(u),E=n*Ke(u),V=i*mr(l),q=i*Ke(l),J=v>Xt;if(J){var j=t.cornerRadius;j&&(r=$g(j),_=r[0],p=r[1],g=r[2],y=r[3]);var ot=Tl(n-i)/2;if(m=re(ot,g),w=re(ot,y),T=re(ot,_),S=re(ot,p),M=b=ln(m,w),A=C=ln(T,S),(b>Xt||C>Xt)&&(D=n*mr(l),R=n*Ke(l),I=i*mr(u),x=i*Ke(u),v<Zh)){var K=qg(O,E,I,x,D,R,V,q);if(K){var pt=O-K[0],gt=E-K[1],Yt=D-K[0],Ie=R-K[1],Oe=1/Ke(Xg((pt*Yt+gt*Ie)/(mn(pt*pt+gt*gt)*mn(Yt*Yt+Ie*Ie)))/2),fr=mn(K[0]*K[0]+K[1]*K[1]);M=re(b,(n-fr)/(Oe+1)),A=re(C,(i-fr)/(Oe-1))}}}if(!J)e.moveTo(f+O,h+E);else if(M>Xt){var Rt=re(g,M),lt=re(y,M),k=ii(I,x,O,E,n,Rt,c),z=ii(D,R,V,q,n,lt,c);e.moveTo(f+k.cx+k.x0,h+k.cy+k.y0),M<b&&Rt===lt?e.arc(f+k.cx,h+k.cy,M,ft(k.y0,k.x0),ft(z.y0,z.x0),!c):(Rt>0&&e.arc(f+k.cx,h+k.cy,Rt,ft(k.y0,k.x0),ft(k.y1,k.x1),!c),e.arc(f,h,n,ft(k.cy+k.y1,k.cx+k.x1),ft(z.cy+z.y1,z.cx+z.x1),!c),lt>0&&e.arc(f+z.cx,h+z.cy,lt,ft(z.y1,z.x1),ft(z.y0,z.x0),!c))}else e.moveTo(f+O,h+E),e.arc(f,h,n,u,l,!c);if(!(i>Xt)||!J)e.lineTo(f+V,h+q);else if(A>Xt){var Rt=re(_,A),lt=re(p,A),k=ii(V,q,D,R,i,-lt,c),z=ii(O,E,I,x,i,-Rt,c);e.lineTo(f+k.cx+k.x0,h+k.cy+k.y0),A<C&&Rt===lt?e.arc(f+k.cx,h+k.cy,A,ft(k.y0,k.x0),ft(z.y0,z.x0),!c):(lt>0&&e.arc(f+k.cx,h+k.cy,lt,ft(k.y0,k.x0),ft(k.y1,k.x1),!c),e.arc(f,h,i,ft(k.cy+k.y1,k.cx+k.x1),ft(z.cy+z.y1,z.cx+z.x1),c),Rt>0&&e.arc(f+z.cx,h+z.cy,Rt,ft(z.y1,z.x1),ft(z.y0,z.x0),!c))}else e.lineTo(f+V,h+q),e.arc(f,h,i,l,u,c)}e.closePath()}}}var Kg=function(){function e(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return e}(),Ls=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Kg},t.prototype.buildPath=function(r,n){Zg(r,n)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(Q);Ls.prototype.type="sector";var Qg=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return e}(),Is=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new Qg},t.prototype.buildPath=function(r,n){var i=n.cx,a=n.cy,o=Math.PI*2;r.moveTo(i+n.r,a),r.arc(i,a,n.r,0,o,!1),r.moveTo(i+n.r0,a),r.arc(i,a,n.r0,0,o,!0)},t}(Q);Is.prototype.type="ring";function Jg(e,t,r,n){var i=[],a=[],o=[],s=[],u,l,f,h;if(n){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=e.length;c<v;c++)Pr(f,f,e[c]),Rr(h,h,e[c]);Pr(f,f,n[0]),Rr(h,h,n[1])}for(var c=0,v=e.length;c<v;c++){var d=e[c];if(r)u=e[c?c-1:v-1],l=e[(c+1)%v];else if(c===0||c===v-1){i.push(Nc(e[c]));continue}else u=e[c-1],l=e[c+1];Hc(a,l,u),ma(a,a,t);var _=Po(d,u),p=Po(d,l),g=_+p;g!==0&&(_/=g,p/=g),ma(o,a,-_),ma(s,a,p);var y=hu([],d,o),m=hu([],d,s);n&&(Rr(y,y,f),Pr(y,y,h),Rr(m,m,f),Pr(m,m,h)),i.push(y),i.push(m)}return r&&i.push(i.shift()),i}function Kh(e,t,r){var n=t.smooth,i=t.points;if(i&&i.length>=2){if(n){var a=Jg(i,n,r,t.smoothConstraint);e.moveTo(i[0][0],i[0][1]);for(var o=i.length,s=0;s<(r?o:o-1);s++){var u=a[s*2],l=a[s*2+1],f=i[(s+1)%o];e.bezierCurveTo(u[0],u[1],l[0],l[1],f[0],f[1])}}else{e.moveTo(i[0][0],i[0][1]);for(var s=1,h=i.length;s<h;s++)e.lineTo(i[s][0],i[s][1])}r&&e.closePath()}}var jg=function(){function e(){this.points=null,this.smooth=0,this.smoothConstraint=null}return e}(),Os=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultShape=function(){return new jg},t.prototype.buildPath=function(r,n){Kh(r,n,!0)},t}(Q);Os.prototype.type="polygon";var t0=function(){function e(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return e}(),ks=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new t0},t.prototype.buildPath=function(r,n){Kh(r,n,!1)},t}(Q);ks.prototype.type="polyline";var e0={},r0=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return e}(),va=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new r0},t.prototype.buildPath=function(r,n){var i,a,o,s;if(this.subPixelOptimize){var u=Oh(e0,n,this.style);i=u.x1,a=u.y1,o=u.x2,s=u.y2}else i=n.x1,a=n.y1,o=n.x2,s=n.y2;var l=n.percent;l!==0&&(r.moveTo(i,a),l<1&&(o=i*(1-l)+o*l,s=a*(1-l)+s*l),r.lineTo(o,s))},t.prototype.pointAt=function(r){var n=this.shape;return[n.x1*(1-r)+n.x2*r,n.y1*(1-r)+n.y2*r]},t}(Q);va.prototype.type="line";var St=[],n0=function(){function e(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return e}();function bl(e,t,r){var n=e.cpx2,i=e.cpy2;return n!=null||i!=null?[(r?bu:mt)(e.x1,e.cpx1,e.cpx2,e.x2,t),(r?bu:mt)(e.y1,e.cpy1,e.cpy2,e.y2,t)]:[(r?Cu:bt)(e.x1,e.cpx1,e.x2,t),(r?Cu:bt)(e.y1,e.cpy1,e.y2,t)]}var Fs=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new n0},t.prototype.buildPath=function(r,n){var i=n.x1,a=n.y1,o=n.x2,s=n.y2,u=n.cpx1,l=n.cpy1,f=n.cpx2,h=n.cpy2,c=n.percent;c!==0&&(r.moveTo(i,a),f==null||h==null?(c<1&&(Fi(i,u,o,c,St),u=St[1],o=St[2],Fi(a,l,s,c,St),l=St[1],s=St[2]),r.quadraticCurveTo(u,l,o,s)):(c<1&&(ki(i,u,f,o,c,St),u=St[1],f=St[2],o=St[3],ki(a,l,h,s,c,St),l=St[1],h=St[2],s=St[3]),r.bezierCurveTo(u,l,f,h,o,s)))},t.prototype.pointAt=function(r){return bl(this.shape,r,!1)},t.prototype.tangentAt=function(r){var n=bl(this.shape,r,!0);return Gc(n,n)},t}(Q);Fs.prototype.type="bezier-curve";var i0=function(){function e(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return e}(),ca=function(e){N(t,e);function t(r){return e.call(this,r)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new i0},t.prototype.buildPath=function(r,n){var i=n.cx,a=n.cy,o=Math.max(n.r,0),s=n.startAngle,u=n.endAngle,l=n.clockwise,f=Math.cos(s),h=Math.sin(s);r.moveTo(f*o+i,h*o+a),r.arc(i,a,o,s,u,!l)},t}(Q);ca.prototype.type="arc";var a0=function(e){N(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.type="compound",r}return t.prototype._updatePathDirty=function(){for(var r=this.shape.paths,n=this.shapeChanged(),i=0;i<r.length;i++)n=n||r[i].shapeChanged();n&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var r=this.shape.paths||[],n=this.getGlobalScale(),i=0;i<r.length;i++)r[i].path||r[i].createPathProxy(),r[i].path.setScale(n[0],n[1],r[i].segmentIgnoreThreshold)},t.prototype.buildPath=function(r,n){for(var i=n.paths||[],a=0;a<i.length;a++)i[a].buildPath(r,i[a].shape,!0)},t.prototype.afterBrush=function(){for(var r=this.shape.paths||[],n=0;n<r.length;n++)r[n].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Q.prototype.getBoundingRect.call(this)},t}(Q),Qh=function(){function e(t){this.colorStops=t||[]}return e.prototype.addColorStop=function(t,r){this.colorStops.push({offset:t,color:r})},e}(),o0=function(e){N(t,e);function t(r,n,i,a,o,s){var u=e.call(this,o)||this;return u.x=r??0,u.y=n??0,u.x2=i??1,u.y2=a??0,u.type="linear",u.global=s||!1,u}return t}(Qh),s0=function(e){N(t,e);function t(r,n,i,a,o){var s=e.call(this,a)||this;return s.x=r??.5,s.y=n??.5,s.r=i??.5,s.type="radial",s.global=o||!1,s}return t}(Qh),Qe=[0,0],Je=[0,0],ai=new $,oi=new $,u0=function(){function e(t,r){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new $;for(var n=0;n<2;n++)this._axes[n]=new $;t&&this.fromBoundingRect(t,r)}return e.prototype.fromBoundingRect=function(t,r){var n=this._corners,i=this._axes,a=t.x,o=t.y,s=a+t.width,u=o+t.height;if(n[0].set(a,o),n[1].set(s,o),n[2].set(s,u),n[3].set(a,u),r)for(var l=0;l<4;l++)n[l].transform(r);$.sub(i[0],n[1],n[0]),$.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(var l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},e.prototype.intersect=function(t,r){var n=!0,i=!r;return ai.set(1/0,1/0),oi.set(0,0),!this._intersectCheckOneSide(this,t,ai,oi,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,ai,oi,i,-1)&&(n=!1,i)||i||$.copy(r,n?ai:oi),n},e.prototype._intersectCheckOneSide=function(t,r,n,i,a,o){for(var s=!0,u=0;u<2;u++){var l=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,Qe),this._getProjMinMaxOnAxis(u,r._corners,Je),Qe[1]<Je[0]||Qe[0]>Je[1]){if(s=!1,a)return s;var f=Math.abs(Je[0]-Qe[1]),h=Math.abs(Qe[0]-Je[1]);Math.min(f,h)>i.len()&&(f<h?$.scale(i,l,-f*o):$.scale(i,l,h*o))}else if(n){var f=Math.abs(Je[0]-Qe[1]),h=Math.abs(Qe[0]-Je[1]);Math.min(f,h)<n.len()&&(f<h?$.scale(n,l,f*o):$.scale(n,l,-h*o))}}return s},e.prototype._getProjMinMaxOnAxis=function(t,r,n){for(var i=this._axes[t],a=this._origin,o=r[0].dot(i)+a[t],s=o,u=o,l=1;l<r.length;l++){var f=r[l].dot(i)+a[t];s=Math.min(f,s),u=Math.max(f,u)}n[0]=s,n[1]=u},e}(),l0=[],f0=function(e){N(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r.notClear=!0,r.incremental=!0,r._displayables=[],r._temporaryDisplayables=[],r._cursor=0,r}return t.prototype.traverse=function(r,n){r.call(n,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(r,n){n?this._temporaryDisplayables.push(r):this._displayables.push(r),this.markRedraw()},t.prototype.addDisplayables=function(r,n){n=n||!1;for(var i=0;i<r.length;i++)this.addDisplayable(r[i],n)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(r){for(var n=this._cursor;n<this._displayables.length;n++)r&&r(this._displayables[n]);for(var n=0;n<this._temporaryDisplayables.length;n++)r&&r(this._temporaryDisplayables[n])},t.prototype.update=function(){this.updateTransform();for(var r=this._cursor;r<this._displayables.length;r++){var n=this._displayables[r];n.parent=this,n.update(),n.parent=null}for(var r=0;r<this._temporaryDisplayables.length;r++){var n=this._temporaryDisplayables[r];n.parent=this,n.update(),n.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var r=new tt(1/0,1/0,-1/0,-1/0),n=0;n<this._displayables.length;n++){var i=this._displayables[n],a=i.getBoundingRect().clone();i.needLocalTransform()&&a.applyTransform(i.getLocalTransform(l0)),r.union(a)}this._rect=r}return this._rect},t.prototype.contain=function(r,n){var i=this.transformCoordToLocal(r,n),a=this.getBoundingRect();if(a.contain(i[0],i[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(r,n))return!0}return!1},t}(Bn),h0=Ot();function v0(e,t,r,n,i){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),u=e==="update";if(s){var l=void 0,f=void 0,h=void 0;n?(l=Y(n.duration,200),f=Y(n.easing,"cubicOut"),h=0):(l=t.getShallow(u?"animationDurationUpdate":"animationDuration"),f=t.getShallow(u?"animationEasingUpdate":"animationEasing"),h=t.getShallow(u?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(l=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),at(h)&&(h=h(r,i)),at(l)&&(l=l(r));var c={duration:l||0,delay:h,easing:f};return c}else return null}function Bs(e,t,r,n,i,a,o){var s=!1,u;at(i)?(o=a,a=i,i=null):B(i)&&(a=i.cb,o=i.during,s=i.isFrom,u=i.removeOpt,i=i.dataIndex);var l=e==="leave";l||t.stopAnimation("leave");var f=v0(e,n,i,l?u||{}:null,n&&n.getAnimationDelayParams?n.getAnimationDelayParams(t,i):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,d={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!l,scope:e,during:o};s?t.animateFrom(r,d):t.animateTo(r,d)}else t.stopAnimation(),!s&&t.attr(r),o&&o(1),a&&a()}function Jh(e,t,r,n,i,a){Bs("update",e,t,r,n,i,a)}function c0(e,t,r,n,i,a){Bs("enter",e,t,r,n,i,a)}function wn(e){if(!e.__zr)return!0;for(var t=0;t<e.animators.length;t++){var r=e.animators[t];if(r.scope==="leave")return!0}return!1}function jh(e,t,r,n,i,a){wn(e)||Bs("leave",e,t,r,n,i,a)}function Cl(e,t,r,n){e.removeTextContent(),e.removeTextGuideLine(),jh(e,{style:{opacity:0}},t,r,n)}function d0(e,t,r){function n(){e.parent&&e.parent.remove(e)}e.isGroup?e.traverse(function(i){i.isGroup||Cl(i,t,r,n)}):Cl(e,t,r,n)}function V1(e){h0(e).oldStyle=e.style}var Hi=Math.max,Ui=Math.min,jo={};function p0(e){return Q.extend(e)}var g0=Vg;function _0(e,t){return g0(e,t)}function Qt(e,t){jo[e]=t}function y0(e){if(jo.hasOwnProperty(e))return jo[e]}function zs(e,t,r,n){var i=Ug(e,t);return r&&(n==="center"&&(r=ev(r,i.getBoundingRect())),rv(i,r)),i}function tv(e,t,r){var n=new lr({style:{image:e,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(i){if(r==="center"){var a={width:i.width,height:i.height};n.setStyle(ev(t,a))}}});return n}function ev(e,t){var r=t.width/t.height,n=e.height*r,i;n<=e.width?i=e.height:(n=e.width,i=n/r);var a=e.x+e.width/2,o=e.y+e.height/2;return{x:a-n/2,y:o-i/2,width:n,height:i}}var m0=Gg;function rv(e,t){if(e.applyTransform){var r=e.getBoundingRect(),n=r.calculateTransform(t);e.applyTransform(n)}}function w0(e,t){return Oh(e,e,{lineWidth:t}),e}function S0(e){return kh(e.shape,e.shape,e.style),e}var T0=rr;function b0(e,t){for(var r=Qf([]);e&&e!==t;)Si(r,e.getLocalTransform(),r),e=e.parent;return r}function nv(e,t,r){return t&&!It(t)&&(t=Ts.getLocalTransform(t)),r&&(t=Jf([],t)),Ir([],e,t)}function C0(e,t,r){var n=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),i=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[e==="left"?-n:e==="right"?n:0,e==="top"?-i:e==="bottom"?i:0];return a=nv(a,t,r),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function Ml(e){return!e.isGroup}function M0(e){return e.shape!=null}function D0(e,t,r){if(!e||!t)return;function n(o){var s={};return o.traverse(function(u){Ml(u)&&u.anid&&(s[u.anid]=u)}),s}function i(o){var s={x:o.x,y:o.y,rotation:o.rotation};return M0(o)&&(s.shape=L({},o.shape)),s}var a=n(e);t.traverse(function(o){if(Ml(o)&&o.anid){var s=a[o.anid];if(s){var u=i(o);o.attr(i(s)),Jh(o,u,r,Gt(o).dataIndex)}}})}function P0(e,t){return X(e,function(r){var n=r[0];n=Hi(n,t.x),n=Ui(n,t.x+t.width);var i=r[1];return i=Hi(i,t.y),i=Ui(i,t.y+t.height),[n,i]})}function R0(e,t){var r=Hi(e.x,t.x),n=Ui(e.x+e.width,t.x+t.width),i=Hi(e.y,t.y),a=Ui(e.y+e.height,t.y+t.height);if(n>=r&&a>=i)return{x:r,y:i,width:n-r,height:a-i}}function A0(e,t,r){var n=L({rectHover:!0},t),i=n.style={strokeNoScale:!0};if(r=r||{x:-1,y:-1,width:2,height:2},e)return e.indexOf("image://")===0?(i.image=e.slice(8),wt(i,r),new lr(n)):zs(e.replace("path://",""),n,r,"center")}function x0(e,t,r,n,i){for(var a=0,o=i[i.length-1];a<i.length;a++){var s=i[a];if(iv(e,t,r,n,s[0],s[1],o[0],o[1]))return!0;o=s}}function iv(e,t,r,n,i,a,o,s){var u=r-e,l=n-t,f=o-i,h=s-a,c=ro(f,h,u,l);if(E0(c))return!1;var v=e-i,d=t-a,_=ro(v,d,u,l)/c;if(_<0||_>1)return!1;var p=ro(v,d,f,h)/c;return!(p<0||p>1)}function ro(e,t,r,n){return e*n-r*t}function E0(e){return e<=1e-6&&e>=-1e-6}function L0(e){var t=e.itemTooltipOption,r=e.componentModel,n=e.itemName,i=H(t)?{formatter:t}:t,a=r.mainType,o=r.componentIndex,s={componentType:a,name:n,$vars:["name"]};s[a+"Index"]=o;var u=e.formatterParamsExtra;u&&P(it(u),function(f){Fr(s,f)||(s[f]=u[f],s.$vars.push(f))});var l=Gt(e.el);l.componentMainType=a,l.componentIndex=o,l.tooltipConfig={name:n,option:wt({content:n,encodeHTMLContent:!0,formatterParams:s},i)}}function Dl(e,t){var r;e.isGroup&&(r=t(e)),r||e.traverse(t)}function av(e,t){if(e)if(F(e))for(var r=0;r<e.length;r++)Dl(e[r],t);else Dl(e,t)}Qt("circle",ha);Qt("ellipse",Es);Qt("sector",Ls);Qt("ring",Is);Qt("polygon",Os);Qt("polyline",ks);Qt("rect",ue);Qt("line",va);Qt("bezierCurve",Fs);Qt("arc",ca);const G1=Object.freeze(Object.defineProperty({__proto__:null,Arc:ca,BezierCurve:Fs,BoundingRect:tt,Circle:ha,CompoundPath:a0,Ellipse:Es,Group:Hr,Image:lr,IncrementalDisplayable:f0,Line:va,LinearGradient:o0,OrientedBoundingRect:u0,Path:Q,Point:$,Polygon:Os,Polyline:ks,RadialGradient:s0,Rect:ue,Ring:Is,Sector:Ls,Text:zr,applyTransform:nv,clipPointsByRect:P0,clipRectByRect:R0,createIcon:A0,extendPath:_0,extendShape:p0,getShapeClass:y0,getTransform:b0,groupTransition:D0,initProps:c0,isElementRemoved:wn,lineLineIntersect:iv,linePolygonIntersect:x0,makeImage:tv,makePath:zs,mergePath:m0,registerShape:Qt,removeElement:jh,removeElementWithFadeOut:d0,resizePath:rv,setTooltipConfig:L0,subPixelOptimize:T0,subPixelOptimizeLine:w0,subPixelOptimizeRect:S0,transformDirection:C0,traverseElements:av,updateProps:Jh},Symbol.toStringTag,{value:"Module"}));var da={};function I0(e,t){for(var r=0;r<Le.length;r++){var n=Le[r],i=t[n],a=e.ensureState(n);a.style=a.style||{},a.style.text=i}var o=e.currentStates.slice();e.clearStates(!0),e.setStyle({text:t.normal}),e.useStates(o,!0)}function Pl(e,t,r){var n=e.labelFetcher,i=e.labelDataIndex,a=e.labelDimIndex,o=t.normal,s;n&&(s=n.getFormattedLabel(i,"normal",null,a,o&&o.get("formatter"),r!=null?{interpolatedValue:r}:null)),s==null&&(s=at(e.defaultText)?e.defaultText(i,e,r):e.defaultText);for(var u={normal:s},l=0;l<Le.length;l++){var f=Le[l],h=t[f];u[f]=Y(n?n.getFormattedLabel(i,f,null,a,h&&h.get("formatter")):null,s)}return u}function Y1(e,t,r,n){r=r||da;for(var i=e instanceof zr,a=!1,o=0;o<hl.length;o++){var s=t[hl[o]];if(s&&s.getShallow("show")){a=!0;break}}var u=i?e:e.getTextContent();if(a){i||(u||(u=new zr,e.setTextContent(u)),e.stateProxy&&(u.stateProxy=e.stateProxy));var l=Pl(r,t),f=t.normal,h=!!f.getShallow("show"),c=Rl(f,n&&n.normal,r,!1,!i);c.text=l.normal,i||e.setTextConfig(Al(f,r,!1));for(var o=0;o<Le.length;o++){var v=Le[o],s=t[v];if(s){var d=u.ensureState(v),_=!!Y(s.getShallow("show"),h);if(_!==h&&(d.ignore=!_),d.style=Rl(s,n&&n[v],r,!0,!i),d.style.text=l[v],!i){var p=e.ensureState(v);p.textConfig=Al(s,r,!0)}}}u.silent=!!f.getShallow("silent"),u.style.x!=null&&(c.x=u.style.x),u.style.y!=null&&(c.y=u.style.y),u.ignore=!h,u.useStyle(c),u.dirty(),r.enableTextSetter&&(ov(u).setLabelText=function(g){var y=Pl(r,t,g);I0(u,y)})}else u&&(u.ignore=!0);e.dirty()}function W1(e,t){t=t||"label";for(var r={normal:e.getModel(t)},n=0;n<Le.length;n++){var i=Le[n];r[i]=e.getModel([i,t])}return r}function Rl(e,t,r,n,i){var a={};return O0(a,e,r,n,i),t&&L(a,t),a}function Al(e,t,r){t=t||{};var n={},i,a=e.getShallow("rotate"),o=Y(e.getShallow("distance"),r?null:5),s=e.getShallow("offset");return i=e.getShallow("position")||(r?null:"inside"),i==="outside"&&(i=t.defaultOutsidePosition||"top"),i!=null&&(n.position=i),s!=null&&(n.offset=s),a!=null&&(a*=Math.PI/180,n.rotation=a),o!=null&&(n.distance=o),n.outsideFill=e.get("color")==="inherit"?t.inheritColor||null:"auto",n}function O0(e,t,r,n,i){r=r||da;var a=t.ecModel,o=a&&a.option.textStyle,s=k0(t),u;if(s){u={};for(var l in s)if(s.hasOwnProperty(l)){var f=t.getModel(["rich",l]);Il(u[l]={},f,o,r,n,i,!1,!0)}}u&&(e.rich=u);var h=t.get("overflow");h&&(e.overflow=h);var c=t.get("minMargin");c!=null&&(e.margin=c),Il(e,t,o,r,n,i,!0,!1)}function k0(e){for(var t;e&&e!==e.ecModel;){var r=(e.option||da).rich;if(r){t=t||{};for(var n=it(r),i=0;i<n.length;i++){var a=n[i];t[a]=1}}e=e.parentModel}return t}var xl=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],El=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Ll=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Il(e,t,r,n,i,a,o,s){r=!i&&r||da;var u=n&&n.inheritColor,l=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=Y(t.getShallow("opacity"),r.opacity);(l==="inherit"||l==="auto")&&(u?l=u:l=null),(f==="inherit"||f==="auto")&&(u?f=u:f=null),a||(l=l||r.color,f=f||r.textBorderColor),l!=null&&(e.fill=l),f!=null&&(e.stroke=f);var c=Y(t.getShallow("textBorderWidth"),r.textBorderWidth);c!=null&&(e.lineWidth=c);var v=Y(t.getShallow("textBorderType"),r.textBorderType);v!=null&&(e.lineDash=v);var d=Y(t.getShallow("textBorderDashOffset"),r.textBorderDashOffset);d!=null&&(e.lineDashOffset=d),!i&&h==null&&!s&&(h=n&&n.defaultOpacity),h!=null&&(e.opacity=h),!i&&!a&&e.fill==null&&n.inheritColor&&(e.fill=n.inheritColor);for(var _=0;_<xl.length;_++){var p=xl[_],g=Y(t.getShallow(p),r[p]);g!=null&&(e[p]=g)}for(var _=0;_<El.length;_++){var p=El[_],g=t.getShallow(p);g!=null&&(e[p]=g)}if(e.verticalAlign==null){var y=t.getShallow("baseline");y!=null&&(e.verticalAlign=y)}if(!o||!n.disableBox){for(var _=0;_<Ll.length;_++){var p=Ll[_],g=t.getShallow(p);g!=null&&(e[p]=g)}var m=t.getShallow("borderType");m!=null&&(e.borderDash=m),(e.backgroundColor==="auto"||e.backgroundColor==="inherit")&&u&&(e.backgroundColor=u),(e.borderColor==="auto"||e.borderColor==="inherit")&&u&&(e.borderColor=u)}}function F0(e,t){var r=t&&t.getModel("textStyle");return ce([e.fontStyle||r&&r.getShallow("fontStyle")||"",e.fontWeight||r&&r.getShallow("fontWeight")||"",(e.fontSize||r&&r.getShallow("fontSize")||12)+"px",e.fontFamily||r&&r.getShallow("fontFamily")||"sans-serif"].join(" "))}var ov=Ot();function X1(e,t,r,n){if(e){var i=ov(e);i.prevValue=i.value,i.value=r;var a=t.normal;i.valueAnimation=a.get("valueAnimation"),i.valueAnimation&&(i.precision=a.get("precision"),i.defaultInterpolatedText=n,i.statesModels=t)}}var B0=["textStyle","color"],no=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],io=new zr,z0=function(){function e(){}return e.prototype.getTextColor=function(t){var r=this.ecModel;return this.getShallow("color")||(!t&&r?r.get(B0):null)},e.prototype.getFont=function(){return F0({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},e.prototype.getTextRect=function(t){for(var r={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<no.length;n++)r[no[n]]=this.getShallow(no[n]);return io.useStyle(r),io.update(),io.getBoundingRect()},e}(),sv=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],N0=An(sv),H0=function(){function e(){}return e.prototype.getLineStyle=function(t){return N0(this,t)},e}(),uv=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],U0=An(uv),V0=function(){function e(){}return e.prototype.getItemStyle=function(t,r){return U0(this,t,r)},e}(),Mt=function(){function e(t,r,n){this.parentModel=r,this.ecModel=n,this.option=t}return e.prototype.init=function(t,r,n){},e.prototype.mergeOption=function(t,r){st(this.option,t,!0)},e.prototype.get=function(t,r){return t==null?this.option:this._doGet(this.parsePath(t),!r&&this.parentModel)},e.prototype.getShallow=function(t,r){var n=this.option,i=n==null?n:n[t];if(i==null&&!r){var a=this.parentModel;a&&(i=a.getShallow(t))}return i},e.prototype.getModel=function(t,r){var n=t!=null,i=n?this.parsePath(t):null,a=n?this._doGet(i):this.option;return r=r||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(i)),new e(a,r,this.ecModel)},e.prototype.isEmpty=function(){return this.option==null},e.prototype.restoreData=function(){},e.prototype.clone=function(){var t=this.constructor;return new t(W(this.option))},e.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},e.prototype.resolveParentPath=function(t){return t},e.prototype.isAnimationEnabled=function(){if(!U.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},e.prototype._doGet=function(t,r){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&!(t[i]&&(n=n&&typeof n=="object"?n[t[i]]:null,n==null));i++);return n==null&&r&&(n=r._doGet(this.resolveParentPath(t),r.parentModel)),n},e}();Ms(Mt);Sp(Mt);ge(Mt,H0);ge(Mt,V0);ge(Mt,Dp);ge(Mt,z0);var G0=Math.round(Math.random()*10);function pa(e){return[e||"",G0++].join("_")}function Y0(e){var t={};e.registerSubTypeDefaulter=function(r,n){var i=se(r);t[i.main]=n},e.determineSubType=function(r,n){var i=n.type;if(!i){var a=se(r).main;e.hasSubTypes(r)&&t[a]&&(i=t[a](n))}return i}}function W0(e,t){e.topologicalTravel=function(a,o,s,u){if(!a.length)return;var l=r(o),f=l.graph,h=l.noEntryList,c={};for(P(a,function(y){c[y]=!0});h.length;){var v=h.pop(),d=f[v],_=!!c[v];_&&(s.call(u,v,d.originalDeps.slice()),delete c[v]),P(d.successor,_?g:p)}P(c,function(){var y="";throw new Error(y)});function p(y){f[y].entryCount--,f[y].entryCount===0&&h.push(y)}function g(y){c[y]=!0,p(y)}};function r(a){var o={},s=[];return P(a,function(u){var l=n(o,u),f=l.originalDeps=t(u),h=i(f,a);l.entryCount=h.length,l.entryCount===0&&s.push(u),P(h,function(c){nt(l.predecessor,c)<0&&l.predecessor.push(c);var v=n(o,c);nt(v.successor,c)<0&&v.successor.push(u)})}),{graph:o,noEntryList:s}}function n(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function i(a,o){var s=[];return P(a,function(u){nt(o,u)>=0&&s.push(u)}),s}}function q1(e,t){return st(st({},e,!0),t,!0)}const X0={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},q0={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var Vi="ZH",Ns="EN",kr=Ns,xi={},Hs={},lv=U.domSupported?function(){var e=(document.documentElement.lang||navigator.language||navigator.browserLanguage||kr).toUpperCase();return e.indexOf(Vi)>-1?Vi:kr}():kr;function fv(e,t){e=e.toUpperCase(),Hs[e]=new Mt(t),xi[e]=t}function $0(e){if(H(e)){var t=xi[e.toUpperCase()]||{};return e===Vi||e===Ns?W(t):st(W(t),W(xi[kr]),!1)}else return st(W(e),W(xi[kr]),!1)}function Z0(e){return Hs[e]}function K0(){return Hs[kr]}fv(Ns,X0);fv(Vi,q0);var Q0=1e3,J0=Q0*60,j0=J0*60,t_=j0*24,$1=t_*365,fn={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},si="{yyyy}-{MM}-{dd}",Z1={year:"{yyyy}",month:"{yyyy}-{MM}",day:si,hour:si+" "+fn.hour,minute:si+" "+fn.minute,second:si+" "+fn.second,millisecond:fn.none},ao=["year","month","day","hour","minute","second","millisecond"],e_=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Te(e,t){return e+="","0000".substr(0,t-e.length)+e}function r_(e){switch(e){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return e}}function K1(e){return e===r_(e)}function Q1(e){switch(e){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function hv(e,t,r,n){var i=Ur(e),a=i[cv(r)](),o=i[Sn(r)]()+1,s=Math.floor((o-1)/3)+1,u=i[Us(r)](),l=i["get"+(r?"UTC":"")+"Day"](),f=i[Gi(r)](),h=(f-1)%12+1,c=i[Vs(r)](),v=i[Gs(r)](),d=i[Ys(r)](),_=f>=12?"pm":"am",p=_.toUpperCase(),g=n instanceof Mt?n:Z0(n||lv)||K0(),y=g.getModel("time"),m=y.get("month"),w=y.get("monthAbbr"),T=y.get("dayOfWeek"),S=y.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,_+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,Te(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,m[o-1]).replace(/{MMM}/g,w[o-1]).replace(/{MM}/g,Te(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Te(u,2)).replace(/{d}/g,u+"").replace(/{eeee}/g,T[l]).replace(/{ee}/g,S[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Te(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,Te(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Te(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Te(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,Te(d,3)).replace(/{S}/g,d+"")}function J1(e,t,r,n,i){var a=null;if(H(r))a=r;else if(at(r))a=r(e.value,t,{level:e.level});else{var o=L({},fn);if(e.level>0)for(var s=0;s<ao.length;++s)o[ao[s]]="{primary|"+o[ao[s]]+"}";var u=r?r.inherit===!1?r:wt(r,o):o,l=vv(e.value,i);if(u[l])a=u[l];else if(u.inherit){for(var f=e_.indexOf(l),s=f-1;s>=0;--s)if(u[l]){a=u[l];break}a=a||o.none}if(F(a)){var h=e.level==null?0:e.level>=0?e.level:a.length+e.level;h=Math.min(h,a.length-1),a=a[h]}}return hv(new Date(e.value),a,i,n)}function vv(e,t){var r=Ur(e),n=r[Sn(t)]()+1,i=r[Us(t)](),a=r[Gi(t)](),o=r[Vs(t)](),s=r[Gs(t)](),u=r[Ys(t)](),l=u===0,f=l&&s===0,h=f&&o===0,c=h&&a===0,v=c&&i===1,d=v&&n===1;return d?"year":v?"month":c?"day":h?"hour":f?"minute":l?"second":"millisecond"}function j1(e,t,r){var n=ut(e)?Ur(e):e;switch(t=t||vv(e,r),t){case"year":return n[cv(r)]();case"half-year":return n[Sn(r)]()>=6?1:0;case"quarter":return Math.floor((n[Sn(r)]()+1)/4);case"month":return n[Sn(r)]();case"day":return n[Us(r)]();case"half-day":return n[Gi(r)]()/24;case"hour":return n[Gi(r)]();case"minute":return n[Vs(r)]();case"second":return n[Gs(r)]();case"millisecond":return n[Ys(r)]()}}function cv(e){return e?"getUTCFullYear":"getFullYear"}function Sn(e){return e?"getUTCMonth":"getMonth"}function Us(e){return e?"getUTCDate":"getDate"}function Gi(e){return e?"getUTCHours":"getHours"}function Vs(e){return e?"getUTCMinutes":"getMinutes"}function Gs(e){return e?"getUTCSeconds":"getSeconds"}function Ys(e){return e?"getUTCMilliseconds":"getMilliseconds"}function tw(e){return e?"setUTCFullYear":"setFullYear"}function ew(e){return e?"setUTCMonth":"setMonth"}function rw(e){return e?"setUTCDate":"setDate"}function nw(e){return e?"setUTCHours":"setHours"}function iw(e){return e?"setUTCMinutes":"setMinutes"}function aw(e){return e?"setUTCSeconds":"setSeconds"}function ow(e){return e?"setUTCMilliseconds":"setMilliseconds"}function n_(e){if(!np(e))return H(e)?e:"-";var t=(e+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function sw(e,t){return e=(e||"").toLowerCase().replace(/-(.)/g,function(r,n){return n.toUpperCase()}),t&&e&&(e=e.charAt(0).toUpperCase()+e.slice(1)),e}var i_=qf;function ts(e,t,r){var n="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function i(f){return f&&ce(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=e instanceof Date;if(o||s){var u=o?Ur(e):e;if(isNaN(+u)){if(s)return"-"}else return hv(u,n,r)}if(t==="ordinal")return Co(e)?i(e):ut(e)&&a(e)?e+"":"-";var l=zi(e);return a(l)?n_(l):Co(e)?i(e):typeof e=="boolean"?e+"":"-"}var Ol=["a","b","c","d","e","f","g"],oo=function(e,t){return"{"+e+(t??"")+"}"};function a_(e,t,r){F(t)||(t=[t]);var n=t.length;if(!n)return"";for(var i=t[0].$vars||[],a=0;a<i.length;a++){var o=Ol[a];e=e.replace(oo(o),oo(o,0))}for(var s=0;s<n;s++)for(var u=0;u<i.length;u++){var l=t[s][i[u]];e=e.replace(oo(Ol[u],s),r?At(l):l)}return e}function o_(e,t){var r=H(e)?{color:e,extraCssText:t}:e||{},n=r.color,i=r.type;t=r.extraCssText;var a=r.renderMode||"html";if(!n)return"";if(a==="html")return i==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+At(n)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+At(n)+";"+(t||"")+'"></span>';var o=r.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:i==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}}function s_(e,t){return t=t||"transparent",H(e)?e:B(e)&&e.colorStops&&(e.colorStops[0]||{}).color||t}function uw(e,t){if(t==="_blank"||t==="blank"){var r=window.open();r.opener=null,r.location.href=e}else window.open(e,t)}var Ei=P,u_=["left","right","top","bottom","width","height"],ui=[["width","left","right"],["height","top","bottom"]];function Ws(e,t,r,n,i){var a=0,o=0;n==null&&(n=1/0),i==null&&(i=1/0);var s=0;t.eachChild(function(u,l){var f=u.getBoundingRect(),h=t.childAt(l+1),c=h&&h.getBoundingRect(),v,d;if(e==="horizontal"){var _=f.width+(c?-c.x+f.x:0);v=a+_,v>n||u.newline?(a=0,v=_,o+=s+r,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(c?-c.y+f.y:0);d=o+p,d>i||u.newline?(a+=s+r,o=0,d=p,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=a,u.y=o,u.markRedraw(),e==="horizontal"?a=v+r:o=d+r)})}var lw=Ws;_s(Ws,"vertical");_s(Ws,"horizontal");function fw(e,t,r){r=i_(r||0);var n=t.width,i=t.height,a=De(e.left,n),o=De(e.top,i),s=De(e.right,n),u=De(e.bottom,i),l=De(e.width,n),f=De(e.height,i),h=r[2]+r[0],c=r[1]+r[3],v=e.aspect;switch(isNaN(l)&&(l=n-s-c-a),isNaN(f)&&(f=i-u-h-o),v!=null&&(isNaN(l)&&isNaN(f)&&(v>n/i?l=n*.8:f=i*.8),isNaN(l)&&(l=v*f),isNaN(f)&&(f=l/v)),isNaN(a)&&(a=n-s-l-c),isNaN(o)&&(o=i-u-f-h),e.left||e.right){case"center":a=n/2-l/2-r[3];break;case"right":a=n-l-c;break}switch(e.top||e.bottom){case"middle":case"center":o=i/2-f/2-r[0];break;case"bottom":o=i-f-h;break}a=a||0,o=o||0,isNaN(l)&&(l=n-c-a-(s||0)),isNaN(f)&&(f=i-h-o-(u||0));var d=new tt(a+r[3],o+r[0],l,f);return d.margin=r,d}function Yi(e){var t=e.layoutMode||e.constructor.layoutMode;return B(t)?t:t?{type:t}:null}function Wi(e,t,r){var n=r&&r.ignoreSize;!F(n)&&(n=[n,n]);var i=o(ui[0],0),a=o(ui[1],1);l(ui[0],e,i),l(ui[1],e,a);function o(f,h){var c={},v=0,d={},_=0,p=2;if(Ei(f,function(m){d[m]=e[m]}),Ei(f,function(m){s(t,m)&&(c[m]=d[m]=t[m]),u(c,m)&&v++,u(d,m)&&_++}),n[h])return u(t,f[1])?d[f[2]]=null:u(t,f[2])&&(d[f[1]]=null),d;if(_===p||!v)return d;if(v>=p)return c;for(var g=0;g<f.length;g++){var y=f[g];if(!s(c,y)&&s(e,y)){c[y]=e[y];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function u(f,h){return f[h]!=null&&f[h]!=="auto"}function l(f,h,c){Ei(f,function(v){h[v]=c[v]})}}function dv(e){return l_({},e)}function l_(e,t){return t&&e&&Ei(u_,function(r){t.hasOwnProperty(r)&&(e[r]=t[r])}),e}var f_=Ot(),rt=function(e){N(t,e);function t(r,n,i){var a=e.call(this,r,n,i)||this;return a.uid=pa("ec_cpt_model"),a}return t.prototype.init=function(r,n,i){this.mergeDefaultAndTheme(r,i)},t.prototype.mergeDefaultAndTheme=function(r,n){var i=Yi(this),a=i?dv(r):{},o=n.getTheme();st(r,o.get(this.mainType)),st(r,this.getDefaultOption()),i&&Wi(r,a,i)},t.prototype.mergeOption=function(r,n){st(this.option,r,!0);var i=Yi(this);i&&Wi(this.option,r,i)},t.prototype.optionUpdated=function(r,n){},t.prototype.getDefaultOption=function(){var r=this.constructor;if(!yp(r))return r.defaultOption;var n=f_(this);if(!n.defaultOption){for(var i=[],a=r;a;){var o=a.prototype.defaultOption;o&&i.push(o),a=a.superClass}for(var s={},u=i.length-1;u>=0;u--)s=st(s,i[u],!0);n.defaultOption=s}return n.defaultOption},t.prototype.getReferringComponents=function(r,n){var i=r+"Index",a=r+"Id";return aa(this.ecModel,r,{index:this.get(i,!0),id:this.get(a,!0)},n)},t.prototype.getBoxLayoutParams=function(){var r=this;return{left:r.get("left"),top:r.get("top"),right:r.get("right"),bottom:r.get("bottom"),width:r.get("width"),height:r.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(r){this.option.zlevel=r},t.protoInitialize=function(){var r=t.prototype;r.type="component",r.id="",r.name="",r.mainType="",r.subType="",r.componentIndex=0}(),t}(Mt);Ph(rt,Mt);Ds(rt);Y0(rt);W0(rt,h_);function h_(e){var t=[];return P(rt.getClassesByMainType(e),function(r){t=t.concat(r.dependencies||r.prototype.dependencies||[])}),t=X(t,function(r){return se(r).main}),e!=="dataset"&&nt(t,"dataset")<=0&&t.unshift("dataset"),t}var pv="";typeof navigator<"u"&&(pv=navigator.platform||"");var wr="rgba(0, 0, 0, 0.2)";const v_={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:wr,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:wr,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:wr,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:wr,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:wr,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:wr,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:pv.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var hw=Z(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),me="original",Pt="arrayRows",Jt="objectRows",le="keyedColumns",or="typedArray",gv="unknown",de="column",Vr="row",ct={Must:1,Might:2,Not:3},_v=Ot();function c_(e){_v(e).datasetMap=Z()}function vw(e,t,r){var n={},i=Xs(t);if(!i||!e)return n;var a=[],o=[],s=t.ecModel,u=_v(s).datasetMap,l=i.uid+"_"+r.seriesLayoutBy,f,h;e=e.slice(),P(e,function(_,p){var g=B(_)?_:e[p]={name:_};g.type==="ordinal"&&f==null&&(f=p,h=d(g)),n[g.name]=[]});var c=u.get(l)||u.set(l,{categoryWayDim:h,valueWayDim:0});P(e,function(_,p){var g=_.name,y=d(_);if(f==null){var m=c.valueWayDim;v(n[g],m,y),v(o,m,y),c.valueWayDim+=y}else if(f===p)v(n[g],0,y),v(a,0,y);else{var m=c.categoryWayDim;v(n[g],m,y),v(o,m,y),c.categoryWayDim+=y}});function v(_,p,g){for(var y=0;y<g;y++)_.push(p+y)}function d(_){var p=_.dimsDef;return p?p.length:1}return a.length&&(n.itemName=a),o.length&&(n.seriesName=o),n}function cw(e,t,r){var n={},i=Xs(e);if(!i)return n;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===Jt||a===le)&&P(o,function(f,h){(B(f)?f.name:f)==="name"&&(s=h)});var u=function(){for(var f={},h={},c=[],v=0,d=Math.min(5,r);v<d;v++){var _=yv(t.data,a,t.seriesLayoutBy,o,t.startIndex,v);c.push(_);var p=_===ct.Not;if(p&&f.v==null&&v!==s&&(f.v=v),(f.n==null||f.n===f.v||!p&&c[f.n]===ct.Not)&&(f.n=v),g(f)&&c[f.n]!==ct.Not)return f;p||(_===ct.Might&&h.v==null&&v!==s&&(h.v=v),(h.n==null||h.n===h.v)&&(h.n=v))}function g(y){return y.v!=null&&y.n!=null}return g(f)?f:g(h)?h:null}();if(u){n.value=[u.v];var l=s??u.n;n.itemName=[l],n.seriesName=[l]}return n}function Xs(e){var t=e.get("data",!0);if(!t)return aa(e.ecModel,"dataset",{index:e.get("datasetIndex",!0),id:e.get("datasetId",!0)},Cs).models[0]}function d_(e){return!e.get("transform",!0)&&!e.get("fromTransformResult",!0)?[]:aa(e.ecModel,"dataset",{index:e.get("fromDatasetIndex",!0),id:e.get("fromDatasetId",!0)},Cs).models}function p_(e,t){return yv(e.data,e.sourceFormat,e.seriesLayoutBy,e.dimensionsDefine,e.startIndex,t)}function yv(e,t,r,n,i,a){var o,s=5;if(Dt(e))return ct.Not;var u,l;if(n){var f=n[a];B(f)?(u=f.name,l=f.type):H(f)&&(u=f)}if(l!=null)return l==="ordinal"?ct.Must:ct.Not;if(t===Pt){var h=e;if(r===Vr){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=w(c[i+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var d=h[i+v];if(d&&(o=w(d[a]))!=null)return o}}else if(t===Jt){var _=e;if(!u)return ct.Not;for(var v=0;v<_.length&&v<s;v++){var p=_[v];if(p&&(o=w(p[u]))!=null)return o}}else if(t===le){var g=e;if(!u)return ct.Not;var c=g[u];if(!c||Dt(c))return ct.Not;for(var v=0;v<c.length&&v<s;v++)if((o=w(c[v]))!=null)return o}else if(t===me)for(var y=e,v=0;v<y.length&&v<s;v++){var p=y[v],m=na(p);if(!F(m))return ct.Not;if((o=w(m[a]))!=null)return o}function w(T){var S=H(T);if(T!=null&&Number.isFinite(Number(T))&&T!=="")return S?ct.Might:ct.Not;if(S&&T!=="-")return ct.Must}return ct.Not}var g_=Z();function __(e,t,r){var n=g_.get(t);if(!n)return r;var i=n(e);return i?r.concat(i):r}var kl=Ot();Ot();var qs=function(){function e(){}return e.prototype.getColorFromPalette=function(t,r,n){var i=Lt(this.get("color",!0)),a=this.get("colorLayer",!0);return m_(this,kl,i,a,t,r,n)},e.prototype.clearColorPalette=function(){w_(this,kl)},e}();function y_(e,t){for(var r=e.length,n=0;n<r;n++)if(e[n].length>t)return e[n];return e[r-1]}function m_(e,t,r,n,i,a,o){a=a||e;var s=t(a),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(i))return l[i];var f=o==null||!n?r:y_(n,o);if(f=f||r,!(!f||!f.length)){var h=f[u];return i&&(l[i]=h),s.paletteIdx=(u+1)%f.length,h}}function w_(e,t){t(e).paletteIdx=0,t(e).paletteNameMap={}}var li,Jr,Fl,Bl="\0_ec_inner",S_=1,$s=function(e){N(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.init=function(r,n,i,a,o,s){a=a||{},this.option=null,this._theme=new Mt(a),this._locale=new Mt(o),this._optionManager=s},t.prototype.setOption=function(r,n,i){var a=Hl(n);this._optionManager.setOption(r,i,a),this._resetOption(null,a)},t.prototype.resetOption=function(r,n){return this._resetOption(r,Hl(n))},t.prototype._resetOption=function(r,n){var i=!1,a=this._optionManager;if(!r||r==="recreate"){var o=a.mountOption(r==="recreate");!this.option||r==="recreate"?Fl(this,o):(this.restoreData(),this._mergeOption(o,n)),i=!0}if((r==="timeline"||r==="media")&&this.restoreData(),!r||r==="recreate"||r==="timeline"){var s=a.getTimelineOption(this);s&&(i=!0,this._mergeOption(s,n))}if(!r||r==="recreate"||r==="media"){var u=a.getMediaOption(this);u.length&&P(u,function(l){i=!0,this._mergeOption(l,n)},this)}return i},t.prototype.mergeOption=function(r){this._mergeOption(r,null)},t.prototype._mergeOption=function(r,n){var i=this.option,a=this._componentsMap,o=this._componentsCount,s=[],u=Z(),l=n&&n.replaceMergeMainTypeMap;c_(this),P(r,function(h,c){h!=null&&(rt.hasClass(c)?c&&(s.push(c),u.set(c,!0)):i[c]=i[c]==null?W(h):st(i[c],h,!0))}),l&&l.each(function(h,c){rt.hasClass(c)&&!u.get(c)&&(s.push(c),u.set(c,!0))}),rt.topologicalTravel(s,rt.getAllClassMainTypes(),f,this);function f(h){var c=__(this,h,Lt(r[h])),v=a.get(h),d=v?l&&l.get(h)?"replaceMerge":"normalMerge":"replaceAll",_=ap(v,c,d);vp(_,h,rt),i[h]=null,a.set(h,null),o.set(h,0);var p=[],g=[],y=0,m;P(_,function(w,T){var S=w.existing,b=w.newOption;if(!b)S&&(S.mergeOption({},this),S.optionUpdated({},!1));else{var C=h==="series",M=rt.getClass(h,w.keyInfo.subType,!C);if(!M)return;if(h==="tooltip"){if(m)return;m=!0}if(S&&S.constructor===M)S.name=w.keyInfo.name,S.mergeOption(b,this),S.optionUpdated(b,!1);else{var A=L({componentIndex:T},w.keyInfo);S=new M(b,this,this,A),L(S,A),w.brandNew&&(S.__requireNewView=!0),S.init(b,this,this),S.optionUpdated(null,!0)}}S?(p.push(S.option),g.push(S),y++):(p.push(void 0),g.push(void 0))},this),i[h]=p,a.set(h,g),o.set(h,y),h==="series"&&li(this)}this._seriesIndices||li(this)},t.prototype.getOption=function(){var r=W(this.option);return P(r,function(n,i){if(rt.hasClass(i)){for(var a=Lt(n),o=a.length,s=!1,u=o-1;u>=0;u--)a[u]&&!Rn(a[u])?s=!0:(a[u]=null,!s&&o--);a.length=o,r[i]=a}}),delete r[Bl],r},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(r){this._payload=r},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(r,n){var i=this._componentsMap.get(r);if(i){var a=i[n||0];if(a)return a;if(n==null){for(var o=0;o<i.length;o++)if(i[o])return i[o]}}},t.prototype.queryComponents=function(r){var n=r.mainType;if(!n)return[];var i=r.index,a=r.id,o=r.name,s=this._componentsMap.get(n);if(!s||!s.length)return[];var u;return i!=null?(u=[],P(Lt(i),function(l){s[l]&&u.push(s[l])})):a!=null?u=zl("id",a,s):o!=null?u=zl("name",o,s):u=Kt(s,function(l){return!!l}),Nl(u,r)},t.prototype.findComponents=function(r){var n=r.query,i=r.mainType,a=s(n),o=a?this.queryComponents(a):Kt(this._componentsMap.get(i),function(l){return!!l});return u(Nl(o,r));function s(l){var f=i+"Index",h=i+"Id",c=i+"Name";return l&&(l[f]!=null||l[h]!=null||l[c]!=null)?{mainType:i,index:l[f],id:l[h],name:l[c]}:null}function u(l){return r.filter?Kt(l,r.filter):l}},t.prototype.eachComponent=function(r,n,i){var a=this._componentsMap;if(at(r)){var o=n,s=r;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var d=h[v];d&&s.call(o,c,d,d.componentIndex)}})}else for(var u=H(r)?a.get(r):B(r)?this.findComponents(r):null,l=0;u&&l<u.length;l++){var f=u[l];f&&n.call(i,f,f.componentIndex)}},t.prototype.getSeriesByName=function(r){var n=ur(r,null);return Kt(this._componentsMap.get("series"),function(i){return!!i&&n!=null&&i.name===n})},t.prototype.getSeriesByIndex=function(r){return this._componentsMap.get("series")[r]},t.prototype.getSeriesByType=function(r){return Kt(this._componentsMap.get("series"),function(n){return!!n&&n.subType===r})},t.prototype.getSeries=function(){return Kt(this._componentsMap.get("series"),function(r){return!!r})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(r,n){Jr(this),P(this._seriesIndices,function(i){var a=this._componentsMap.get("series")[i];r.call(n,a,i)},this)},t.prototype.eachRawSeries=function(r,n){P(this._componentsMap.get("series"),function(i){i&&r.call(n,i,i.componentIndex)})},t.prototype.eachSeriesByType=function(r,n,i){Jr(this),P(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===r&&n.call(i,o,a)},this)},t.prototype.eachRawSeriesByType=function(r,n,i){return P(this.getSeriesByType(r),n,i)},t.prototype.isSeriesFiltered=function(r){return Jr(this),this._seriesIndicesMap.get(r.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(r,n){Jr(this);var i=[];P(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];r.call(n,o,a)&&i.push(a)},this),this._seriesIndices=i,this._seriesIndicesMap=Z(i)},t.prototype.restoreData=function(r){li(this);var n=this._componentsMap,i=[];n.each(function(a,o){rt.hasClass(o)&&i.push(o)}),rt.topologicalTravel(i,rt.getAllClassMainTypes(),function(a){P(n.get(a),function(o){o&&(a!=="series"||!T_(o,r))&&o.restoreData()})})},t.internalField=function(){li=function(r){var n=r._seriesIndices=[];P(r._componentsMap.get("series"),function(i){i&&n.push(i.componentIndex)}),r._seriesIndicesMap=Z(n)},Jr=function(r){},Fl=function(r,n){r.option={},r.option[Bl]=S_,r._componentsMap=Z({series:[]}),r._componentsCount=Z();var i=n.aria;B(i)&&i.enabled==null&&(i.enabled=!0),b_(n,r._theme.option),st(n,v_,!1),r._mergeOption(n,null)}}(),t}(Mt);function T_(e,t){if(t){var r=t.seriesIndex,n=t.seriesId,i=t.seriesName;return r!=null&&e.componentIndex!==r||n!=null&&e.id!==n||i!=null&&e.name!==i}}function b_(e,t){var r=e.color&&!e.colorLayer;P(t,function(n,i){i==="colorLayer"&&r||rt.hasClass(i)||(typeof n=="object"?e[i]=e[i]?st(e[i],n,!1):W(n):e[i]==null&&(e[i]=n))})}function zl(e,t,r){if(F(t)){var n=Z();return P(t,function(a){if(a!=null){var o=ur(a,null);o!=null&&n.set(a,!0)}}),Kt(r,function(a){return a&&n.get(a[e])})}else{var i=ur(t,null);return Kt(r,function(a){return a&&i!=null&&a[e]===i})}}function Nl(e,t){return t.hasOwnProperty("subType")?Kt(e,function(r){return r&&r.subType===t.subType}):e}function Hl(e){var t=Z();return e&&P(Lt(e.replaceMerge),function(r){t.set(r,!0)}),{replaceMergeMainTypeMap:t}}ge($s,qs);var C_=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],mv=function(){function e(t){P(C_,function(r){this[r]=ie(t[r],t)},this)}return e}(),so={},wv=function(){function e(){this._coordinateSystems=[]}return e.prototype.create=function(t,r){var n=[];P(so,function(i,a){var o=i.create(t,r);n=n.concat(o||[])}),this._coordinateSystems=n},e.prototype.update=function(t,r){P(this._coordinateSystems,function(n){n.update&&n.update(t,r)})},e.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},e.register=function(t,r){so[t]=r},e.get=function(t){return so[t]},e}(),M_=/^(min|max)?(.+)$/,D_=function(){function e(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return e.prototype.setOption=function(t,r,n){t&&(P(Lt(t.series),function(o){o&&o.data&&Dt(o.data)&&Do(o.data)}),P(Lt(t.dataset),function(o){o&&o.source&&Dt(o.source)&&Do(o.source)})),t=W(t);var i=this._optionBackup,a=P_(t,r,!i);this._newBaseOption=a.baseOption,i?(a.timelineOptions.length&&(i.timelineOptions=a.timelineOptions),a.mediaList.length&&(i.mediaList=a.mediaList),a.mediaDefault&&(i.mediaDefault=a.mediaDefault)):this._optionBackup=a},e.prototype.mountOption=function(t){var r=this._optionBackup;return this._timelineOptions=r.timelineOptions,this._mediaList=r.mediaList,this._mediaDefault=r.mediaDefault,this._currentMediaIndices=[],W(t?r.baseOption:this._newBaseOption)},e.prototype.getTimelineOption=function(t){var r,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(r=W(n[i.getCurrentIndex()]))}return r},e.prototype.getMediaOption=function(t){var r=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!i.length&&!a)return s;for(var u=0,l=i.length;u<l;u++)R_(i[u].query,r,n)&&o.push(u);return!o.length&&a&&(o=[-1]),o.length&&!x_(o,this._currentMediaIndices)&&(s=X(o,function(f){return W(f===-1?a.option:i[f].option)})),this._currentMediaIndices=o,s},e}();function P_(e,t,r){var n=[],i,a,o=e.baseOption,s=e.timeline,u=e.options,l=e.media,f=!!e.media,h=!!(u||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(e.options=e.media=null),a=e),f&&F(l)&&P(l,function(v){v&&v.option&&(v.query?n.push(v):i||(i=v))}),c(a),P(u,function(v){return c(v)}),P(n,function(v){return c(v.option)});function c(v){P(t,function(d){d(v,r)})}return{baseOption:a,timelineOptions:u||[],mediaDefault:i,mediaList:n}}function R_(e,t,r){var n={width:t,height:r,aspectratio:t/r},i=!0;return P(e,function(a,o){var s=o.match(M_);if(!(!s||!s[1]||!s[2])){var u=s[1],l=s[2].toLowerCase();A_(n[l],a,u)||(i=!1)}}),i}function A_(e,t,r){return r==="min"?e>=t:r==="max"?e<=t:e===t}function x_(e,t){return e.join(",")===t.join(",")}var Wt=P,xn=B,Ul=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function uo(e){var t=e&&e.itemStyle;if(t)for(var r=0,n=Ul.length;r<n;r++){var i=Ul[r],a=t.normal,o=t.emphasis;a&&a[i]&&(e[i]=e[i]||{},e[i].normal?st(e[i].normal,a[i]):e[i].normal=a[i],a[i]=null),o&&o[i]&&(e[i]=e[i]||{},e[i].emphasis?st(e[i].emphasis,o[i]):e[i].emphasis=o[i],o[i]=null)}}function dt(e,t,r){if(e&&e[t]&&(e[t].normal||e[t].emphasis)){var n=e[t].normal,i=e[t].emphasis;n&&(r?(e[t].normal=e[t].emphasis=null,wt(e[t],n)):e[t]=n),i&&(e.emphasis=e.emphasis||{},e.emphasis[t]=i,i.focus&&(e.emphasis.focus=i.focus),i.blurScope&&(e.emphasis.blurScope=i.blurScope))}}function hn(e){dt(e,"itemStyle"),dt(e,"lineStyle"),dt(e,"areaStyle"),dt(e,"label"),dt(e,"labelLine"),dt(e,"upperLabel"),dt(e,"edgeLabel")}function et(e,t){var r=xn(e)&&e[t],n=xn(r)&&r.textStyle;if(n)for(var i=0,a=Xu.length;i<a;i++){var o=Xu[i];n.hasOwnProperty(o)&&(r[o]=n[o])}}function Bt(e){e&&(hn(e),et(e,"label"),e.emphasis&&et(e.emphasis,"label"))}function E_(e){if(xn(e)){uo(e),hn(e),et(e,"label"),et(e,"upperLabel"),et(e,"edgeLabel"),e.emphasis&&(et(e.emphasis,"label"),et(e.emphasis,"upperLabel"),et(e.emphasis,"edgeLabel"));var t=e.markPoint;t&&(uo(t),Bt(t));var r=e.markLine;r&&(uo(r),Bt(r));var n=e.markArea;n&&Bt(n);var i=e.data;if(e.type==="graph"){i=i||e.nodes;var a=e.links||e.edges;if(a&&!Dt(a))for(var o=0;o<a.length;o++)Bt(a[o]);P(e.categories,function(l){hn(l)})}if(i&&!Dt(i))for(var o=0;o<i.length;o++)Bt(i[o]);if(t=e.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)Bt(s[o]);if(r=e.markLine,r&&r.data)for(var u=r.data,o=0;o<u.length;o++)F(u[o])?(Bt(u[o][0]),Bt(u[o][1])):Bt(u[o]);e.type==="gauge"?(et(e,"axisLabel"),et(e,"title"),et(e,"detail")):e.type==="treemap"?(dt(e.breadcrumb,"itemStyle"),P(e.levels,function(l){hn(l)})):e.type==="tree"&&hn(e.leaves)}}function he(e){return F(e)?e:e?[e]:[]}function Vl(e){return(F(e)?e[0]:e)||{}}function L_(e,t){Wt(he(e.series),function(n){xn(n)&&E_(n)});var r=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&r.push("valueAxis","categoryAxis","logAxis","timeAxis"),Wt(r,function(n){Wt(he(e[n]),function(i){i&&(et(i,"axisLabel"),et(i.axisPointer,"label"))})}),Wt(he(e.parallel),function(n){var i=n&&n.parallelAxisDefault;et(i,"axisLabel"),et(i&&i.axisPointer,"label")}),Wt(he(e.calendar),function(n){dt(n,"itemStyle"),et(n,"dayLabel"),et(n,"monthLabel"),et(n,"yearLabel")}),Wt(he(e.radar),function(n){et(n,"name"),n.name&&n.axisName==null&&(n.axisName=n.name,delete n.name),n.nameGap!=null&&n.axisNameGap==null&&(n.axisNameGap=n.nameGap,delete n.nameGap)}),Wt(he(e.geo),function(n){xn(n)&&(Bt(n),Wt(he(n.regions),function(i){Bt(i)}))}),Wt(he(e.timeline),function(n){Bt(n),dt(n,"label"),dt(n,"itemStyle"),dt(n,"controlStyle",!0);var i=n.data;F(i)&&P(i,function(a){B(a)&&(dt(a,"label"),dt(a,"itemStyle"))})}),Wt(he(e.toolbox),function(n){dt(n,"iconStyle"),Wt(n.feature,function(i){dt(i,"iconStyle")})}),et(Vl(e.axisPointer),"label"),et(Vl(e.tooltip).axisPointer,"label")}function I_(e,t){for(var r=t.split(","),n=e,i=0;i<r.length&&(n=n&&n[r[i]],n!=null);i++);return n}function O_(e,t,r,n){for(var i=t.split(","),a=e,o,s=0;s<i.length-1;s++)o=i[s],a[o]==null&&(a[o]={}),a=a[o];a[i[s]]==null&&(a[i[s]]=r)}function Gl(e){e&&P(k_,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var k_=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],F_=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],lo=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function jr(e){var t=e&&e.itemStyle;if(t)for(var r=0;r<lo.length;r++){var n=lo[r][1],i=lo[r][0];t[n]!=null&&(t[i]=t[n])}}function Yl(e){e&&e.alignTo==="edge"&&e.margin!=null&&e.edgeDistance==null&&(e.edgeDistance=e.margin)}function Wl(e){e&&e.downplay&&!e.blur&&(e.blur=e.downplay)}function B_(e){e&&e.focusNodeAdjacency!=null&&(e.emphasis=e.emphasis||{},e.emphasis.focus==null&&(e.emphasis.focus="adjacency"))}function Sv(e,t){if(e)for(var r=0;r<e.length;r++)t(e[r]),e[r]&&Sv(e[r].children,t)}function Tv(e,t){L_(e,t),e.series=Lt(e.series),P(e.series,function(r){if(B(r)){var n=r.type;if(n==="line")r.clipOverflow!=null&&(r.clip=r.clipOverflow);else if(n==="pie"||n==="gauge"){r.clockWise!=null&&(r.clockwise=r.clockWise),Yl(r.label);var i=r.data;if(i&&!Dt(i))for(var a=0;a<i.length;a++)Yl(i[a]);r.hoverOffset!=null&&(r.emphasis=r.emphasis||{},(r.emphasis.scaleSize=null)&&(r.emphasis.scaleSize=r.hoverOffset))}else if(n==="gauge"){var o=I_(r,"pointer.color");o!=null&&O_(r,"itemStyle.color",o)}else if(n==="bar"){jr(r),jr(r.backgroundStyle),jr(r.emphasis);var i=r.data;if(i&&!Dt(i))for(var a=0;a<i.length;a++)typeof i[a]=="object"&&(jr(i[a]),jr(i[a]&&i[a].emphasis))}else if(n==="sunburst"){var s=r.highlightPolicy;s&&(r.emphasis=r.emphasis||{},r.emphasis.focus||(r.emphasis.focus=s)),Wl(r),Sv(r.data,Wl)}else n==="graph"||n==="sankey"?B_(r):n==="map"&&(r.mapType&&!r.map&&(r.map=r.mapType),r.mapLocation&&wt(r,r.mapLocation));r.hoverAnimation!=null&&(r.emphasis=r.emphasis||{},r.emphasis&&r.emphasis.scale==null&&(r.emphasis.scale=r.hoverAnimation)),Gl(r)}}),e.dataRange&&(e.visualMap=e.dataRange),P(F_,function(r){var n=e[r];n&&(F(n)||(n=[n]),P(n,function(i){Gl(i)}))})}function z_(e){var t=Z();e.eachSeries(function(r){var n=r.get("stack");if(n){var i=t.get(n)||t.set(n,[]),a=r.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:r};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;i.length&&a.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}}),t.each(N_)}function N_(e){P(e,function(t,r){var n=[],i=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,u=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(l,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return i;var v,d;s?d=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var _=NaN,p=r-1;p>=0;p--){var g=e[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,v)),d>=0){var y=g.data.getByRawIndex(g.stackResultDimension,d);if(u==="all"||u==="positive"&&y>0||u==="negative"&&y<0||u==="samesign"&&c>=0&&y>0||u==="samesign"&&c<=0&&y<0){c=ep(c,y),_=y;break}}}return n[0]=c,n[1]=_,n})})}var ga=function(){function e(t){this.data=t.data||(t.sourceFormat===le?{}:[]),this.sourceFormat=t.sourceFormat||gv,this.seriesLayoutBy=t.seriesLayoutBy||de,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var r=this.dimensionsDefine=t.dimensionsDefine;if(r)for(var n=0;n<r.length;n++){var i=r[n];i.type==null&&p_(this,n)===ct.Must&&(i.type="ordinal")}}return e}();function H_(e){return e instanceof ga}function es(e,t,r){r=r||bv(e);var n=t.seriesLayoutBy,i=G_(e,r,n,t.sourceHeader,t.dimensions),a=new ga({data:e,sourceFormat:r,seriesLayoutBy:n,dimensionsDefine:i.dimensionsDefine,startIndex:i.startIndex,dimensionsDetectedCount:i.dimensionsDetectedCount,metaRawOption:W(t)});return a}function U_(e){return new ga({data:e,sourceFormat:Dt(e)?or:me})}function V_(e){return new ga({data:e.data,sourceFormat:e.sourceFormat,seriesLayoutBy:e.seriesLayoutBy,dimensionsDefine:W(e.dimensionsDefine),startIndex:e.startIndex,dimensionsDetectedCount:e.dimensionsDetectedCount})}function bv(e){var t=gv;if(Dt(e))t=or;else if(F(e)){e.length===0&&(t=Pt);for(var r=0,n=e.length;r<n;r++){var i=e[r];if(i!=null){if(F(i)||Dt(i)){t=Pt;break}else if(B(i)){t=Jt;break}}}}else if(B(e)){for(var a in e)if(Fr(e,a)&&It(e[a])){t=le;break}}return t}function G_(e,t,r,n,i){var a,o;if(!e)return{dimensionsDefine:Xl(i),startIndex:o,dimensionsDetectedCount:a};if(t===Pt){var s=e;n==="auto"||n==null?ql(function(l){l!=null&&l!=="-"&&(H(l)?o==null&&(o=1):o=0)},r,s,10):o=ut(n)?n:n?1:0,!i&&o===1&&(i=[],ql(function(l,f){i[f]=l!=null?l+"":""},r,s,1/0)),a=i?i.length:r===Vr?s.length:s[0]?s[0].length:null}else if(t===Jt)i||(i=Y_(e));else if(t===le)i||(i=[],P(e,function(l,f){i.push(f)}));else if(t===me){var u=na(e[0]);a=F(u)&&u.length||1}return{startIndex:o,dimensionsDefine:Xl(i),dimensionsDetectedCount:a}}function Y_(e){for(var t=0,r;t<e.length&&!(r=e[t++]););if(r)return it(r)}function Xl(e){if(e){var t=Z();return X(e,function(r,n){r=B(r)?r:{name:r};var i={name:r.name,displayName:r.displayName,type:r.type};if(i.name==null)return i;i.name+="",i.displayName==null&&(i.displayName=i.name);var a=t.get(i.name);return a?i.name+="-"+a.count++:t.set(i.name,{count:1}),i})}}function ql(e,t,r,n){if(t===Vr)for(var i=0;i<r.length&&i<n;i++)e(r[i]?r[i][0]:null,i);else for(var a=r[0]||[],i=0;i<a.length&&i<n;i++)e(a[i],i)}function W_(e){var t=e.sourceFormat;return t===Jt||t===le}var je,tr,er,$l,Zl,X_=function(){function e(t,r){var n=H_(t)?t:U_(t);this._source=n;var i=this._data=n.data;n.sourceFormat===or&&(this._offset=0,this._dimSize=r,this._data=i),Zl(this,i,n)}return e.prototype.getSource=function(){return this._source},e.prototype.count=function(){return 0},e.prototype.getItem=function(t,r){},e.prototype.appendData=function(t){},e.prototype.clean=function(){},e.protoInitialize=function(){var t=e.prototype;t.pure=!1,t.persistent=!0}(),e.internalField=function(){var t;Zl=function(o,s,u){var l=u.sourceFormat,f=u.seriesLayoutBy,h=u.startIndex,c=u.dimensionsDefine,v=$l[Zs(l,f)];if(L(o,v),l===or)o.getItem=r,o.count=i,o.fillStorage=n;else{var d=Cv(l,f);o.getItem=ie(d,null,s,h,c);var _=Mv(l,f);o.count=ie(_,null,s,h,c)}};var r=function(o,s){o=o-this._offset,s=s||[];for(var u=this._data,l=this._dimSize,f=l*o,h=0;h<l;h++)s[h]=u[f+h];return s},n=function(o,s,u,l){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=l[c],d=v[0]==null?1/0:v[0],_=v[1]==null?-1/0:v[1],p=s-o,g=u[c],y=0;y<p;y++){var m=f[y*h+c];g[o+y]=m,m<d&&(d=m),m>_&&(_=m)}v[0]=d,v[1]=_}},i=function(){return this._data?this._data.length/this._dimSize:0};$l=(t={},t[Pt+"_"+de]={pure:!0,appendData:a},t[Pt+"_"+Vr]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[Jt]={pure:!0,appendData:a},t[le]={pure:!0,appendData:function(o){var s=this._data;P(o,function(u,l){for(var f=s[l]||(s[l]=[]),h=0;h<(u||[]).length;h++)f.push(u[h])})}},t[me]={appendData:a},t[or]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),e}(),Kl=function(e,t,r,n){return e[n]},q_=(je={},je[Pt+"_"+de]=function(e,t,r,n){return e[n+t]},je[Pt+"_"+Vr]=function(e,t,r,n,i){n+=t;for(var a=i||[],o=e,s=0;s<o.length;s++){var u=o[s];a[s]=u?u[n]:null}return a},je[Jt]=Kl,je[le]=function(e,t,r,n,i){for(var a=i||[],o=0;o<r.length;o++){var s=r[o].name,u=e[s];a[o]=u?u[n]:null}return a},je[me]=Kl,je);function Cv(e,t){var r=q_[Zs(e,t)];return r}var Ql=function(e,t,r){return e.length},$_=(tr={},tr[Pt+"_"+de]=function(e,t,r){return Math.max(0,e.length-t)},tr[Pt+"_"+Vr]=function(e,t,r){var n=e[0];return n?Math.max(0,n.length-t):0},tr[Jt]=Ql,tr[le]=function(e,t,r){var n=r[0].name,i=e[n];return i?i.length:0},tr[me]=Ql,tr);function Mv(e,t){var r=$_[Zs(e,t)];return r}var fo=function(e,t,r){return e[t]},Z_=(er={},er[Pt]=fo,er[Jt]=function(e,t,r){return e[r]},er[le]=fo,er[me]=function(e,t,r){var n=na(e);return n instanceof Array?n[t]:n},er[or]=fo,er);function Dv(e){var t=Z_[e];return t}function Zs(e,t){return e===Pt?e+"_"+t:e}function Xi(e,t,r){if(e){var n=e.getRawDataItem(t);if(n!=null){var i=e.getStore(),a=i.getSource().sourceFormat;if(r!=null){var o=e.getDimensionIndex(r),s=i.getDimensionProperty(o);return Dv(a)(n,o,s)}else{var u=n;return a===me&&(u=na(n)),u}}}}var K_=/\{@(.+?)\}/g,Q_=function(){function e(){}return e.prototype.getDataParams=function(t,r){var n=this.getData(r),i=this.getRawValue(t,r),a=n.getRawIndex(t),o=n.getName(t),s=n.getRawDataItem(t),u=n.getItemVisual(t,"style"),l=u&&u[n.getItemVisual(t,"drawType")||"fill"],f=u&&u.stroke,h=this.mainType,c=h==="series",v=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:r,value:i,color:l,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},e.prototype.getFormattedLabel=function(t,r,n,i,a,o){r=r||"normal";var s=this.getData(n),u=this.getDataParams(t,n);if(o&&(u.value=o.interpolatedValue),i!=null&&F(u.value)&&(u.value=u.value[i]),!a){var l=s.getItemModel(t);a=l.get(r==="normal"?["label","formatter"]:[r,"label","formatter"])}if(at(a))return u.status=r,u.dimensionIndex=i,a(u);if(H(a)){var f=a_(a,u);return f.replace(K_,function(h,c){var v=c.length,d=c;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var _=Xi(s,t,d);if(o&&F(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(_=o.interpolatedValue[p])}return _!=null?_+"":""})}},e.prototype.getRawValue=function(t,r){return Xi(this.getData(r),t)},e.prototype.formatTooltip=function(t,r,n){},e}();function dw(e){var t,r;return B(e)?e.type&&(r=e):t=e,{text:t,frag:r}}function Tn(e){return new J_(e)}var J_=function(){function e(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return e.prototype.perform=function(t){var r=this._upstream,n=t&&t.skip;if(this._dirty&&r){var i=this.context;i.data=i.outputData=r.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!n&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,u=f(t&&t.modBy),l=t&&t.modDataCount||0;(o!==u||s!==l)&&(a="reset");function f(y){return!(y>=1)&&(y=1),y}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(n)),this._modBy=u,this._modDataCount=l;var c=t&&t.step;if(r?this._dueEnd=r._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!n&&(h||v<d)){var _=this._progress;if(F(_))for(var p=0;p<_.length;p++)this._doProgress(_[p],v,d,u,l);else this._doProgress(_,v,d,u,l)}this._dueIndex=d;var g=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},e.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},e.prototype._doProgress=function(t,r,n,i,a){Jl.reset(r,n,i,a),this._callingProgress=t,this._callingProgress({start:r,end:n,count:n-r,next:Jl.next},this.context)},e.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var r,n;!t&&this._reset&&(r=this._reset(this.context),r&&r.progress&&(n=r.forceFirstProgress,r=r.progress),F(r)&&!r.length&&(r=null)),this._progress=r,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},e.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},e.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},e.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},e.prototype.getUpstream=function(){return this._upstream},e.prototype.getDownstream=function(){return this._downstream},e.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},e}(),Jl=function(){var e,t,r,n,i,a={reset:function(u,l,f,h){t=u,e=l,r=f,n=h,i=Math.ceil(n/r),a.next=r>1&&n>0?s:o}};return a;function o(){return t<e?t++:null}function s(){var u=t%i*r+Math.ceil(t/i),l=t>=e?null:u<n?u:t;return t++,l}}();function Li(e,t){var r=t&&t.type;return r==="ordinal"?e:(r==="time"&&!ut(e)&&e!=null&&e!=="-"&&(e=+Ur(e)),e==null||e===""?NaN:Number(e))}Z({number:function(e){return parseFloat(e)},time:function(e){return+Ur(e)},trim:function(e){return H(e)?ce(e):e}});var j_=function(){function e(t,r){var n=t==="desc";this._resultLT=n?1:-1,r==null&&(r=n?"min":"max"),this._incomparable=r==="min"?-1/0:1/0}return e.prototype.evaluate=function(t,r){var n=ut(t)?t:zi(t),i=ut(r)?r:zi(r),a=isNaN(n),o=isNaN(i);if(a&&(n=this._incomparable),o&&(i=this._incomparable),a&&o){var s=H(t),u=H(r);s&&(n=u?t:0),u&&(i=s?r:0)}return n<i?this._resultLT:n>i?-this._resultLT:0},e}(),ty=function(){function e(){}return e.prototype.getRawData=function(){throw new Error("not supported")},e.prototype.getRawDataItem=function(t){throw new Error("not supported")},e.prototype.cloneRawData=function(){},e.prototype.getDimensionInfo=function(t){},e.prototype.cloneAllDimensionInfo=function(){},e.prototype.count=function(){},e.prototype.retrieveValue=function(t,r){},e.prototype.retrieveValueFromItem=function(t,r){},e.prototype.convertValue=function(t,r){return Li(t,r)},e}();function ey(e,t){var r=new ty,n=e.data,i=r.sourceFormat=e.sourceFormat,a=e.startIndex,o="";e.seriesLayoutBy!==de&&xt(o);var s=[],u={},l=e.dimensionsDefine;if(l)P(l,function(_,p){var g=_.name,y={index:p,name:g,displayName:_.displayName};if(s.push(y),g!=null){var m="";Fr(u,g)&&xt(m),u[g]=y}});else for(var f=0;f<e.dimensionsDetectedCount;f++)s.push({index:f});var h=Cv(i,de);t.__isBuiltIn&&(r.getRawDataItem=function(_){return h(n,a,s,_)},r.getRawData=ie(ry,null,e)),r.cloneRawData=ie(ny,null,e);var c=Mv(i,de);r.count=ie(c,null,n,a,s);var v=Dv(i);r.retrieveValue=function(_,p){var g=h(n,a,s,_);return d(g,p)};var d=r.retrieveValueFromItem=function(_,p){if(_!=null){var g=s[p];if(g)return v(_,p,g.name)}};return r.getDimensionInfo=ie(iy,null,s,u),r.cloneAllDimensionInfo=ie(ay,null,s),r}function ry(e){var t=e.sourceFormat;if(!Ks(t)){var r="";xt(r)}return e.data}function ny(e){var t=e.sourceFormat,r=e.data;if(!Ks(t)){var n="";xt(n)}if(t===Pt){for(var i=[],a=0,o=r.length;a<o;a++)i.push(r[a].slice());return i}else if(t===Jt){for(var i=[],a=0,o=r.length;a<o;a++)i.push(L({},r[a]));return i}}function iy(e,t,r){if(r!=null){if(ut(r)||!isNaN(r)&&!Fr(t,r))return e[r];if(Fr(t,r))return t[r]}}function ay(e){return W(e)}var Pv=Z();function oy(e){e=W(e);var t=e.type,r="";t||xt(r);var n=t.split(":");n.length!==2&&xt(r);var i=!1;n[0]==="echarts"&&(t=n[1],i=!0),e.__isBuiltIn=i,Pv.set(t,e)}function sy(e,t,r){var n=Lt(e),i=n.length,a="";i||xt(a);for(var o=0,s=i;o<s;o++){var u=n[o];t=uy(u,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function uy(e,t,r,n){var i="";t.length||xt(i),B(e)||xt(i);var a=e.type,o=Pv.get(a);o||xt(i);var s=X(t,function(l){return ey(l,o)}),u=Lt(o.transform({upstream:s[0],upstreamList:s,config:W(e.config)}));return X(u,function(l,f){var h="";B(l)||xt(h),l.data||xt(h);var c=bv(l.data);Ks(c)||xt(h);var v,d=t[0];if(d&&f===0&&!l.dimensions){var _=d.startIndex;_&&(l.data=d.data.slice(0,_).concat(l.data)),v={seriesLayoutBy:de,sourceHeader:_,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:de,sourceHeader:0,dimensions:l.dimensions};return es(l.data,v,null)})}function Ks(e){return e===Pt||e===Jt}var _a="undefined",ly=typeof Uint32Array===_a?Array:Uint32Array,fy=typeof Uint16Array===_a?Array:Uint16Array,hy=typeof Int32Array===_a?Array:Int32Array,jl=typeof Float64Array===_a?Array:Float64Array,Rv={float:jl,int:hy,ordinal:Array,number:Array,time:jl},ho;function Sr(e){return e>65535?ly:fy}function Tr(){return[1/0,-1/0]}function vy(e){var t=e.constructor;return t===Array?e.slice():new t(e)}function tf(e,t,r,n,i){var a=Rv[r||"float"];if(i){var o=e[t],s=o&&o.length;if(s!==n){for(var u=new a(n),l=0;l<s;l++)u[l]=o[l];e[t]=u}}else e[t]=new a(n)}var cy=function(){function e(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=Z()}return e.prototype.initData=function(t,r,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),a=this.defaultDimValueGetter=ho[i.sourceFormat];this._dimValueGetter=n||a,this._rawExtent=[],W_(i),this._dimensions=X(r,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},e.prototype.getProvider=function(){return this._provider},e.prototype.getSource=function(){return this._provider.getSource()},e.prototype.ensureCalculationDimension=function(t,r){var n=this._calcDimNameToIdx,i=this._dimensions,a=n.get(t);if(a!=null){if(i[a].type===r)return a}else a=i.length;return i[a]={type:r},n.set(t,a),this._chunks[a]=new Rv[r||"float"](this._rawCount),this._rawExtent[a]=Tr(),a},e.prototype.collectOrdinalMeta=function(t,r){var n=this._chunks[t],i=this._dimensions[t],a=this._rawExtent,o=i.ordinalOffset||0,s=n.length;o===0&&(a[t]=Tr());for(var u=a[t],l=o;l<s;l++){var f=n[l]=r.parseAndCollect(n[l]);isNaN(f)||(u[0]=Math.min(f,u[0]),u[1]=Math.max(f,u[1]))}i.ordinalMeta=r,i.ordinalOffset=s,i.type="ordinal"},e.prototype.getOrdinalMeta=function(t){var r=this._dimensions[t],n=r.ordinalMeta;return n},e.prototype.getDimensionProperty=function(t){var r=this._dimensions[t];return r&&r.property},e.prototype.appendData=function(t){var r=this._provider,n=this.count();r.appendData(t);var i=r.count();return r.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},e.prototype.appendValues=function(t,r){for(var n=this._chunks,i=this._dimensions,a=i.length,o=this._rawExtent,s=this.count(),u=s+Math.max(t.length,r||0),l=0;l<a;l++){var f=i[l];tf(n,l,f.type,u,!0)}for(var h=[],c=s;c<u;c++)for(var v=c-s,d=0;d<a;d++){var f=i[d],_=ho.arrayRows.call(this,t[v]||h,f.property,v,d);n[d][c]=_;var p=o[d];_<p[0]&&(p[0]=_),_>p[1]&&(p[1]=_)}return this._rawCount=this._count=u,{start:s,end:u}},e.prototype._initDataFromProvider=function(t,r,n){for(var i=this._provider,a=this._chunks,o=this._dimensions,s=o.length,u=this._rawExtent,l=X(o,function(y){return y.property}),f=0;f<s;f++){var h=o[f];u[f]||(u[f]=Tr()),tf(a,f,h.type,r,n)}if(i.fillStorage)i.fillStorage(t,r,a,u);else for(var c=[],v=t;v<r;v++){c=i.getItem(v,c);for(var d=0;d<s;d++){var _=a[d],p=this._dimValueGetter(c,l[d],v,d);_[v]=p;var g=u[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=r,this._extent=[]},e.prototype.count=function(){return this._count},e.prototype.get=function(t,r){if(!(r>=0&&r<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(r)]:NaN},e.prototype.getValues=function(t,r){var n=[],i=[];if(r==null){r=t,t=[];for(var a=0;a<this._dimensions.length;a++)i.push(a)}else i=t;for(var a=0,o=i.length;a<o;a++)n.push(this.get(i[a],r));return n},e.prototype.getByRawIndex=function(t,r){if(!(r>=0&&r<this._rawCount))return NaN;var n=this._chunks[t];return n?n[r]:NaN},e.prototype.getSum=function(t){var r=this._chunks[t],n=0;if(r)for(var i=0,a=this.count();i<a;i++){var o=this.get(t,i);isNaN(o)||(n+=o)}return n},e.prototype.getMedian=function(t){var r=[];this.each([t],function(a){isNaN(a)||r.push(a)});var n=r.sort(function(a,o){return a-o}),i=this.count();return i===0?0:i%2===1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},e.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var r=this._indices,n=r[t];if(n!=null&&n<this._count&&n===t)return t;for(var i=0,a=this._count-1;i<=a;){var o=(i+a)/2|0;if(r[o]<t)i=o+1;else if(r[o]>t)a=o-1;else return o}return-1},e.prototype.indicesOfNearest=function(t,r,n){var i=this._chunks,a=i[t],o=[];if(!a)return o;n==null&&(n=1/0);for(var s=1/0,u=-1,l=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=r-a[c],d=Math.abs(v);d<=n&&((d<s||d===s&&v>=0&&u<0)&&(s=d,u=v,l=0),v===u&&(o[l++]=f))}return o.length=l,o},e.prototype.getIndices=function(){var t,r=this._indices;if(r){var n=r.constructor,i=this._count;if(n===Array){t=new n(i);for(var a=0;a<i;a++)t[a]=r[a]}else t=new n(r.buffer,0,i)}else{var n=Sr(this._rawCount);t=new n(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},e.prototype.filter=function(t,r){if(!this._count)return this;for(var n=this.clone(),i=n.count(),a=Sr(n._rawCount),o=new a(i),s=[],u=t.length,l=0,f=t[0],h=n._chunks,c=0;c<i;c++){var v=void 0,d=n.getRawIndex(c);if(u===0)v=r(c);else if(u===1){var _=h[f][d];v=r(_,c)}else{for(var p=0;p<u;p++)s[p]=h[t[p]][d];s[p]=c,v=r.apply(null,s)}v&&(o[l++]=d)}return l<i&&(n._indices=o),n._count=l,n._extent=[],n._updateGetRawIdx(),n},e.prototype.selectRange=function(t){var r=this.clone(),n=r._count;if(!n)return this;var i=it(t),a=i.length;if(!a)return this;var o=r.count(),s=Sr(r._rawCount),u=new s(o),l=0,f=i[0],h=t[f][0],c=t[f][1],v=r._chunks,d=!1;if(!r._indices){var _=0;if(a===1){for(var p=v[i[0]],g=0;g<n;g++){var y=p[g];(y>=h&&y<=c||isNaN(y))&&(u[l++]=_),_++}d=!0}else if(a===2){for(var p=v[i[0]],m=v[i[1]],w=t[i[1]][0],T=t[i[1]][1],g=0;g<n;g++){var y=p[g],S=m[g];(y>=h&&y<=c||isNaN(y))&&(S>=w&&S<=T||isNaN(S))&&(u[l++]=_),_++}d=!0}}if(!d)if(a===1)for(var g=0;g<o;g++){var b=r.getRawIndex(g),y=v[i[0]][b];(y>=h&&y<=c||isNaN(y))&&(u[l++]=b)}else for(var g=0;g<o;g++){for(var C=!0,b=r.getRawIndex(g),M=0;M<a;M++){var A=i[M],y=v[A][b];(y<t[A][0]||y>t[A][1])&&(C=!1)}C&&(u[l++]=r.getRawIndex(g))}return l<o&&(r._indices=u),r._count=l,r._extent=[],r._updateGetRawIdx(),r},e.prototype.map=function(t,r){var n=this.clone(t);return this._updateDims(n,t,r),n},e.prototype.modify=function(t,r){this._updateDims(this,t,r)},e.prototype._updateDims=function(t,r,n){for(var i=t._chunks,a=[],o=r.length,s=t.count(),u=[],l=t._rawExtent,f=0;f<r.length;f++)l[r[f]]=Tr();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)u[v]=i[r[v]][c];u[o]=h;var d=n&&n.apply(null,u);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var _=r[f],p=d[f],g=l[_],y=i[_];y&&(y[c]=p),p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}}},e.prototype.lttbDownSample=function(t,r){var n=this.clone([t],!0),i=n._chunks,a=i[t],o=this.count(),s=0,u=Math.floor(1/r),l=this.getRawIndex(0),f,h,c,v=new(Sr(this._rawCount))(Math.min((Math.ceil(o/u)+2)*2,o));v[s++]=l;for(var d=1;d<o-1;d+=u){for(var _=Math.min(d+u,o-1),p=Math.min(d+u*2,o),g=(p+_)/2,y=0,m=_;m<p;m++){var w=this.getRawIndex(m),T=a[w];isNaN(T)||(y+=T)}y/=p-_;var S=d,b=Math.min(d+u,o),C=d-1,M=a[l];f=-1,c=S;for(var A=-1,D=0,m=S;m<b;m++){var w=this.getRawIndex(m),T=a[w];if(isNaN(T)){D++,A<0&&(A=w);continue}h=Math.abs((C-g)*(T-M)-(C-m)*(y-M)),h>f&&(f=h,c=w)}D>0&&D<b-S&&(v[s++]=Math.min(A,c),c=Math.max(A,c)),v[s++]=c,l=c}return v[s++]=this.getRawIndex(o-1),n._count=s,n._indices=v,n.getRawIndex=this._getRawIdx,n},e.prototype.minmaxDownSample=function(t,r){for(var n=this.clone([t],!0),i=n._chunks,a=Math.floor(1/r),o=i[t],s=this.count(),u=new(Sr(this._rawCount))(Math.ceil(s/a)*2),l=0,f=0;f<s;f+=a){var h=f,c=o[this.getRawIndex(h)],v=f,d=o[this.getRawIndex(v)],_=a;f+a>s&&(_=s-f);for(var p=0;p<_;p++){var g=this.getRawIndex(f+p),y=o[g];y<c&&(c=y,h=f+p),y>d&&(d=y,v=f+p)}var m=this.getRawIndex(h),w=this.getRawIndex(v);h<v?(u[l++]=m,u[l++]=w):(u[l++]=w,u[l++]=m)}return n._count=l,n._indices=u,n._updateGetRawIdx(),n},e.prototype.downSample=function(t,r,n,i){for(var a=this.clone([t],!0),o=a._chunks,s=[],u=Math.floor(1/r),l=o[t],f=this.count(),h=a._rawExtent[t]=Tr(),c=new(Sr(this._rawCount))(Math.ceil(f/u)),v=0,d=0;d<f;d+=u){u>f-d&&(u=f-d,s.length=u);for(var _=0;_<u;_++){var p=this.getRawIndex(d+_);s[_]=l[p]}var g=n(s),y=this.getRawIndex(Math.min(d+i(s,g)||0,f-1));l[y]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),c[v++]=y}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},e.prototype.each=function(t,r){if(this._count)for(var n=t.length,i=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(n){case 0:r(a);break;case 1:r(i[t[0]][s],a);break;case 2:r(i[t[0]][s],i[t[1]][s],a);break;default:for(var u=0,l=[];u<n;u++)l[u]=i[t[u]][s];l[u]=a,r.apply(null,l)}}},e.prototype.getDataExtent=function(t){var r=this._chunks[t],n=Tr();if(!r)return n;var i=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=n;for(var s=o[0],u=o[1],l=0;l<i;l++){var f=this.getRawIndex(l),h=r[f];h<s&&(s=h),h>u&&(u=h)}return o=[s,u],this._extent[t]=o,o},e.prototype.getRawDataItem=function(t){var r=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(r);for(var n=[],i=this._chunks,a=0;a<i.length;a++)n.push(i[a][r]);return n},e.prototype.clone=function(t,r){var n=new e,i=this._chunks,a=t&&Ee(t,function(s,u){return s[u]=!0,s},{});if(a)for(var o=0;o<i.length;o++)n._chunks[o]=a[o]?vy(i[o]):i[o];else n._chunks=i;return this._copyCommonProps(n),r||(n._indices=this._cloneIndices()),n._updateGetRawIdx(),n},e.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=W(this._extent),t._rawExtent=W(this._rawExtent)},e.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,r=void 0;if(t===Array){var n=this._indices.length;r=new t(n);for(var i=0;i<n;i++)r[i]=this._indices[i]}else r=new t(this._indices);return r}return null},e.prototype._getRawIdxIdentity=function(t){return t},e.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},e.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},e.internalField=function(){function t(r,n,i,a){return Li(r[a],this._dimensions[a])}ho={arrayRows:t,objectRows:function(r,n,i,a){return Li(r[n],this._dimensions[a])},keyedColumns:t,original:function(r,n,i,a){var o=r&&(r.value==null?r:r.value);return Li(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(r,n,i,a){return r[a]}}}(),e}(),dy=function(){function e(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return e.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},e.prototype._setLocalSource=function(t,r){this._sourceList=t,this._upstreamSignList=r,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},e.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},e.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},e.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,r=this._getUpstreamSourceManagers(),n=!!r.length,i,a;if(fi(t)){var o=t,s=void 0,u=void 0,l=void 0;if(n){var f=r[0];f.prepareSource(),l=f.getSource(),s=l.data,u=l.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),u=Dt(s)?or:me,a=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},v=Y(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=Y(h.sourceHeader,c.sourceHeader),_=Y(h.dimensions,c.dimensions),p=v!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||_;i=p?[es(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:_},u)]:[]}else{var g=t;if(n){var y=this._applyTransform(r);i=y.sourceList,a=y.upstreamSignList}else{var m=g.get("source",!0);i=[es(m,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(i,a)},e.prototype._applyTransform=function(t){var r=this._sourceHost,n=r.get("transform",!0),i=r.get("fromTransformResult",!0);if(i!=null){var a="";t.length!==1&&ef(a)}var o,s=[],u=[];return P(t,function(l){l.prepareSource();var f=l.getSource(i||0),h="";i!=null&&!f&&ef(h),s.push(f),u.push(l._getVersionSign())}),n?o=sy(n,s,{datasetIndex:r.componentIndex}):i!=null&&(o=[V_(s[0])]),{sourceList:o,upstreamSignList:u}},e.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),r=0;r<t.length;r++){var n=t[r];if(n._isDirty()||this._upstreamSignList[r]!==n._getVersionSign())return!0}},e.prototype.getSource=function(t){t=t||0;var r=this._sourceList[t];if(!r){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return r},e.prototype.getSharedDataStore=function(t){var r=t.makeStoreSchema();return this._innerGetDataStore(r.dimensions,t.source,r.hash)},e.prototype._innerGetDataStore=function(t,r,n){var i=0,a=this._storeList,o=a[i];o||(o=a[i]={});var s=o[n];if(!s){var u=this._getUpstreamSourceManagers()[0];fi(this._sourceHost)&&u?s=u._innerGetDataStore(t,r,n):(s=new cy,s.initData(new X_(r,t.length),t)),o[n]=s}return s},e.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(fi(t)){var r=Xs(t);return r?[r.getSourceManager()]:[]}else return X(d_(t),function(n){return n.getSourceManager()})},e.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,r,n,i;if(fi(t))r=t.get("seriesLayoutBy",!0),n=t.get("sourceHeader",!0),i=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;r=a.get("seriesLayoutBy",!0),n=a.get("sourceHeader",!0),i=a.get("dimensions",!0)}return{seriesLayoutBy:r,sourceHeader:n,dimensions:i}},e}();function fi(e){return e.mainType==="series"}function ef(e){throw new Error(e)}var py="line-height:1";function Av(e){var t=e.lineHeight;return t==null?py:"line-height:"+At(t+"")+"px"}function xv(e,t){var r=e.color||"#6e7079",n=e.fontSize||12,i=e.fontWeight||"400",a=e.color||"#464646",o=e.fontSize||14,s=e.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+At(n+"")+"px;color:"+At(r)+";font-weight:"+At(i+""),valueStyle:"font-size:"+At(o+"")+"px;color:"+At(a)+";font-weight:"+At(s+"")}:{nameStyle:{fontSize:n,fill:r,fontWeight:i},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var gy=[0,10,20,30],_y=["",`
`,`

`,`


`];function rs(e,t){return t.type=e,t}function ns(e){return e.type==="section"}function Ev(e){return ns(e)?yy:my}function Lv(e){if(ns(e)){var t=0,r=e.blocks.length,n=r>1||r>0&&!e.noHeader;return P(e.blocks,function(i){var a=Lv(i);a>=t&&(t=a+ +(n&&(!a||ns(i)&&!i.noHeader)))}),t}return 0}function yy(e,t,r,n){var i=t.noHeader,a=wy(Lv(t)),o=[],s=t.blocks||[];pe(!s||F(s)),s=s||[];var u=e.orderMode;if(t.sortBlocks&&u){s=s.slice();var l={valueAsc:"asc",valueDesc:"desc"};if(Fr(l,u)){var f=new j_(l[u],null);s.sort(function(_,p){return f.evaluate(_.sortParam,p.sortParam)})}else u==="seriesDesc"&&s.reverse()}P(s,function(_,p){var g=t.valueFormatter,y=Ev(_)(g?L(L({},e),{valueFormatter:g}):e,_,p>0?a.html:0,n);y!=null&&o.push(y)});var h=e.renderMode==="richText"?o.join(a.richText):is(n,o.join(""),i?r:a.html);if(i)return h;var c=ts(t.header,"ordinal",e.useUTC),v=xv(n,e.renderMode).nameStyle,d=Av(n);return e.renderMode==="richText"?Iv(e,c,v)+a.richText+h:is(n,'<div style="'+v+";"+d+';">'+At(c)+"</div>"+h,r)}function my(e,t,r,n){var i=e.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,u=t.name,l=e.useUTC,f=t.valueFormatter||e.valueFormatter||function(w){return w=F(w)?w:[w],X(w,function(T,S){return ts(T,F(v)?v[S]:v,l)})};if(!(a&&o)){var h=s?"":e.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",i),c=a?"":ts(u,"ordinal",l),v=t.valueType,d=o?[]:f(t.value,t.dataIndex),_=!s||!a,p=!s&&a,g=xv(n,i),y=g.nameStyle,m=g.valueStyle;return i==="richText"?(s?"":h)+(a?"":Iv(e,c,y))+(o?"":by(e,d,_,p,m)):is(n,(s?"":h)+(a?"":Sy(c,!s,y))+(o?"":Ty(d,_,p,m)),r)}}function pw(e,t,r,n,i,a){if(e){var o=Ev(e),s={useUTC:i,renderMode:r,orderMode:n,markupStyleCreator:t,valueFormatter:e.valueFormatter};return o(s,e,0,a)}}function wy(e){return{html:gy[e],richText:_y[e]}}function is(e,t,r){var n='<div style="clear:both"></div>',i="margin: "+r+"px 0 0",a=Av(e);return'<div style="'+i+";"+a+';">'+t+n+"</div>"}function Sy(e,t,r){var n=t?"margin-left:2px":"";return'<span style="'+r+";"+n+'">'+At(e)+"</span>"}function Ty(e,t,r,n){var i=r?"10px":"20px",a=t?"float:right;margin-left:"+i:"";return e=F(e)?e:[e],'<span style="'+a+";"+n+'">'+X(e,function(o){return At(o)}).join("&nbsp;&nbsp;")+"</span>"}function Iv(e,t,r){return e.markupStyleCreator.wrapRichTextStyle(t,r)}function by(e,t,r,n,i){var a=[i],o=n?10:20;return r&&a.push({padding:[0,0,0,o],align:"right"}),e.markupStyleCreator.wrapRichTextStyle(F(t)?t.join("  "):t,a)}function Cy(e,t){var r=e.getData().getItemVisual(t,"style"),n=r[e.visualDrawType];return s_(n)}function gw(e,t){var r=e.get("padding");return r??(t==="richText"?[8,10]:10)}var _w=function(){function e(){this.richTextStyles={},this._nextStyleNameId=mh()}return e.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},e.prototype.makeTooltipMarker=function(t,r,n){var i=n==="richText"?this._generateStyleName():null,a=o_({color:r,type:t,renderMode:n,markerId:i});return H(a)?a:(this.richTextStyles[i]=a.style,a.content)},e.prototype.wrapRichTextStyle=function(t,r){var n={};F(r)?P(r,function(a){return L(n,a)}):L(n,r);var i=this._generateStyleName();return this.richTextStyles[i]=n,"{"+i+"|"+t+"}"},e}();function My(e){var t=e.series,r=e.dataIndex,n=e.multipleSeries,i=t.getData(),a=i.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(r),u=F(s),l=Cy(t,r),f,h,c,v;if(o>1||u&&!o){var d=Dy(s,t,r,a,l);f=d.inlineValues,h=d.inlineValueTypes,c=d.blocks,v=d.inlineValues[0]}else if(o){var _=i.getDimensionInfo(a[0]);v=f=Xi(i,r,a[0]),h=_.type}else v=f=u?s[0]:s;var p=bh(t),g=p&&t.name||"",y=i.getName(r),m=n?g:y;return rs("section",{header:g,noHeader:n||!p,sortParam:v,blocks:[rs("nameValue",{markerType:"item",markerColor:l,name:m,noName:!ce(m),value:f,valueType:h,dataIndex:r})].concat(c||[])})}function Dy(e,t,r,n,i){var a=t.getData(),o=Ee(e,function(h,c,v){var d=a.getDimensionInfo(v);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],u=[],l=[];n.length?P(n,function(h){f(Xi(a,r,h),h)}):P(e,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?l.push(rs("nameValue",{markerType:"subItem",markerColor:i,name:v.displayName,value:h,valueType:v.type})):(s.push(h),u.push(v.type)))}return{inlineValues:s,inlineValueTypes:u,blocks:l}}var be=Ot();function hi(e,t){return e.getName(t)||e.getId(t)}var Py="__universalTransitionEnabled",En=function(e){N(t,e);function t(){var r=e!==null&&e.apply(this,arguments)||this;return r._selectedDataIndicesMap={},r}return t.prototype.init=function(r,n,i){this.seriesIndex=this.componentIndex,this.dataTask=Tn({count:Ay,reset:xy}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(r,i);var a=be(this).sourceManager=new dy(this);a.prepareSource();var o=this.getInitialData(r,i);nf(o,this),this.dataTask.context.data=o,be(this).dataBeforeProcessed=o,rf(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(r,n){var i=Yi(this),a=i?dv(r):{},o=this.subType;rt.hasClass(o)&&(o+="Series"),st(r,n.getTheme().get(this.subType)),st(r,this.getDefaultOption()),Wu(r,"label",["show"]),this.fillDataTextStyle(r.data),i&&Wi(r,a,i)},t.prototype.mergeOption=function(r,n){r=st(this.option,r,!0),this.fillDataTextStyle(r.data);var i=Yi(this);i&&Wi(this.option,r,i);var a=be(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(r,n);nf(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,be(this).dataBeforeProcessed=o,rf(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(r){if(r&&!Dt(r))for(var n=["show"],i=0;i<r.length;i++)r[i]&&r[i].label&&Wu(r[i],"label",n)},t.prototype.getInitialData=function(r,n){},t.prototype.appendData=function(r){var n=this.getRawData();n.appendData(r.data)},t.prototype.getData=function(r){var n=as(this);if(n){var i=n.context.data;return r==null||!i.getLinkedData?i:i.getLinkedData(r)}else return be(this).data},t.prototype.getAllData=function(){var r=this.getData();return r&&r.getLinkedDataAll?r.getLinkedDataAll():[{data:r}]},t.prototype.setData=function(r){var n=as(this);if(n){var i=n.context;i.outputData=r,n!==this.dataTask&&(i.data=r)}be(this).data=r},t.prototype.getEncode=function(){var r=this.get("encode",!0);if(r)return Z(r)},t.prototype.getSourceManager=function(){return be(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return be(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var r=this.get("colorBy");return r||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var r=this.coordinateSystem;return r&&r.getBaseAxis&&r.getBaseAxis()},t.prototype.formatTooltip=function(r,n,i){return My({series:this,dataIndex:r,multipleSeries:n})},t.prototype.isAnimationEnabled=function(){var r=this.ecModel;if(U.node&&!(r&&r.ssr))return!1;var n=this.getShallow("animation");return n&&this.getData().count()>this.getShallow("animationThreshold")&&(n=!1),!!n},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(r,n,i){var a=this.ecModel,o=qs.prototype.getColorFromPalette.call(this,r,n,i);return o||(o=a.getColorFromPalette(r,n,i)),o},t.prototype.coordDimToDataDim=function(r){return this.getRawData().mapDimensionsAll(r)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(r,n){this._innerSelect(this.getData(n),r)},t.prototype.unselect=function(r,n){var i=this.option.selectedMap;if(i){var a=this.option.selectedMode,o=this.getData(n);if(a==="series"||i==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<r.length;s++){var u=r[s],l=hi(o,u);i[l]=!1,this._selectedDataIndicesMap[l]=-1}}},t.prototype.toggleSelect=function(r,n){for(var i=[],a=0;a<r.length;a++)i[0]=r[a],this.isSelected(r[a],n)?this.unselect(i,n):this.select(i,n)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var r=this._selectedDataIndicesMap,n=it(r),i=[],a=0;a<n.length;a++){var o=r[n[a]];o>=0&&i.push(o)}return i},t.prototype.isSelected=function(r,n){var i=this.option.selectedMap;if(!i)return!1;var a=this.getData(n);return(i==="all"||i[hi(a,r)])&&!a.getItemModel(r).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[Py])return!0;var r=this.option.universalTransition;return r?r===!0?!0:r&&r.enabled:!1},t.prototype._innerSelect=function(r,n){var i,a,o=this.option,s=o.selectedMode,u=n.length;if(!(!s||!u)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){B(o.selectedMap)||(o.selectedMap={});for(var l=o.selectedMap,f=0;f<u;f++){var h=n[f],c=hi(r,h);l[c]=!0,this._selectedDataIndicesMap[c]=r.getRawIndex(h)}}else if(s==="single"||s===!0){var v=n[u-1],c=hi(r,v);o.selectedMap=(i={},i[c]=!0,i),this._selectedDataIndicesMap=(a={},a[c]=r.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(r){if(!this.option.selectedMap){var n=[];r.hasItemOption&&r.each(function(i){var a=r.getRawDataItem(i);a&&a.selected&&n.push(i)}),n.length>0&&this._innerSelect(r,n)}},t.registerClass=function(r){return rt.registerClass(r)},t.protoInitialize=function(){var r=t.prototype;r.type="series.__base__",r.seriesIndex=0,r.ignoreStyleOnData=!1,r.hasSymbolVisual=!1,r.defaultSymbol="circle",r.visualStyleAccessPath="itemStyle",r.visualDrawType="fill"}(),t}(rt);ge(En,Q_);ge(En,qs);Ph(En,rt);function rf(e){var t=e.name;bh(e)||(e.name=Ry(e)||t)}function Ry(e){var t=e.getRawData(),r=t.mapDimensionsAll("seriesName"),n=[];return P(r,function(i){var a=t.getDimensionInfo(i);a.displayName&&n.push(a.displayName)}),n.join(" ")}function Ay(e){return e.model.getRawData().count()}function xy(e){var t=e.model;return t.setData(t.getRawData().cloneShallow()),Ey}function Ey(e,t){t.outputData&&e.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function nf(e,t){P(Bc(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(r){e.wrapMethod(r,_s(Ly,t))})}function Ly(e,t){var r=as(e);return r&&r.setOutputEnd((t||this).count()),t}function as(e){var t=(e.ecModel||{}).scheduler,r=t&&t.getPipeline(e.uid);if(r){var n=r.currentTask;if(n){var i=n.agentStubMap;i&&(n=i.get(e.uid))}return n}}var Qs=function(){function e(){this.group=new Hr,this.uid=pa("viewComponent")}return e.prototype.init=function(t,r){},e.prototype.render=function(t,r,n,i){},e.prototype.dispose=function(t,r){},e.prototype.updateView=function(t,r,n,i){},e.prototype.updateLayout=function(t,r,n,i){},e.prototype.updateVisual=function(t,r,n,i){},e.prototype.toggleBlurSeries=function(t,r,n){},e.prototype.eachRendered=function(t){var r=this.group;r&&r.traverse(t)},e}();Ms(Qs);Ds(Qs);function Iy(){var e=Ot();return function(t){var r=e(t),n=t.pipelineContext,i=!!r.large,a=!!r.progressiveRender,o=r.large=!!(n&&n.large),s=r.progressiveRender=!!(n&&n.progressiveRender);return(i!==o||a!==s)&&"reset"}}var Ov=Ot(),Oy=Iy(),bn=function(){function e(){this.group=new Hr,this.uid=pa("viewChart"),this.renderTask=Tn({plan:ky,reset:Fy}),this.renderTask.context={view:this}}return e.prototype.init=function(t,r){},e.prototype.render=function(t,r,n,i){},e.prototype.highlight=function(t,r,n,i){var a=t.getData(i&&i.dataType);a&&of(a,i,"emphasis")},e.prototype.downplay=function(t,r,n,i){var a=t.getData(i&&i.dataType);a&&of(a,i,"normal")},e.prototype.remove=function(t,r){this.group.removeAll()},e.prototype.dispose=function(t,r){},e.prototype.updateView=function(t,r,n,i){this.render(t,r,n,i)},e.prototype.updateLayout=function(t,r,n,i){this.render(t,r,n,i)},e.prototype.updateVisual=function(t,r,n,i){this.render(t,r,n,i)},e.prototype.eachRendered=function(t){av(this.group,t)},e.markUpdateMethod=function(t,r){Ov(t).updateMethod=r},e.protoInitialize=function(){var t=e.prototype;t.type="chart"}(),e}();function af(e,t,r){e&&Ko(e)&&(t==="emphasis"?Xo:qo)(e,r)}function of(e,t,r){var n=ia(e,t),i=t&&t.highlightKey!=null?Ig(t.highlightKey):null;n!=null?P(Lt(n),function(a){af(e.getItemGraphicEl(a),r,i)}):e.eachItemGraphicEl(function(a){af(a,r,i)})}Ms(bn);Ds(bn);function ky(e){return Oy(e.model)}function Fy(e){var t=e.model,r=e.ecModel,n=e.api,i=e.payload,a=t.pipelineContext.progressiveRender,o=e.view,s=i&&Ov(i).updateMethod,u=a?"incrementalPrepareRender":s&&o[s]?s:"render";return u!=="render"&&o[u](t,r,n,i),By[u]}var By={incrementalPrepareRender:{progress:function(e,t){t.view.incrementalRender(e,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(e,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}},qi="\0__throttleOriginMethod",sf="\0__throttleRate",uf="\0__throttleType";function Js(e,t,r){var n,i=0,a=0,o=null,s,u,l,f;t=t||0;function h(){a=new Date().getTime(),o=null,e.apply(u,l||[])}var c=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];n=new Date().getTime(),u=this,l=v;var _=f||t,p=f||r;f=null,s=n-(p?i:a)-_,clearTimeout(o),p?o=setTimeout(h,_):s>=0?h():o=setTimeout(h,-s),i=n};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function yw(e,t,r,n){var i=e[t];if(i){var a=i[qi]||i,o=i[uf],s=i[sf];if(s!==r||o!==n){if(r==null||!n)return e[t]=a;i=e[t]=Js(a,r,n==="debounce"),i[qi]=a,i[uf]=n,i[sf]=r}return i}}function mw(e,t){var r=e[t];r&&r[qi]&&(r.clear&&r.clear(),e[t]=r[qi])}var lf=Ot(),ff={itemStyle:An(uv,!0),lineStyle:An(sv,!0)},zy={lineStyle:"stroke",itemStyle:"fill"};function kv(e,t){var r=e.visualStyleMapper||ff[t];return r||(console.warn("Unknown style type '"+t+"'."),ff.itemStyle)}function Fv(e,t){var r=e.visualDrawType||zy[t];return r||(console.warn("Unknown style type '"+t+"'."),"fill")}var Ny={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){var r=e.getData(),n=e.visualStyleAccessPath||"itemStyle",i=e.getModel(n),a=kv(e,n),o=a(i),s=i.getShallow("decal");s&&(r.setVisual("decal",s),s.dirty=!0);var u=Fv(e,n),l=o[u],f=at(l)?l:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[u]||f||h){var c=e.getColorFromPalette(e.name,null,t.getSeriesCount());o[u]||(o[u]=c,r.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||at(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||at(o.stroke)?c:o.stroke}if(r.setVisual("style",o),r.setVisual("drawType",u),!t.isSeriesFiltered(e)&&f)return r.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var _=e.getDataParams(d),p=L({},o);p[u]=f(_),v.setItemVisual(d,"style",p)}}}},tn=new Mt,Hy={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){if(!(e.ignoreStyleOnData||t.isSeriesFiltered(e))){var r=e.getData(),n=e.visualStyleAccessPath||"itemStyle",i=kv(e,n),a=r.getVisual("drawType");return{dataEach:r.hasItemOption?function(o,s){var u=o.getRawDataItem(s);if(u&&u[n]){tn.option=u[n];var l=i(tn),f=o.ensureUniqueItemVisual(s,"style");L(f,l),tn.option.decal&&(o.setItemVisual(s,"decal",tn.option.decal),tn.option.decal.dirty=!0),a in l&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},Uy={performRawSeries:!0,overallReset:function(e){var t=Z();e.eachSeries(function(r){var n=r.getColorBy();if(!r.isColorBySeries()){var i=r.type+"-"+n,a=t.get(i);a||(a={},t.set(i,a)),lf(r).scope=a}}),e.eachSeries(function(r){if(!(r.isColorBySeries()||e.isSeriesFiltered(r))){var n=r.getRawData(),i={},a=r.getData(),o=lf(r).scope,s=r.visualStyleAccessPath||"itemStyle",u=Fv(r,s);a.each(function(l){var f=a.getRawIndex(l);i[f]=l}),n.each(function(l){var f=i[l],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=n.getName(l)||l+"",d=n.count();c[u]=r.getColorFromPalette(v,o,d)}})}})}},vi=Math.PI;function Vy(e,t){t=t||{},wt(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var r=new Hr,n=new ue({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});r.add(n);var i=new zr({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new ue({style:{fill:"none"},textContent:i,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});r.add(a);var o;return t.showSpinner&&(o=new ca({shape:{startAngle:-vi/2,endAngle:-vi/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:vi*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:vi*3/2}).delay(300).start("circularInOut"),r.add(o)),r.resize=function(){var s=i.getBoundingRect().width,u=t.showSpinner?t.spinnerRadius:0,l=(e.getWidth()-u*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:u),f=e.getHeight()/2;t.showSpinner&&o.setShape({cx:l,cy:f}),a.setShape({x:l-u,y:f-u,width:u*2,height:u*2}),n.setShape({x:0,y:0,width:e.getWidth(),height:e.getHeight()})},r.resize(),r}var Bv=function(){function e(t,r,n,i){this._stageTaskMap=Z(),this.ecInstance=t,this.api=r,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return e.prototype.restoreData=function(t,r){t.restoreData(r),this._stageTaskMap.each(function(n){var i=n.overallTask;i&&i.dirty()})},e.prototype.getPerformArgs=function(t,r){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,a=!r&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex,o=a?n.step:null,s=i&&i.modDataCount,u=s!=null?Math.ceil(s/o):null;return{step:o,modBy:u,modDataCount:s}}},e.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},e.prototype.updateStreamModes=function(t,r){var n=this._pipelineMap.get(t.uid),i=t.getData(),a=i.count(),o=n.progressiveEnabled&&r.incrementalPrepareRender&&a>=n.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),u=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=n.context={progressiveRender:o,modDataCount:u,large:s}},e.prototype.restorePipelines=function(t){var r=this,n=r._pipelineMap=Z();t.eachSeries(function(i){var a=i.getProgressive(),o=i.uid;n.set(o,{id:o,head:null,tail:null,threshold:i.getProgressiveThreshold(),progressiveEnabled:a&&!(i.preventIncremental&&i.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),r._pipe(i,i.dataTask)})},e.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,r=this.api.getModel(),n=this.api;P(this._allHandlers,function(i){var a=t.get(i.uid)||t.set(i.uid,{}),o="";pe(!(i.reset&&i.overallReset),o),i.reset&&this._createSeriesStageTask(i,a,r,n),i.overallReset&&this._createOverallStageTask(i,a,r,n)},this)},e.prototype.prepareView=function(t,r,n,i){var a=t.renderTask,o=a.context;o.model=r,o.ecModel=n,o.api=i,a.__block=!t.incrementalPrepareRender,this._pipe(r,a)},e.prototype.performDataProcessorTasks=function(t,r){this._performStageTasks(this._dataProcessorHandlers,t,r,{block:!0})},e.prototype.performVisualTasks=function(t,r,n){this._performStageTasks(this._visualHandlers,t,r,n)},e.prototype._performStageTasks=function(t,r,n,i){i=i||{};var a=!1,o=this;P(t,function(u,l){if(!(i.visualType&&i.visualType!==u.visualType)){var f=o._stageTaskMap.get(u.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,d=c.agentStubMap;d.each(function(p){s(i,p)&&(p.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,n);var _=o.getPerformArgs(c,i.block);d.each(function(p){p.perform(_)}),c.perform(_)&&(a=!0)}else h&&h.each(function(p,g){s(i,p)&&p.dirty();var y=o.getPerformArgs(p,i.block);y.skip=!u.performRawSeries&&r.isSeriesFiltered(p.context.model),o.updatePayload(p,n),p.perform(y)&&(a=!0)})}});function s(u,l){return u.setDirty&&(!u.dirtyMap||u.dirtyMap.get(l.__pipeline.id))}this.unfinished=a||this.unfinished},e.prototype.performSeriesTasks=function(t){var r;t.eachSeries(function(n){r=n.dataTask.perform()||r}),this.unfinished=r||this.unfinished},e.prototype.plan=function(){this._pipelineMap.each(function(t){var r=t.tail;do{if(r.__block){t.blockIndex=r.__idxInPipeline;break}r=r.getUpstream()}while(r)})},e.prototype.updatePayload=function(t,r){r!=="remain"&&(t.context.payload=r)},e.prototype._createSeriesStageTask=function(t,r,n,i){var a=this,o=r.seriesTaskMap,s=r.seriesTaskMap=Z(),u=t.seriesType,l=t.getTargetSeries;t.createOnAllSeries?n.eachRawSeries(f):u?n.eachRawSeriesByType(u,f):l&&l(n,i).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||Tn({plan:qy,reset:$y,count:Ky}));v.context={model:h,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},e.prototype._createOverallStageTask=function(t,r,n,i){var a=this,o=r.overallTask=r.overallTask||Tn({reset:Gy});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,u=o.agentStubMap=Z(),l=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";pe(!t.createOnAllSeries,v),l?n.eachRawSeriesByType(l,d):f?f(n,i).each(d):(h=!1,P(n.getSeries(),d));function d(_){var p=_.uid,g=u.set(p,s&&s.get(p)||(c=!0,Tn({reset:Yy,onDirty:Xy})));g.context={model:_,overallProgress:h},g.agent=o,g.__block=h,a._pipe(_,g)}c&&o.dirty()},e.prototype._pipe=function(t,r){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=r),i.tail&&i.tail.pipe(r),i.tail=r,r.__idxInPipeline=i.count++,r.__pipeline=i},e.wrapStageHandler=function(t,r){return at(t)&&(t={overallReset:t,seriesType:Qy(t)}),t.uid=pa("stageHandler"),r&&(t.visualType=r),t},e}();function Gy(e){e.overallReset(e.ecModel,e.api,e.payload)}function Yy(e){return e.overallProgress&&Wy}function Wy(){this.agent.dirty(),this.getDownstream().dirty()}function Xy(){this.agent&&this.agent.dirty()}function qy(e){return e.plan?e.plan(e.model,e.ecModel,e.api,e.payload):null}function $y(e){e.useClearVisual&&e.data.clearAllVisual();var t=e.resetDefines=Lt(e.reset(e.model,e.ecModel,e.api,e.payload));return t.length>1?X(t,function(r,n){return zv(n)}):Zy}var Zy=zv(0);function zv(e){return function(t,r){var n=r.data,i=r.resetDefines[e];if(i&&i.dataEach)for(var a=t.start;a<t.end;a++)i.dataEach(n,a);else i&&i.progress&&i.progress(t,n)}}function Ky(e){return e.data.count()}function Qy(e){$i=null;try{e(Ln,Nv)}catch{}return $i}var Ln={},Nv={},$i;Hv(Ln,$s);Hv(Nv,mv);Ln.eachSeriesByType=Ln.eachRawSeriesByType=function(e){$i=e};Ln.eachComponent=function(e){e.mainType==="series"&&e.subType&&($i=e.subType)};function Hv(e,t){for(var r in t.prototype)e[r]=Vt}var hf=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const Jy={color:hf,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],hf]};var vt="#B9B8CE",vf="#100C2A",ci=function(){return{axisLine:{lineStyle:{color:vt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},cf=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Uv={darkMode:!0,color:cf,backgroundColor:vf,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:vt},pageTextStyle:{color:vt}},textStyle:{color:vt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:vt}},dataZoom:{borderColor:"#71708A",textStyle:{color:vt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:vt}},timeline:{lineStyle:{color:vt},label:{color:vt},controlStyle:{color:vt,borderColor:vt}},calendar:{itemStyle:{color:vf},dayLabel:{color:vt},monthLabel:{color:vt},yearLabel:{color:vt}},timeAxis:ci(),logAxis:ci(),valueAxis:ci(),categoryAxis:ci(),line:{symbol:"circle"},graph:{color:cf},gauge:{title:{color:vt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:vt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Uv.categoryAxis.splitLine.show=!1;var jy=function(){function e(){}return e.prototype.normalizeQuery=function(t){var r={},n={},i={};if(H(t)){var a=se(t);r.mainType=a.main||null,r.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};P(t,function(u,l){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=l.lastIndexOf(c);if(v>0&&v===l.length-c.length){var d=l.slice(0,v);d!=="data"&&(r.mainType=d,r[c.toLowerCase()]=u,f=!0)}}s.hasOwnProperty(l)&&(n[l]=u,f=!0),f||(i[l]=u)})}return{cptQuery:r,dataQuery:n,otherQuery:i}},e.prototype.filter=function(t,r){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,a=n.packedEvent,o=n.model,s=n.view;if(!o||!s)return!0;var u=r.cptQuery,l=r.dataQuery;return f(u,o,"mainType")&&f(u,o,"subType")&&f(u,o,"index","componentIndex")&&f(u,o,"name")&&f(u,o,"id")&&f(l,a,"name")&&f(l,a,"dataIndex")&&f(l,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,r.otherQuery,i,a));function f(h,c,v,d){return h[v]==null||c[d||v]===h[v]}},e.prototype.afterTrigger=function(){this.eventInfo=null},e}(),os=["symbol","symbolSize","symbolRotate","symbolOffset"],df=os.concat(["symbolKeepAspect"]),tm={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){var r=e.getData();if(e.legendIcon&&r.setVisual("legendIcon",e.legendIcon),!e.hasSymbolVisual)return;for(var n={},i={},a=!1,o=0;o<os.length;o++){var s=os[o],u=e.get(s);at(u)?(a=!0,i[s]=u):n[s]=u}if(n.symbol=n.symbol||e.defaultSymbol,r.setVisual(L({legendIcon:e.legendIcon||n.symbol,symbolKeepAspect:e.get("symbolKeepAspect")},n)),t.isSeriesFiltered(e))return;var l=it(i);function f(h,c){for(var v=e.getRawValue(c),d=e.getDataParams(c),_=0;_<l.length;_++){var p=l[_];h.setItemVisual(c,p,i[p](v,d))}}return{dataEach:a?f:null}}},em={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){if(!e.hasSymbolVisual||t.isSeriesFiltered(e))return;var r=e.getData();function n(i,a){for(var o=i.getItemModel(a),s=0;s<df.length;s++){var u=df[s],l=o.getShallow(u,!0);l!=null&&i.setItemVisual(a,u,l)}}return{dataEach:r.hasItemOption?n:null}}};function rm(e,t,r){switch(r){case"color":var n=e.getItemVisual(t,"style");return n[e.getVisual("drawType")];case"opacity":return e.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return e.getItemVisual(t,r)}}function nm(e,t){switch(t){case"color":var r=e.getVisual("style");return r[e.getVisual("drawType")];case"opacity":return e.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return e.getVisual(t)}}function ww(e,t){function r(n,i){var a=[];return n.eachComponent({mainType:"series",subType:e,query:i},function(o){a.push(o.seriesIndex)}),a}P([[e+"ToggleSelect","toggleSelect"],[e+"Select","select"],[e+"UnSelect","unselect"]],function(n){t(n[0],function(i,a,o){i=L({},i),o.dispatchAction(L(i,{type:n[1],seriesIndex:r(a,i)}))})})}function br(e,t,r,n,i){var a=e+t;r.isSilent(a)||n.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,u=o.option.selectedMap,l=i.selected,f=0;f<l.length;f++)if(l[f].seriesIndex===s){var h=o.getData(),c=ia(h,i.fromActionPayload);r.trigger(a,{type:a,seriesId:o.id,name:F(c)?h.getName(c[0]):h.getName(c),selected:H(u)?u:L({},u)})}})}function im(e,t,r){e.on("selectchanged",function(n){var i=r.getModel();n.isFromClick?(br("map","selectchanged",t,i,n),br("pie","selectchanged",t,i,n)):n.fromAction==="select"?(br("map","selected",t,i,n),br("pie","selected",t,i,n)):n.fromAction==="unselect"&&(br("map","unselected",t,i,n),br("pie","unselected",t,i,n))})}function di(e,t,r){for(var n;e&&!(t(e)&&(n=e,r));)e=e.__hostTarget||e.parent;return n}var am=Math.round(Math.random()*9),om=typeof Object.defineProperty=="function",sm=function(){function e(){this._id="__ec_inner_"+am++}return e.prototype.get=function(t){return this._guard(t)[this._id]},e.prototype.set=function(t,r){var n=this._guard(t);return om?Object.defineProperty(n,this._id,{value:r,enumerable:!1,configurable:!0}):n[this._id]=r,this},e.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},e.prototype.has=function(t){return!!this._guard(t)[this._id]},e.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},e}(),um=Q.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(e,t){var r=t.cx,n=t.cy,i=t.width/2,a=t.height/2;e.moveTo(r,n-a),e.lineTo(r+i,n+a),e.lineTo(r-i,n+a),e.closePath()}}),lm=Q.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(e,t){var r=t.cx,n=t.cy,i=t.width/2,a=t.height/2;e.moveTo(r,n-a),e.lineTo(r+i,n),e.lineTo(r,n+a),e.lineTo(r-i,n),e.closePath()}}),fm=Q.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(e,t){var r=t.x,n=t.y,i=t.width/5*3,a=Math.max(i,t.height),o=i/2,s=o*o/(a-o),u=n-a+o+s,l=Math.asin(s/o),f=Math.cos(l)*o,h=Math.sin(l),c=Math.cos(l),v=o*.6,d=o*.7;e.moveTo(r-f,u+s),e.arc(r,u,o,Math.PI-l,Math.PI*2+l),e.bezierCurveTo(r+f-h*v,u+s+c*v,r,n-d,r,n),e.bezierCurveTo(r,n-d,r-f+h*v,u+s+c*v,r-f,u+s),e.closePath()}}),hm=Q.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(e,t){var r=t.height,n=t.width,i=t.x,a=t.y,o=n/3*2;e.moveTo(i,a),e.lineTo(i+o,a+r),e.lineTo(i,a+r/4*3),e.lineTo(i-o,a+r),e.lineTo(i,a),e.closePath()}}),vm={line:va,rect:ue,roundRect:ue,square:ue,circle:ha,diamond:lm,pin:fm,arrow:hm,triangle:um},cm={line:function(e,t,r,n,i){i.x1=e,i.y1=t+n/2,i.x2=e+r,i.y2=t+n/2},rect:function(e,t,r,n,i){i.x=e,i.y=t,i.width=r,i.height=n},roundRect:function(e,t,r,n,i){i.x=e,i.y=t,i.width=r,i.height=n,i.r=Math.min(r,n)/4},square:function(e,t,r,n,i){var a=Math.min(r,n);i.x=e,i.y=t,i.width=a,i.height=a},circle:function(e,t,r,n,i){i.cx=e+r/2,i.cy=t+n/2,i.r=Math.min(r,n)/2},diamond:function(e,t,r,n,i){i.cx=e+r/2,i.cy=t+n/2,i.width=r,i.height=n},pin:function(e,t,r,n,i){i.x=e+r/2,i.y=t+n/2,i.width=r,i.height=n},arrow:function(e,t,r,n,i){i.x=e+r/2,i.y=t+n/2,i.width=r,i.height=n},triangle:function(e,t,r,n,i){i.cx=e+r/2,i.cy=t+n/2,i.width=r,i.height=n}},ss={};P(vm,function(e,t){ss[t]=new e});var dm=Q.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(e,t,r){var n=dh(e,t,r),i=this.shape;return i&&i.symbolType==="pin"&&t.position==="inside"&&(n.y=r.y+r.height*.4),n},buildPath:function(e,t,r){var n=t.symbolType;if(n!=="none"){var i=ss[n];i||(n="rect",i=ss[n]),cm[n](t.x,t.y,t.width,t.height,i.shape),i.buildPath(e,i.shape,r)}}});function pm(e,t){if(this.type!=="image"){var r=this.style;this.__isEmptyBrush?(r.stroke=e,r.fill=t||"#fff",r.lineWidth=2):this.shape.symbolType==="line"?r.stroke=e:r.fill=e,this.markRedraw()}}function gm(e,t,r,n,i,a,o){var s=e.indexOf("empty")===0;s&&(e=e.substr(5,1).toLowerCase()+e.substr(6));var u;return e.indexOf("image://")===0?u=tv(e.slice(8),new tt(t,r,n,i),o?"center":"cover"):e.indexOf("path://")===0?u=zs(e.slice(7),{},new tt(t,r,n,i),o?"center":"cover"):u=new dm({shape:{symbolType:e,x:t,y:r,width:n,height:i}}),u.__isEmptyBrush=s,u.setColor=pm,a&&u.setColor(a),u}function Sw(e){return F(e)||(e=[+e,+e]),[e[0]||0,e[1]||0]}function Tw(e,t){if(e!=null)return F(e)||(e=[e,e]),[De(e[0],t[0])||0,De(Y(e[1],e[0]),t[1])||0]}function nr(e){return isFinite(e)}function _m(e,t,r){var n=t.x==null?0:t.x,i=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(n=n*r.width+r.x,i=i*r.width+r.x,a=a*r.height+r.y,o=o*r.height+r.y),n=nr(n)?n:0,i=nr(i)?i:1,a=nr(a)?a:0,o=nr(o)?o:0;var s=e.createLinearGradient(n,a,i,o);return s}function ym(e,t,r){var n=r.width,i=r.height,a=Math.min(n,i),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,u=t.r==null?.5:t.r;t.global||(o=o*n+r.x,s=s*i+r.y,u=u*a),o=nr(o)?o:.5,s=nr(s)?s:.5,u=u>=0&&nr(u)?u:.5;var l=e.createRadialGradient(o,s,0,o,s,u);return l}function pf(e,t,r){for(var n=t.type==="radial"?ym(e,t,r):_m(e,t,r),i=t.colorStops,a=0;a<i.length;a++)n.addColorStop(i[a].offset,i[a].color);return n}function mm(e,t){if(e===t||!e&&!t)return!1;if(!e||!t||e.length!==t.length)return!0;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!0;return!1}function pi(e){return parseInt(e,10)}function bw(e,t,r){var n=["width","height"][t],i=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(r[n]!=null&&r[n]!=="auto")return parseFloat(r[n]);var s=document.defaultView.getComputedStyle(e);return(e[i]||pi(s[n])||pi(e.style[n]))-(pi(s[a])||0)-(pi(s[o])||0)|0}function wm(e,t){return!e||e==="solid"||!(t>0)?null:e==="dashed"?[4*t,2*t]:e==="dotted"?[t]:ut(e)?[e]:F(e)?e:null}function Vv(e){var t=e.style,r=t.lineDash&&t.lineWidth>0&&wm(t.lineDash,t.lineWidth),n=t.lineDashOffset;if(r){var i=t.strokeNoScale&&e.getLineScale?e.getLineScale():1;i&&i!==1&&(r=X(r,function(a){return a/i}),n/=i)}return[r,n]}var Sm=new Br(!0);function Zi(e){var t=e.stroke;return!(t==null||t==="none"||!(e.lineWidth>0))}function gf(e){return typeof e=="string"&&e!=="none"}function Ki(e){var t=e.fill;return t!=null&&t!=="none"}function _f(e,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.fillOpacity*t.opacity,e.fill(),e.globalAlpha=r}else e.fill()}function yf(e,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var r=e.globalAlpha;e.globalAlpha=t.strokeOpacity*t.opacity,e.stroke(),e.globalAlpha=r}else e.stroke()}function mf(e,t,r){var n=Rh(t.image,t.__image,r);if(oa(n)){var i=e.createPattern(n,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&i&&i.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*zc),a.scaleSelf(t.scaleX||1,t.scaleY||1),i.setTransform(a)}return i}}function Tm(e,t,r,n){var i,a=Zi(r),o=Ki(r),s=r.strokePercent,u=s<1,l=!t.path;(!t.silent||u)&&l&&t.createPathProxy();var f=t.path||Sm,h=t.__dirty;if(!n){var c=r.fill,v=r.stroke,d=o&&!!c.colorStops,_=a&&!!v.colorStops,p=o&&!!c.image,g=a&&!!v.image,y=void 0,m=void 0,w=void 0,T=void 0,S=void 0;(d||_)&&(S=t.getBoundingRect()),d&&(y=h?pf(e,c,S):t.__canvasFillGradient,t.__canvasFillGradient=y),_&&(m=h?pf(e,v,S):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),p&&(w=h||!t.__canvasFillPattern?mf(e,c,t):t.__canvasFillPattern,t.__canvasFillPattern=w),g&&(T=h||!t.__canvasStrokePattern?mf(e,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=w),d?e.fillStyle=y:p&&(w?e.fillStyle=w:o=!1),_?e.strokeStyle=m:g&&(T?e.strokeStyle=T:a=!1)}var b=t.getGlobalScale();f.setScale(b[0],b[1],t.segmentIgnoreThreshold);var C,M;e.setLineDash&&r.lineDash&&(i=Vv(t),C=i[0],M=i[1]);var A=!0;(l||h&Mr)&&(f.setDPR(e.dpr),u?f.setContext(null):(f.setContext(e),A=!1),f.reset(),t.buildPath(f,t.shape,n),f.toStatic(),t.pathUpdated()),A&&f.rebuildPath(e,u?s:1),C&&(e.setLineDash(C),e.lineDashOffset=M),n||(r.strokeFirst?(a&&yf(e,r),o&&_f(e,r)):(o&&_f(e,r),a&&yf(e,r))),C&&e.setLineDash([])}function bm(e,t,r){var n=t.__image=Rh(r.image,t.__image,t,t.onload);if(!(!n||!oa(n))){var i=r.x||0,a=r.y||0,o=t.getWidth(),s=t.getHeight(),u=n.width/n.height;if(o==null&&s!=null?o=s*u:s==null&&o!=null?s=o/u:o==null&&s==null&&(o=n.width,s=n.height),r.sWidth&&r.sHeight){var l=r.sx||0,f=r.sy||0;e.drawImage(n,l,f,r.sWidth,r.sHeight,i,a,o,s)}else if(r.sx&&r.sy){var l=r.sx,f=r.sy,h=o-l,c=s-f;e.drawImage(n,l,f,h,c,i,a,o,s)}else e.drawImage(n,i,a,o,s)}}function Cm(e,t,r){var n,i=r.text;if(i!=null&&(i+=""),i){e.font=r.font||sr,e.textAlign=r.textAlign,e.textBaseline=r.textBaseline;var a=void 0,o=void 0;e.setLineDash&&r.lineDash&&(n=Vv(t),a=n[0],o=n[1]),a&&(e.setLineDash(a),e.lineDashOffset=o),r.strokeFirst?(Zi(r)&&e.strokeText(i,r.x,r.y),Ki(r)&&e.fillText(i,r.x,r.y)):(Ki(r)&&e.fillText(i,r.x,r.y),Zi(r)&&e.strokeText(i,r.x,r.y)),a&&e.setLineDash([])}}var wf=["shadowBlur","shadowOffsetX","shadowOffsetY"],Sf=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Gv(e,t,r,n,i){var a=!1;if(!n&&(r=r||{},t===r))return!1;if(n||t.opacity!==r.opacity){Ct(e,i),a=!0;var o=Math.max(Math.min(t.opacity,1),0);e.globalAlpha=isNaN(o)?ir.opacity:o}(n||t.blend!==r.blend)&&(a||(Ct(e,i),a=!0),e.globalCompositeOperation=t.blend||ir.blend);for(var s=0;s<wf.length;s++){var u=wf[s];(n||t[u]!==r[u])&&(a||(Ct(e,i),a=!0),e[u]=e.dpr*(t[u]||0))}return(n||t.shadowColor!==r.shadowColor)&&(a||(Ct(e,i),a=!0),e.shadowColor=t.shadowColor||ir.shadowColor),a}function Tf(e,t,r,n,i){var a=In(t,i.inHover),o=n?null:r&&In(r,i.inHover)||{};if(a===o)return!1;var s=Gv(e,a,o,n,i);if((n||a.fill!==o.fill)&&(s||(Ct(e,i),s=!0),gf(a.fill)&&(e.fillStyle=a.fill)),(n||a.stroke!==o.stroke)&&(s||(Ct(e,i),s=!0),gf(a.stroke)&&(e.strokeStyle=a.stroke)),(n||a.opacity!==o.opacity)&&(s||(Ct(e,i),s=!0),e.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var u=a.lineWidth,l=u/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);e.lineWidth!==l&&(s||(Ct(e,i),s=!0),e.lineWidth=l)}for(var f=0;f<Sf.length;f++){var h=Sf[f],c=h[0];(n||a[c]!==o[c])&&(s||(Ct(e,i),s=!0),e[c]=a[c]||h[1])}return s}function Mm(e,t,r,n,i){return Gv(e,In(t,i.inHover),r&&In(r,i.inHover),n,i)}function Yv(e,t){var r=t.transform,n=e.dpr||1;r?e.setTransform(n*r[0],n*r[1],n*r[2],n*r[3],n*r[4],n*r[5]):e.setTransform(n,0,0,n,0,0)}function Dm(e,t,r){for(var n=!1,i=0;i<e.length;i++){var a=e[i];n=n||a.isZeroArea(),Yv(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}r.allClipped=n}function Pm(e,t){return e&&t?e[0]!==t[0]||e[1]!==t[1]||e[2]!==t[2]||e[3]!==t[3]||e[4]!==t[4]||e[5]!==t[5]:!(!e&&!t)}var bf=1,Cf=2,Mf=3,Df=4;function Rm(e){var t=Ki(e),r=Zi(e);return!(e.lineDash||!(+t^+r)||t&&typeof e.fill!="string"||r&&typeof e.stroke!="string"||e.strokePercent<1||e.strokeOpacity<1||e.fillOpacity<1)}function Ct(e,t){t.batchFill&&e.fill(),t.batchStroke&&e.stroke(),t.batchFill="",t.batchStroke=""}function In(e,t){return t&&e.__hoverStyle||e.style}function Am(e,t){us(e,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function us(e,t,r,n){var i=t.transform;if(!t.shouldBePainted(r.viewWidth,r.viewHeight,!1,!1)){t.__dirty&=~ae,t.__isRendered=!1;return}var a=t.__clipPaths,o=r.prevElClipPaths,s=!1,u=!1;if((!o||mm(a,o))&&(o&&o.length&&(Ct(e,r),e.restore(),u=s=!0,r.prevElClipPaths=null,r.allClipped=!1,r.prevEl=null),a&&a.length&&(Ct(e,r),e.save(),Dm(a,e,r),s=!0),r.prevElClipPaths=a),r.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var l=r.prevEl;l||(u=s=!0);var f=t instanceof Q&&t.autoBatch&&Rm(t.style);s||Pm(i,l.transform)?(Ct(e,r),Yv(e,t)):f||Ct(e,r);var h=In(t,r.inHover);t instanceof Q?(r.lastDrawType!==bf&&(u=!0,r.lastDrawType=bf),Tf(e,t,l,u,r),(!f||!r.batchFill&&!r.batchStroke)&&e.beginPath(),Tm(e,t,h,f),f&&(r.batchFill=h.fill||"",r.batchStroke=h.stroke||"")):t instanceof Ni?(r.lastDrawType!==Mf&&(u=!0,r.lastDrawType=Mf),Tf(e,t,l,u,r),Cm(e,t,h)):t instanceof lr?(r.lastDrawType!==Cf&&(u=!0,r.lastDrawType=Cf),Mm(e,t,l,u,r),bm(e,t,h)):t.getTemporalDisplayables&&(r.lastDrawType!==Df&&(u=!0,r.lastDrawType=Df),xm(e,t,r)),f&&n&&Ct(e,r),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),r.prevEl=t,t.__dirty=0,t.__isRendered=!0}function xm(e,t,r){var n=t.getDisplayables(),i=t.getTemporalDisplayables();e.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:r.viewWidth,viewHeight:r.viewHeight,inHover:r.inHover},o,s;for(o=t.getCursor(),s=n.length;o<s;o++){var u=n[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),us(e,u,a,o===s-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}for(var l=0,f=i.length;l<f;l++){var u=i[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),us(e,u,a,l===f-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}t.clearTemporalDisplayables(),t.notClear=!0,e.restore()}var vo=new sm,Pf=new Fn(100),Rf=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Af(e,t){if(e==="none")return null;var r=t.getDevicePixelRatio(),n=t.getZr(),i=n.painter.type==="svg";e.dirty&&vo.delete(e);var a=vo.get(e);if(a)return a;var o=wt(e,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return u(s),s.rotation=o.rotation,s.scaleX=s.scaleY=i?1:1/r,vo.set(e,s),e.dirty=!1,s;function u(l){for(var f=[r],h=!0,c=0;c<Rf.length;++c){var v=o[Rf[c]];if(v!=null&&!F(v)&&!H(v)&&!ut(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var d;if(h){d=f.join(",")+(i?"-svg":"");var _=Pf.get(d);_&&(i?l.svgElement=_:l.image=_)}var p=Xv(o.dashArrayX),g=Em(o.dashArrayY),y=Wv(o.symbol),m=Lm(p),w=qv(g),T=!i&&On.createCanvas(),S=i&&{tag:"g",attrs:{},key:"dcl",children:[]},b=M(),C;T&&(T.width=b.width*r,T.height=b.height*r,C=T.getContext("2d")),A(),h&&Pf.put(d,T||S),l.image=T,l.svgElement=S,l.svgWidth=b.width,l.svgHeight=b.height;function M(){for(var D=1,R=0,I=m.length;R<I;++R)D=Gu(D,m[R]);for(var x=1,R=0,I=y.length;R<I;++R)x=Gu(x,y[R].length);D*=x;var O=w*m.length*y.length;return{width:Math.max(1,Math.min(D,o.maxTileWidth)),height:Math.max(1,Math.min(O,o.maxTileHeight))}}function A(){C&&(C.clearRect(0,0,T.width,T.height),o.backgroundColor&&(C.fillStyle=o.backgroundColor,C.fillRect(0,0,T.width,T.height)));for(var D=0,R=0;R<g.length;++R)D+=g[R];if(D<=0)return;for(var I=-w,x=0,O=0,E=0;I<b.height;){if(x%2===0){for(var V=O/2%y.length,q=0,J=0,j=0;q<b.width*2;){for(var ot=0,R=0;R<p[E].length;++R)ot+=p[E][R];if(ot<=0)break;if(J%2===0){var K=(1-o.symbolSize)*.5,pt=q+p[E][J]*K,gt=I+g[x]*K,Yt=p[E][J]*o.symbolSize,Ie=g[x]*o.symbolSize,Oe=j/2%y[V].length;fr(pt,gt,Yt,Ie,y[V][Oe])}q+=p[E][J],++j,++J,J===p[E].length&&(J=0)}++E,E===p.length&&(E=0)}I+=g[x],++O,++x,x===g.length&&(x=0)}function fr(Rt,lt,k,z,ke){var _t=i?1:r,au=gm(ke,Rt*_t,lt*_t,k*_t,z*_t,o.color,o.symbolKeepAspect);if(i){var ou=n.painter.renderOneToVNode(au);ou&&S.children.push(ou)}else Am(C,au)}}}}function Wv(e){if(!e||e.length===0)return[["rect"]];if(H(e))return[[e]];for(var t=!0,r=0;r<e.length;++r)if(!H(e[r])){t=!1;break}if(t)return Wv([e]);for(var n=[],r=0;r<e.length;++r)H(e[r])?n.push([e[r]]):n.push(e[r]);return n}function Xv(e){if(!e||e.length===0)return[[0,0]];if(ut(e)){var t=Math.ceil(e);return[[t,t]]}for(var r=!0,n=0;n<e.length;++n)if(!ut(e[n])){r=!1;break}if(r)return Xv([e]);for(var i=[],n=0;n<e.length;++n)if(ut(e[n])){var t=Math.ceil(e[n]);i.push([t,t])}else{var t=X(e[n],function(s){return Math.ceil(s)});t.length%2===1?i.push(t.concat(t)):i.push(t)}return i}function Em(e){if(!e||typeof e=="object"&&e.length===0)return[0,0];if(ut(e)){var t=Math.ceil(e);return[t,t]}var r=X(e,function(n){return Math.ceil(n)});return e.length%2?r.concat(r):r}function Lm(e){return X(e,function(t){return qv(t)})}function qv(e){for(var t=0,r=0;r<e.length;++r)t+=e[r];return e.length%2===1?t*2:t}function Im(e,t){e.eachRawSeries(function(r){if(!e.isSeriesFiltered(r)){var n=r.getData();n.hasItemVisual()&&n.each(function(o){var s=n.getItemVisual(o,"decal");if(s){var u=n.ensureUniqueItemVisual(o,"style");u.decal=Af(s,t)}});var i=n.getVisual("decal");if(i){var a=n.getVisual("style");a.decal=Af(i,t)}}})}var Zt=new _e,$v={};function Cw(e,t){$v[e]=t}function Om(e){return $v[e]}var km=1,Fm=800,Bm=900,zm=1e3,Nm=2e3,Hm=5e3,Zv=1e3,Um=1100,js=2e3,Kv=3e3,Vm=4e3,ya=4500,Gm=4600,Ym=5e3,Wm=6e3,Qv=7e3,Mw={PROCESSOR:{FILTER:zm,SERIES_FILTER:Fm,STATISTIC:Hm},VISUAL:{LAYOUT:Zv,PROGRESSIVE_LAYOUT:Um,GLOBAL:js,CHART:Kv,POST_CHART_LAYOUT:Gm,COMPONENT:Vm,BRUSH:Ym,CHART_ITEM:ya,ARIA:Wm,DECAL:Qv}},ht="__flagInMainProcess",Tt="__pendingUpdate",co="__needsUpdateStatus",xf=/^[a-zA-Z0-9_]+$/,po="__connectUpdateStatus",Ef=0,Xm=1,qm=2;function Jv(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];if(this.isDisposed()){this.id;return}return tc(this,e,t)}}function jv(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return tc(this,e,t)}}function tc(e,t,r){return r[0]=r[0]&&r[0].toLowerCase(),_e.prototype[t].apply(e,r)}var ec=function(e){N(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t}(_e),rc=ec.prototype;rc.on=jv("on");rc.off=jv("off");var Cr,go,gi,Ce,_o,yo,mo,en,rn,Lf,If,wo,Of,_i,kf,nc,kt,Ff,ic=function(e){N(t,e);function t(r,n,i){var a=e.call(this,new jy)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],i=i||{},H(n)&&(n=ac[n]),a._dom=r;var o="canvas",s="auto",u=!1;i.ssr;var l=a._zr=Uu(r,{renderer:i.renderer||o,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:Y(i.useDirtyRect,u),useCoarsePointer:Y(i.useCoarsePointer,s),pointerSize:i.pointerSize});a._ssr=i.ssr,a._throttledZrFlush=Js(ie(l.flush,l),17),n=W(n),n&&Tv(n,!0),a._theme=n,a._locale=$0(i.locale||lv),a._coordSysMgr=new wv;var f=a._api=kf(a);function h(c,v){return c.__prio-v.__prio}return Ti(Ji,h),Ti(ls,h),a._scheduler=new Bv(a,f,ls,Ji),a._messageCenter=new ec,a._initEvents(),a.resize=ie(a.resize,a),l.animation.on("frame",a._onframe,a),Lf(l,a),If(l,a),Do(a),a}return t.prototype._onframe=function(){if(!this._disposed){Ff(this);var r=this._scheduler;if(this[Tt]){var n=this[Tt].silent;this[ht]=!0;try{Cr(this),Ce.update.call(this,null,this[Tt].updateParams)}catch(u){throw this[ht]=!1,this[Tt]=null,u}this._zr.flush(),this[ht]=!1,this[Tt]=null,en.call(this,n),rn.call(this,n)}else if(r.unfinished){var i=km,a=this._model,o=this._api;r.unfinished=!1;do{var s=+new Date;r.performSeriesTasks(a),r.performDataProcessorTasks(a),yo(this,a),r.performVisualTasks(a),_i(this,this._model,o,"remain",{}),i-=+new Date-s}while(i>0&&r.unfinished);r.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(r,n,i){if(!this[ht]){if(this._disposed){this.id;return}var a,o,s;if(B(n)&&(i=n.lazyUpdate,a=n.silent,o=n.replaceMerge,s=n.transition,n=n.notMerge),this[ht]=!0,!this._model||n){var u=new D_(this._api),l=this._theme,f=this._model=new $s;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,l,this._locale,u)}this._model.setOption(r,{replaceMerge:o},fs);var h={seriesTransition:s,optionChanged:!0};if(i)this[Tt]={silent:a,updateParams:h},this[ht]=!1,this.getZr().wakeUp();else{try{Cr(this),Ce.update.call(this,null,h)}catch(c){throw this[Tt]=null,this[ht]=!1,c}this._ssr||this._zr.flush(),this[Tt]=null,this[ht]=!1,en.call(this,a),rn.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||U.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(r){return this.renderToCanvas(r)},t.prototype.renderToCanvas=function(r){r=r||{};var n=this._zr.painter;return n.getRenderedCanvas({backgroundColor:r.backgroundColor||this._model.get("backgroundColor"),pixelRatio:r.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(r){r=r||{};var n=this._zr.painter;return n.renderToString({useViewBox:r.useViewBox})},t.prototype.getSvgDataURL=function(){if(U.svgSupported){var r=this._zr,n=r.storage.getDisplayList();return P(n,function(i){i.stopAnimation(null,!0)}),r.painter.toDataURL()}},t.prototype.getDataURL=function(r){if(this._disposed){this.id;return}r=r||{};var n=r.excludeComponents,i=this._model,a=[],o=this;P(n,function(u){i.eachComponent({mainType:u},function(l){var f=o._componentsMap[l.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(r).toDataURL("image/"+(r&&r.type||"png"));return P(a,function(u){u.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(r){if(this._disposed){this.id;return}var n=r.type==="svg",i=this.group,a=Math.min,o=Math.max,s=1/0;if(Bf[i]){var u=s,l=s,f=-s,h=-s,c=[],v=r&&r.pixelRatio||this.getDevicePixelRatio();P(Mn,function(m,w){if(m.group===i){var T=n?m.getZr().painter.getSvgDom().innerHTML:m.renderToCanvas(W(r)),S=m.getDom().getBoundingClientRect();u=a(S.left,u),l=a(S.top,l),f=o(S.right,f),h=o(S.bottom,h),c.push({dom:T,left:S.left,top:S.top})}}),u*=v,l*=v,f*=v,h*=v;var d=f-u,_=h-l,p=On.createCanvas(),g=Uu(p,{renderer:n?"svg":"canvas"});if(g.resize({width:d,height:_}),n){var y="";return P(c,function(m){var w=m.left-u,T=m.top-l;y+='<g transform="translate('+w+","+T+')">'+m.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=y,r.connectedBackgroundColor&&g.painter.setBackgroundColor(r.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return r.connectedBackgroundColor&&g.add(new ue({shape:{x:0,y:0,width:d,height:_},style:{fill:r.connectedBackgroundColor}})),P(c,function(m){var w=new lr({style:{x:m.left*v-u,y:m.top*v-l,image:m.dom}});g.add(w)}),g.refreshImmediately(),p.toDataURL("image/"+(r&&r.type||"png"))}else return this.getDataURL(r)},t.prototype.convertToPixel=function(r,n){return _o(this,"convertToPixel",r,n)},t.prototype.convertFromPixel=function(r,n){return _o(this,"convertFromPixel",r,n)},t.prototype.containPixel=function(r,n){if(this._disposed){this.id;return}var i=this._model,a,o=Ha(i,r);return P(o,function(s,u){u.indexOf("Models")>=0&&P(s,function(l){var f=l.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(n);else if(u==="seriesModels"){var h=this._chartsMap[l.__viewId];h&&h.containPoint&&(a=a||h.containPoint(n,l))}},this)},this),!!a},t.prototype.getVisual=function(r,n){var i=this._model,a=Ha(i,r,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),u=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return u!=null?rm(s,u,n):nm(s,n)},t.prototype.getViewOfComponentModel=function(r){return this._componentsMap[r.__viewId]},t.prototype.getViewOfSeriesModel=function(r){return this._chartsMap[r.__viewId]},t.prototype._initEvents=function(){var r=this;P($m,function(n){var i=function(a){var o=r.getModel(),s=a.target,u,l=n==="globalout";if(l?u={}:s&&di(s,function(d){var _=Gt(d);if(_&&_.dataIndex!=null){var p=_.dataModel||o.getSeriesByIndex(_.seriesIndex);return u=p&&p.getDataParams(_.dataIndex,_.dataType,s)||{},!0}else if(_.eventData)return u=L({},_.eventData),!0},!0),u){var f=u.componentType,h=u.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=u.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&r[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];u.event=a,u.type=n,r._$eventProcessor.eventInfo={targetEl:s,packedEvent:u,model:c,view:v},r.trigger(n,u)}};i.zrEventfulCallAtLast=!0,r._zr.on(n,i,r)}),P(Cn,function(n,i){r._messageCenter.on(i,function(a){this.trigger(i,a)},r)}),P(["selectchanged"],function(n){r._messageCenter.on(n,function(i){this.trigger(n,i)},r)}),im(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var r=this.getDom();r&&Mh(this.getDom(),eu,"");var n=this,i=n._api,a=n._model;P(n._componentsViews,function(o){o.dispose(a,i)}),P(n._chartsViews,function(o){o.dispose(a,i)}),n._zr.dispose(),n._dom=n._model=n._chartsMap=n._componentsMap=n._chartsViews=n._componentsViews=n._scheduler=n._api=n._zr=n._throttledZrFlush=n._theme=n._coordSysMgr=n._messageCenter=null,delete Mn[n.id]},t.prototype.resize=function(r){if(!this[ht]){if(this._disposed){this.id;return}this._zr.resize(r);var n=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!n){var i=n.resetOption("media"),a=r&&r.silent;this[Tt]&&(a==null&&(a=this[Tt].silent),i=!0,this[Tt]=null),this[ht]=!0;try{i&&Cr(this),Ce.update.call(this,{type:"resize",animation:L({duration:0},r&&r.animation)})}catch(o){throw this[ht]=!1,o}this[ht]=!1,en.call(this,a),rn.call(this,a)}}},t.prototype.showLoading=function(r,n){if(this._disposed){this.id;return}if(B(r)&&(n=r,r=""),r=r||"default",this.hideLoading(),!!hs[r]){var i=hs[r](this._api,n),a=this._zr;this._loadingFX=i,a.add(i)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(r){var n=L({},r);return n.type=Cn[r.type],n},t.prototype.dispatchAction=function(r,n){if(this._disposed){this.id;return}if(B(n)||(n={silent:!!n}),!!Qi[r.type]&&this._model){if(this[ht]){this._pendingActions.push(r);return}var i=n.silent;mo.call(this,r,i);var a=n.flush;a?this._zr.flush():a!==!1&&U.browser.weChat&&this._throttledZrFlush(),en.call(this,i),rn.call(this,i)}},t.prototype.updateLabelLayout=function(){Zt.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(r){if(this._disposed){this.id;return}var n=r.seriesIndex,i=this.getModel(),a=i.getSeriesByIndex(n);a.appendData(r),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){Cr=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),go(h,!0),go(h,!1),c.plan()},go=function(h,c){for(var v=h._model,d=h._scheduler,_=c?h._componentsViews:h._chartsViews,p=c?h._componentsMap:h._chartsMap,g=h._zr,y=h._api,m=0;m<_.length;m++)_[m].__alive=!1;c?v.eachComponent(function(S,b){S!=="series"&&w(b)}):v.eachSeries(w);function w(S){var b=S.__requireNewView;S.__requireNewView=!1;var C="_ec_"+S.id+"_"+S.type,M=!b&&p[C];if(!M){var A=se(S.type),D=c?Qs.getClass(A.main,A.sub):bn.getClass(A.sub);M=new D,M.init(v,y),p[C]=M,_.push(M),g.add(M.group)}S.__viewId=M.__id=C,M.__alive=!0,M.__model=S,M.group.__ecComponentInfo={mainType:S.mainType,index:S.componentIndex},!c&&d.prepareView(M,S,v,y)}for(var m=0;m<_.length;){var T=_[m];T.__alive?m++:(!c&&T.renderTask.dispose(),g.remove(T.group),T.dispose(v,y),_.splice(m,1),p[T.__id]===T&&delete p[T.__id],T.__id=T.group.__ecComponentInfo=null)}},gi=function(h,c,v,d,_){var p=h._model;if(p.setUpdatePayload(v),!d){P([].concat(h._componentsViews).concat(h._chartsViews),T);return}var g={};g[d+"Id"]=v[d+"Id"],g[d+"Index"]=v[d+"Index"],g[d+"Name"]=v[d+"Name"];var y={mainType:d,query:g};_&&(y.subType=_);var m=v.excludeSeriesId,w;m!=null&&(w=Z(),P(Lt(m),function(S){var b=ur(S,null);b!=null&&w.set(b,!0)})),p&&p.eachComponent(y,function(S){var b=w&&w.get(S.id)!=null;if(!b)if(_l(v))if(S instanceof En)v.type===ar&&!v.notBlur&&!S.get(["emphasis","disabled"])&&Cg(S,v,h._api);else{var C=xs(S.mainType,S.componentIndex,v.name,h._api),M=C.focusSelf,A=C.dispatchers;v.type===ar&&M&&!v.notBlur&&Zo(S.mainType,S.componentIndex,h._api),A&&P(A,function(D){v.type===ar?Xo(D):qo(D)})}else Qo(v)&&S instanceof En&&(Pg(S,v,h._api),pl(S),kt(h))},h),p&&p.eachComponent(y,function(S){var b=w&&w.get(S.id)!=null;b||T(h[d==="series"?"_chartsMap":"_componentsMap"][S.__viewId])},h);function T(S){S&&S.__alive&&S[c]&&S[c](S.__model,p,h._api,v)}},Ce={prepareAndUpdate:function(h){Cr(this),Ce.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,d=this._api,_=this._zr,p=this._coordSysMgr,g=this._scheduler;if(v){v.setUpdatePayload(h),g.restoreData(v,h),g.performSeriesTasks(v),p.create(v,d),g.performDataProcessorTasks(v,h),yo(this,v),p.update(v,d),r(v),g.performVisualTasks(v,h),wo(this,v,d,h,c);var y=v.get("backgroundColor")||"transparent",m=v.get("darkMode");_.setBackgroundColor(y),m!=null&&m!=="auto"&&_.setDarkMode(m),Zt.trigger("afterupdate",v,d)}},updateTransform:function(h){var c=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(h);var _=[];v.eachComponent(function(g,y){if(g!=="series"){var m=c.getViewOfComponentModel(y);if(m&&m.__alive)if(m.updateTransform){var w=m.updateTransform(y,v,d,h);w&&w.update&&_.push(m)}else _.push(m)}});var p=Z();v.eachSeries(function(g){var y=c._chartsMap[g.__viewId];if(y.updateTransform){var m=y.updateTransform(g,v,d,h);m&&m.update&&p.set(g.uid,1)}else p.set(g.uid,1)}),r(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:p}),_i(this,v,d,h,{},p),Zt.trigger("afterupdate",v,d)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),bn.markUpdateMethod(h,"updateView"),r(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),wo(this,c,this._api,h,{}),Zt.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(d){d.getData().clearAllVisual()}),bn.markUpdateMethod(h,"updateVisual"),r(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,_){if(d!=="series"){var p=c.getViewOfComponentModel(_);p&&p.__alive&&p.updateVisual(_,v,c._api,h)}}),v.eachSeries(function(d){var _=c._chartsMap[d.__viewId];_.updateVisual(d,v,c._api,h)}),Zt.trigger("afterupdate",v,this._api))},updateLayout:function(h){Ce.update.call(this,h)}},_o=function(h,c,v,d){if(h._disposed){h.id;return}for(var _=h._model,p=h._coordSysMgr.getCoordinateSystems(),g,y=Ha(_,v),m=0;m<p.length;m++){var w=p[m];if(w[c]&&(g=w[c](_,y,d))!=null)return g}},yo=function(h,c){var v=h._chartsMap,d=h._scheduler;c.eachSeries(function(_){d.updateStreamModes(_,v[_.__viewId])})},mo=function(h,c){var v=this,d=this.getModel(),_=h.type,p=h.escapeConnect,g=Qi[_],y=g.actionInfo,m=(y.update||"update").split(":"),w=m.pop(),T=m[0]!=null&&se(m[0]);this[ht]=!0;var S=[h],b=!1;h.batch&&(b=!0,S=X(h.batch,function(x){return x=wt(L({},x),h),x.batch=null,x}));var C=[],M,A=Qo(h),D=_l(h);if(D&&Yh(this._api),P(S,function(x){if(M=g.action(x,v._model,v._api),M=M||L({},x),M.type=y.event||M.type,C.push(M),D){var O=Ch(h),E=O.queryOptionMap,V=O.mainTypeSpecified,q=V?E.keys()[0]:"series";gi(v,w,x,q),kt(v)}else A?(gi(v,w,x,"series"),kt(v)):T&&gi(v,w,x,T.main,T.sub)}),w!=="none"&&!D&&!A&&!T)try{this[Tt]?(Cr(this),Ce.update.call(this,h),this[Tt]=null):Ce[w].call(this,h)}catch(x){throw this[ht]=!1,x}if(b?M={type:y.event||_,escapeConnect:p,batch:C}:M=C[0],this[ht]=!1,!c){var R=this._messageCenter;if(R.trigger(M.type,M),A){var I={type:"selectchanged",escapeConnect:p,selected:Rg(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};R.trigger(I.type,I)}}},en=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();mo.call(this,v,h)}},rn=function(h){!h&&this.trigger("updated")},Lf=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[Tt]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},If=function(h,c){h.on("mouseover",function(v){var d=v.target,_=di(d,Ko);_&&(Mg(_,v,c._api),kt(c))}).on("mouseout",function(v){var d=v.target,_=di(d,Ko);_&&(Dg(_,v,c._api),kt(c))}).on("click",function(v){var d=v.target,_=di(d,function(y){return Gt(y).dataIndex!=null},!0);if(_){var p=_.selected?"unselect":"select",g=Gt(_);c._api.dispatchAction({type:p,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function r(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function n(h){var c=[],v=[],d=!1;if(h.eachComponent(function(y,m){var w=m.get("zlevel")||0,T=m.get("z")||0,S=m.getZLevelKey();d=d||!!S,(y==="series"?v:c).push({zlevel:w,z:T,idx:m.componentIndex,type:y,key:S})}),d){var _=c.concat(v),p,g;Ti(_,function(y,m){return y.zlevel===m.zlevel?y.z-m.z:y.zlevel-m.zlevel}),P(_,function(y){var m=h.getComponent(y.type,y.idx),w=y.zlevel,T=y.key;p!=null&&(w=Math.max(p,w)),T?(w===p&&T!==g&&w++,g=T):g&&(w===p&&w++,g=""),p=w,m.setZLevel(w)})}}wo=function(h,c,v,d,_){n(c),Of(h,c,v,d,_),P(h._chartsViews,function(p){p.__alive=!1}),_i(h,c,v,d,_),P(h._chartsViews,function(p){p.__alive||p.remove(c,v)})},Of=function(h,c,v,d,_,p){P(p||h._componentsViews,function(g){var y=g.__model;l(y,g),g.render(y,c,v,d),s(y,g),f(y,g)})},_i=function(h,c,v,d,_,p){var g=h._scheduler;_=L(_||{},{updatedSeries:c.getSeries()}),Zt.trigger("series:beforeupdate",c,v,_);var y=!1;c.eachSeries(function(m){var w=h._chartsMap[m.__viewId];w.__alive=!0;var T=w.renderTask;g.updatePayload(T,d),l(m,w),p&&p.get(m.uid)&&T.dirty(),T.perform(g.getPerformArgs(T))&&(y=!0),w.group.silent=!!m.get("silent"),o(m,w),pl(m)}),g.unfinished=y||g.unfinished,Zt.trigger("series:layoutlabels",c,v,_),Zt.trigger("series:transition",c,v,_),c.eachSeries(function(m){var w=h._chartsMap[m.__viewId];s(m,w),f(m,w)}),a(h,c),Zt.trigger("series:afterupdate",c,v,_)},kt=function(h){h[co]=!0,h.getZr().wakeUp()},Ff=function(h){h[co]&&(h.getZr().storage.traverse(function(c){wn(c)||i(c)}),h[co]=!1)};function i(h){for(var c=[],v=h.currentStates,d=0;d<v.length;d++){var _=v[d];_==="emphasis"||_==="blur"||_==="select"||c.push(_)}h.selected&&h.states.select&&c.push("select"),h.hoverState===la&&h.states.emphasis?c.push("emphasis"):h.hoverState===ua&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,d=v.storage,_=0;d.traverse(function(p){p.isGroup||_++}),_>c.get("hoverLayerThreshold")&&!U.node&&!U.worker&&c.eachSeries(function(p){if(!p.preventUsingHoverLayer){var g=h._chartsMap[p.__viewId];g.__alive&&g.eachRendered(function(y){y.states.emphasis&&(y.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,d=h.get("zlevel")||0;c.eachRendered(function(_){return u(_,v,d,-1/0),!0})}}function u(h,c,v,d){var _=h.getTextContent(),p=h.getTextGuideLine(),g=h.isGroup;if(g)for(var y=h.childrenRef(),m=0;m<y.length;m++)d=Math.max(u(y[m],c,v,d),d);else h.z=c,h.zlevel=v,d=Math.max(h.z2,d);if(_&&(_.z=c,_.zlevel=v,isFinite(d)&&(_.z2=d+2)),p){var w=h.textGuideLineConfig;p.z=c,p.zlevel=v,isFinite(d)&&(p.z2=d+(w&&w.showAbove?1:-1))}return d}function l(h,c){c.eachRendered(function(v){if(!wn(v)){var d=v.getTextContent(),_=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),_&&_.stateTransition&&(_.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),_=v.get("duration"),p=_>0?{duration:_,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(g){if(g.states&&g.states.emphasis){if(wn(g))return;if(g instanceof Q&&Og(g),g.__dirty){var y=g.prevStates;y&&g.useStates(y)}if(d){g.stateTransition=p;var m=g.getTextContent(),w=g.getTextGuideLine();m&&(m.stateTransition=p),w&&(w.stateTransition=p)}g.__dirty&&i(g)}})}kf=function(h){return new(function(c){N(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var _=d.__ecComponentInfo;if(_!=null)return h._model.getComponent(_.mainType,_.index);d=d.parent}},v.prototype.enterEmphasis=function(d,_){Xo(d,_),kt(h)},v.prototype.leaveEmphasis=function(d,_){qo(d,_),kt(h)},v.prototype.enterBlur=function(d){bg(d),kt(h)},v.prototype.leaveBlur=function(d){Hh(d),kt(h)},v.prototype.enterSelect=function(d){Uh(d),kt(h)},v.prototype.leaveSelect=function(d){Vh(d),kt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},v}(mv))(h)},nc=function(h){function c(v,d){for(var _=0;_<v.length;_++){var p=v[_];p[po]=d}}P(Cn,function(v,d){h._messageCenter.on(d,function(_){if(Bf[h.group]&&h[po]!==Ef){if(_&&_.escapeConnect)return;var p=h.makeActionFromEvent(_),g=[];P(Mn,function(y){y!==h&&y.group===h.group&&g.push(y)}),c(g,Ef),P(g,function(y){y[po]!==Xm&&y.dispatchAction(p)}),c(g,qm)}})})}}(),t}(_e),tu=ic.prototype;tu.on=Jv("on");tu.off=Jv("off");tu.one=function(e,t,r){var n=this;function i(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),n.off(e,i)}this.on.call(this,e,i,r)};var $m=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var Qi={},Cn={},ls=[],fs=[],Ji=[],ac={},hs={},Mn={},Bf={},Zm=+new Date-0,eu="_echarts_instance_";function Km(e,t,r){var n=!(r&&r.ssr);if(n){var i=Qm(e);if(i)return i}var a=new ic(e,t,r);return a.id="ec_"+Zm++,Mn[a.id]=a,n&&Mh(e,eu,a.id),nc(a),Zt.trigger("afterinit",a),a}function Qm(e){return Mn[pp(e,eu)]}function oc(e,t){ac[e]=t}function Jm(e){nt(fs,e)<0&&fs.push(e)}function jm(e,t){ru(ls,e,t,Nm)}function Dw(e){sc("afterinit",e)}function Pw(e){sc("afterupdate",e)}function sc(e,t){Zt.on(e,t)}function zn(e,t,r){at(t)&&(r=t,t="");var n=B(e)?e.type:[e,e={event:t}][0];e.event=(e.event||n).toLowerCase(),t=e.event,!Cn[t]&&(pe(xf.test(n)&&xf.test(t)),Qi[n]||(Qi[n]={action:r,actionInfo:e}),Cn[t]=n)}function Rw(e,t){wv.register(e,t)}function Aw(e,t){ru(Ji,e,t,Zv,"layout")}function Gr(e,t){ru(Ji,e,t,Kv,"visual")}var zf=[];function ru(e,t,r,n,i){if((at(t)||B(t))&&(r=t,t=n),!(nt(zf,r)>=0)){zf.push(r);var a=Bv.wrapStageHandler(r,i);a.__prio=t,a.__raw=r,e.push(a)}}function t1(e,t){hs[e]=t}function xw(e,t,r){var n=Om("registerMap");n&&n(e,t,r)}var Ew=oy;Gr(js,Ny);Gr(ya,Hy);Gr(ya,Uy);Gr(js,tm);Gr(ya,em);Gr(Qv,Im);Jm(Tv);jm(Bm,z_);t1("default",Vy);zn({type:ar,event:ar,update:ar},Vt);zn({type:Ri,event:Ri,update:Ri},Vt);zn({type:_n,event:_n,update:_n},Vt);zn({type:Ai,event:Ai,update:Ai},Vt);zn({type:yn,event:yn,update:yn},Vt);oc("light",Jy);oc("dark",Uv);var So=null;function e1(e){return So||(So=(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){return setTimeout(t,16)}).bind(window)),So(e)}var To=null;function r1(e){To||(To=(window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||function(t){clearTimeout(t)}).bind(window)),To(e)}function n1(e){var t=document.createElement("style");return t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e)),(document.querySelector("head")||document.body).appendChild(t),t}function yi(e,t){t===void 0&&(t={});var r=document.createElement(e);return Object.keys(t).forEach(function(n){r[n]=t[n]}),r}function uc(e,t,r){var n=window.getComputedStyle(e,null)||{display:"none"};return n[t]}function vs(e){if(!document.documentElement.contains(e))return{detached:!0,rendered:!1};for(var t=e;t!==document;){if(uc(t,"display")==="none")return{detached:!1,rendered:!1};t=t.parentNode}return{detached:!1,rendered:!0}}var i1='.resize-triggers{visibility:hidden;opacity:0;pointer-events:none}.resize-contract-trigger,.resize-contract-trigger:before,.resize-expand-trigger,.resize-triggers{content:"";position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden}.resize-contract-trigger,.resize-expand-trigger{background:#eee;overflow:auto}.resize-contract-trigger:before{width:200%;height:200%}',cs=0,Ii=null;function a1(e,t){e.__resize_mutation_handler__||(e.__resize_mutation_handler__=u1.bind(e));var r=e.__resize_listeners__;if(!r){if(e.__resize_listeners__=[],window.ResizeObserver){var n=e.offsetWidth,i=e.offsetHeight,a=new ResizeObserver(function(){!e.__resize_observer_triggered__&&(e.__resize_observer_triggered__=!0,e.offsetWidth===n&&e.offsetHeight===i)||ji(e)}),o=vs(e),s=o.detached,u=o.rendered;e.__resize_observer_triggered__=s===!1&&u===!1,e.__resize_observer__=a,a.observe(e)}else if(e.attachEvent&&e.addEventListener)e.__resize_legacy_resize_handler__=function(){ji(e)},e.attachEvent("onresize",e.__resize_legacy_resize_handler__),document.addEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);else if(cs||(Ii=n1(i1)),l1(e),e.__resize_rendered__=vs(e).rendered,window.MutationObserver){var l=new MutationObserver(e.__resize_mutation_handler__);l.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0}),e.__resize_mutation_observer__=l}}e.__resize_listeners__.push(t),cs++}function o1(e,t){var r=e.__resize_listeners__;if(r){if(t&&r.splice(r.indexOf(t),1),!r.length||!t){if(e.detachEvent&&e.removeEventListener){e.detachEvent("onresize",e.__resize_legacy_resize_handler__),document.removeEventListener("DOMSubtreeModified",e.__resize_mutation_handler__);return}e.__resize_observer__?(e.__resize_observer__.unobserve(e),e.__resize_observer__.disconnect(),e.__resize_observer__=null):(e.__resize_mutation_observer__&&(e.__resize_mutation_observer__.disconnect(),e.__resize_mutation_observer__=null),e.removeEventListener("scroll",nu),e.removeChild(e.__resize_triggers__.triggers),e.__resize_triggers__=null),e.__resize_listeners__=null}!--cs&&Ii&&Ii.parentNode.removeChild(Ii)}}function s1(e){var t=e.__resize_last__,r=t.width,n=t.height,i=e.offsetWidth,a=e.offsetHeight;return i!==r||a!==n?{width:i,height:a}:null}function u1(){var e=vs(this),t=e.rendered,r=e.detached;t!==this.__resize_rendered__&&(!r&&this.__resize_triggers__&&(iu(this),this.addEventListener("scroll",nu,!0)),this.__resize_rendered__=t,ji(this))}function nu(){var e=this;iu(this),this.__resize_raf__&&r1(this.__resize_raf__),this.__resize_raf__=e1(function(){var t=s1(e);t&&(e.__resize_last__=t,ji(e))})}function ji(e){!e||!e.__resize_listeners__||e.__resize_listeners__.forEach(function(t){t.call(e,e)})}function l1(e){var t=uc(e,"position");(!t||t==="static")&&(e.style.position="relative"),e.__resize_old_position__=t,e.__resize_last__={};var r=yi("div",{className:"resize-triggers"}),n=yi("div",{className:"resize-expand-trigger"}),i=yi("div"),a=yi("div",{className:"resize-contract-trigger"});n.appendChild(i),r.appendChild(n),r.appendChild(a),e.appendChild(r),e.__resize_triggers__={triggers:r,expand:n,expandChild:i,contract:a},iu(e),e.addEventListener("scroll",nu,!0),e.__resize_last__={width:e.offsetWidth,height:e.offsetHeight}}function iu(e){var t=e.__resize_triggers__,r=t.expand,n=t.expandChild,i=t.contract,a=i.scrollWidth,o=i.scrollHeight,s=r.offsetWidth,u=r.offsetHeight,l=r.scrollWidth,f=r.scrollHeight;i.scrollLeft=a,i.scrollTop=o,n.style.width=s+1+"px",n.style.height=u+1+"px",r.scrollLeft=l,r.scrollTop=f}var ne=function(){return ne=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},ne.apply(this,arguments)};var f1=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function h1(e){return t=Object.create(null),f1.forEach(function(r){t[r]=function(n){return function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[n].apply(e.value,i)}}(r)}),t;var t}var v1={autoresize:[Boolean,Object]},c1=/^on[^a-z]/,Nf=function(e){return c1.test(e)};function mi(e,t){var r=gc(e)?_c(e):e;return r&&typeof r=="object"&&"value"in r?r.value||t:r||t}var d1="ecLoadingOptions",p1={loading:Boolean,loadingOptions:Object},nn=null,lc="x-vue-echarts",Hf=[],an=[];(function(e,t){if(e&&typeof document<"u"){var r,n=t.prepend===!0?"prepend":"append",i=t.singleTag===!0,a=typeof t.container=="string"?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(i){var o=Hf.indexOf(a);o===-1&&(o=Hf.push(a)-1,an[o]={}),r=an[o]&&an[o][n]?an[o][n]:an[o][n]=s()}else r=s();e.charCodeAt(0)===65279&&(e=e.substring(1)),r.styleSheet?r.styleSheet.cssText+=e:r.appendChild(document.createTextNode(e))}function s(){var u=document.createElement("style");if(u.setAttribute("type","text/css"),t.attributes)for(var l=Object.keys(t.attributes),f=0;f<l.length;f++)u.setAttribute(l[f],t.attributes[l[f]]);var h=n==="prepend"?"afterbegin":"beforeend";return a.insertAdjacentElement(h,u),u}})(`x-vue-echarts{display:flex;flex-direction:column;width:100%;height:100%;min-width:0}
.vue-echarts-inner{flex-grow:1;min-width:0;width:auto!important;height:auto!important}
`,{});var g1=function(){if(nn!=null)return nn;if(typeof HTMLElement>"u"||typeof customElements>"u")return nn=!1;try{new Function("tag",`class EChartsElement extends HTMLElement {
  __dispose = null;

  disconnectedCallback() {
    if (this.__dispose) {
      this.__dispose();
      this.__dispose = null;
    }
  }
}

if (customElements.get(tag) == null) {
  customElements.define(tag, EChartsElement);
}
`)(lc)}catch{return nn=!1}return nn=!0}(),_1="ecTheme",y1="ecInitOptions",m1="ecUpdateOptions",Uf=/(^&?~?!?)native:/,Lw=fc({name:"echarts",props:ne(ne({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},v1),p1),emits:{},inheritAttrs:!1,setup:function(e,t){var r=t.attrs,n=Nn(),i=Nn(),a=Nn(),o=Nn(),s=Hn(_1,null),u=Hn(y1,null),l=Hn(m1,null),f=hc(e),h=f.autoresize,c=f.manualUpdate,v=f.loading,d=f.loadingOptions,_=hr(function(){return o.value||e.option||null}),p=hr(function(){return e.theme||mi(s,{})}),g=hr(function(){return e.initOptions||mi(u,{})}),y=hr(function(){return e.updateOptions||mi(l,{})}),m=hr(function(){return function(D){var R={};for(var I in D)Nf(I)||(R[I]=D[I]);return R}(r)}),w={},T=vc().proxy.$listeners,S={};function b(D){if(i.value){var R=a.value=Km(i.value,p.value,g.value);e.group&&(R.group=e.group),Object.keys(S).forEach(function(x){var O=S[x];if(O){var E=x.toLowerCase();E.charAt(0)==="~"&&(E=E.substring(1),O.__once__=!0);var V=R;if(E.indexOf("zr:")===0&&(V=R.getZr(),E=E.substring(3)),O.__once__){delete O.__once__;var q=O;O=function(){for(var J=[],j=0;j<arguments.length;j++)J[j]=arguments[j];q.apply(void 0,J),V.off(E,O)}}V.on(E,O)}}),h.value?pc(function(){R&&!R.isDisposed()&&R.resize(),I()}):I()}function I(){var x=D||_.value;x&&R.setOption(x,y.value)}}function C(){a.value&&(a.value.dispose(),a.value=void 0)}T?Object.keys(T).forEach(function(D){Uf.test(D)?w[D.replace(Uf,"$1")]=T[D]:S[D]=T[D]}):Object.keys(r).filter(function(D){return Nf(D)}).forEach(function(D){var R=D.charAt(2).toLowerCase()+D.slice(3);if(R.indexOf("native:")!==0)R.substring(R.length-4)==="Once"&&(R="~".concat(R.substring(0,R.length-4))),S[R]=r[D];else{var I="on".concat(R.charAt(7).toUpperCase()).concat(R.slice(8));w[I]=r[D]}});var M=null;Un(c,function(D){typeof M=="function"&&(M(),M=null),D||(M=Un(function(){return e.option},function(R,I){R&&(a.value?a.value.setOption(R,ne({notMerge:R!==I},y.value)):b())},{deep:!0}))},{immediate:!0}),Un([p,g],function(){C(),b()},{deep:!0}),uu(function(){e.group&&a.value&&(a.value.group=e.group)});var A=h1(a);return function(D,R,I){var x=Hn(d1,{}),O=hr(function(){return ne(ne({},mi(x,{})),I==null?void 0:I.value)});uu(function(){var E=D.value;E&&(R.value?E.showLoading(O.value):E.hideLoading())})}(a,v,d),function(D,R,I){var x=null;Un([I,D,R],function(O,E,V){var q=O[0],J=O[1],j=O[2];if(q&&J&&j){var ot=j===!0?{}:j,K=ot.throttle,pt=K===void 0?100:K,gt=ot.onResize,Yt=function(){J.resize(),gt==null||gt()};x=pt?Js(Yt,pt):Yt,a1(q,x)}V(function(){q&&x&&o1(q,x)})})}(a,h,i),cc(function(){b()}),dc(function(){g1&&n.value?n.value.__dispose=C:C()}),ne({chart:a,root:n,inner:i,setOption:function(D,R){e.manualUpdate&&(o.value=D),a.value?a.value.setOption(D,R||{}):b(D)},nonEventAttrs:m,nativeListeners:w},A)},render:function(){var e=ne(ne({},this.nonEventAttrs),this.nativeListeners);return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",su(lc,e,[su("div",{ref:"inner",class:"vue-echarts-inner"})])}});export{Q0 as $,p_ as A,ct as B,hy as C,X_ as D,Cs as E,_s as F,vw as G,wv as H,na as I,Ds as J,Vo as K,xr as L,Mt as M,O1 as N,yh as O,n_ as P,Iy as Q,De as R,or as S,hv as T,Z1 as U,hw as V,Q1 as W,r_ as X,J1 as Y,t_ as Z,N as _,L as a,S1 as a$,J0 as a0,j0 as a1,$1 as a2,Ur as a3,e_ as a4,K1 as a5,Kt as a6,aw as a7,ow as a8,iw as a9,Gr as aA,Aw as aB,Rw as aC,zn as aD,sc as aE,Pw as aF,Dw as aG,jm as aH,Jm as aI,P1 as aJ,Gd as aK,A1 as aL,R1 as aM,$ as aN,hl as aO,ks as aP,Le as aQ,Yc as aR,C1 as aS,u0 as aT,b1 as aU,ae as aV,_e as aW,Fu as aX,On as aY,ys as aZ,pf as a_,nw as aa,rw as ab,ew as ac,j1 as ad,Ys as ae,Gs as af,Vs as ag,Gi as ah,Us as ai,Sn as aj,cv as ak,tw as al,I1 as am,Pn as an,Ic as ao,tt as ap,nt as aq,bn as ar,En as as,Qs as at,rt as au,Mw as av,Cw as aw,xw as ax,t1 as ay,Ew as az,Ot as b,w0 as b$,mf as b0,us as b1,Am as b2,Eo as b3,U as b4,st as b5,Wf as b6,bw as b7,Hr as b8,gm as b9,B1 as bA,ov as bB,o0 as bC,D1 as bD,q1 as bE,dh as bF,d0 as bG,X1 as bH,Js as bI,Vp as bJ,fw as bK,wi as bL,cw as bM,x1 as bN,Wu as bO,ww as bP,ge as bQ,Yi as bR,dv as bS,Wi as bT,Jf as bU,Ir as bV,T1 as bW,E1 as bX,Rl as bY,L0 as bZ,va as b_,Xi as ba,Xo as bb,qo as bc,Jh as bd,V1 as be,c0 as bf,W1 as bg,Tw as bh,lr as bi,Y1 as bj,H1 as bk,Gt as bl,jh as bm,Sw as bn,av as bo,Br as bp,ah as bq,mt as br,Q as bs,ue as bt,Ls as bu,s_ as bv,U1 as bw,ia as bx,N1 as by,zr as bz,Z as c,Qf as c0,sd as c1,Si as c2,L1 as c3,D0 as c4,G1 as c5,A0 as c6,yw as c7,mw as c8,nd as c9,Ag as cA,lw as cB,Af as cC,Fr as cD,Lw as cE,cn as ca,gu as cb,nv as cc,i_ as cd,sw as ce,Mo as cf,qt as cg,M1 as ch,gw as ci,xt as cj,F1 as ck,di as cl,rs as cm,ce as cn,_w as co,dw as cp,pw as cq,At as cr,a_ as cs,Ch as ct,aa as cu,Vt as cv,uw as cw,bh as cx,xe as cy,ws as cz,H as d,P as e,ut as f,H_ as g,It as h,B as i,cy as j,k1 as k,ur as l,X as m,F as n,ie as o,it as p,z1 as q,Y as r,W_ as s,at as t,Xf as u,W as v,me as w,U_ as x,Lt as y,wt as z};
