<!DOCTYPE html>
<html>
<head>
    <title>会员码删除测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>会员码删除功能测试</h1>
    
    <button onclick="getMemberCodes()">1. 获取会员码列表</button>
    <button onclick="testDeleteCode()">2. 测试删除会员码</button>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'https://api.dznovel.top/api';
        let memberCodes = [];
        
        function addResult(title, success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<h3>${title}</h3><pre>${message}</pre>`;
            document.getElementById('results').appendChild(div);
        }
        
        async function getMemberCodes() {
            try {
                const response = await fetch(`${API_BASE}/member-codes?_api_path=member-codes&page=1&size=5`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    memberCodes = data.data.memberCodes;
                    addResult('获取会员码列表', true, 
                        `成功获取 ${memberCodes.length} 个会员码:\n` + 
                        memberCodes.map(code => `- ${code.code} (ID: ${code.id})`).join('\n')
                    );
                } else {
                    addResult('获取会员码列表', false, `失败: ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                addResult('获取会员码列表', false, `异常: ${error.message}`);
            }
        }
        
        async function testDeleteCode() {
            if (memberCodes.length === 0) {
                addResult('删除会员码', false, '请先获取会员码列表');
                return;
            }
            
            const codeToDelete = memberCodes[0];
            
            try {
                const response = await fetch(`${API_BASE}/member-codes/${codeToDelete.code}?_api_path=member-codes/${codeToDelete.code}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('删除会员码', true, `成功删除会员码: ${codeToDelete.code}\n响应: ${data.message}`);
                } else {
                    addResult('删除会员码', false, 
                        `删除失败 (状态码: ${response.status}):\n${JSON.stringify(data, null, 2)}`
                    );
                }
            } catch (error) {
                addResult('删除会员码', false, `异常: ${error.message}`);
            }
        }
    </script>
</body>
</html>
