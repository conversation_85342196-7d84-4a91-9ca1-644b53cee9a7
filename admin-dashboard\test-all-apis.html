<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台管理系统 - 完整API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f8fff8; }
        .error { border-color: #f44336; background-color: #fff8f8; }
        .pending { border-color: #ff9800; background-color: #fff8f0; }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px;
            font-size: 12px;
        }
        button:hover { background: #66b1ff; }
        .log {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
            font-size: 11px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>🔧 后台管理系统 - 完整API测试</h1>
    
    <div class="grid">
        <!-- 基础API测试 -->
        <div class="container">
            <h2>🏥 基础API</h2>
            
            <div class="test-item pending" id="health-test">
                <h4>健康检查</h4>
                <button onclick="testHealthCheck()">测试</button>
                <div class="log" id="health-log">等待测试...</div>
            </div>

            <div class="test-item pending" id="dashboard-test">
                <h4>仪表板数据</h4>
                <button onclick="testDashboard()">测试</button>
                <div class="log" id="dashboard-log">等待测试...</div>
            </div>
        </div>

        <!-- 用户管理API -->
        <div class="container">
            <h2>👥 用户管理</h2>
            
            <div class="test-item pending" id="users-test">
                <h4>用户列表</h4>
                <button onclick="testUsers()">测试</button>
                <div class="log" id="users-log">等待测试...</div>
            </div>

            <div class="test-item pending" id="members-stats-test">
                <h4>会员统计</h4>
                <button onclick="testMembersStats()">测试</button>
                <div class="log" id="members-stats-log">等待测试...</div>
            </div>
        </div>

        <!-- 小说管理API -->
        <div class="container">
            <h2>📚 小说管理</h2>
            
            <div class="test-item pending" id="novels-test">
                <h4>小说列表</h4>
                <button onclick="testNovels()">测试</button>
                <div class="log" id="novels-log">等待测试...</div>
            </div>

            <div class="test-item pending" id="novels-stats-test">
                <h4>小说统计</h4>
                <button onclick="testNovelsStats()">测试</button>
                <div class="log" id="novels-stats-log">等待测试...</div>
            </div>
        </div>

        <!-- 会员码管理API -->
        <div class="container">
            <h2>🎫 会员码管理</h2>
            
            <div class="test-item pending" id="member-codes-test">
                <h4>会员码列表</h4>
                <button onclick="testMemberCodes()">测试</button>
                <div class="log" id="member-codes-log">等待测试...</div>
            </div>
        </div>

        <!-- 同步管理API -->
        <div class="container">
            <h2>🔄 同步管理</h2>
            
            <div class="test-item pending" id="sync-stats-test">
                <h4>同步统计</h4>
                <button onclick="testSyncStats()">测试</button>
                <div class="log" id="sync-stats-log">等待测试...</div>
            </div>

            <div class="test-item pending" id="sync-records-test">
                <h4>同步记录</h4>
                <button onclick="testSyncRecords()">测试</button>
                <div class="log" id="sync-records-log">等待测试...</div>
            </div>
        </div>

        <!-- 管理员API -->
        <div class="container">
            <h2>🔐 管理员功能</h2>
            
            <div class="test-item pending" id="admin-login-test">
                <h4>管理员登录</h4>
                <button onclick="testAdminLogin()">测试</button>
                <div class="log" id="admin-login-log">等待测试...</div>
            </div>
        </div>
    </div>

    <div class="container">
        <button onclick="runAllTests()" style="background: #67C23A; font-size: 16px; padding: 15px 30px;">
            🚀 运行所有测试
        </button>
        <button onclick="clearAllLogs()" style="background: #909399; font-size: 16px; padding: 15px 30px;">
            🧹 清空日志
        </button>
    </div>

    <script>
        const API_BASE_URL = 'https://api.dznovel.top/api';
        
        function addApiPathParam(url) {
            const separator = url.includes('?') ? '&' : '?';
            const apiPath = url.startsWith('/') ? url.substring(1) : url;
            return `${url}${separator}_api_path=${apiPath}`;
        }

        async function makeRequest(endpoint, options = {}) {
            const url = addApiPathParam(`${API_BASE_URL}${endpoint}`);
            console.log('🔍 请求URL:', url);
            
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            return response;
        }

        function updateTestResult(testId, success, message) {
            const logEl = document.getElementById(`${testId}-log`);
            const testEl = document.getElementById(`${testId}-test`);
            
            logEl.textContent = message;
            testEl.className = `test-item ${success ? 'success' : 'error'}`;
        }

        // 测试函数
        async function testHealthCheck() {
            try {
                updateTestResult('health', null, '🔍 正在测试健康检查...');
                const response = await makeRequest('/health');
                const data = await response.json();
                
                if (response.ok) {
                    updateTestResult('health', true, `✅ 健康检查成功！\n状态: ${data.status}\n消息: ${data.message}`);
                } else {
                    updateTestResult('health', false, `❌ 健康检查失败\n状态码: ${response.status}`);
                }
            } catch (error) {
                updateTestResult('health', false, `❌ 健康检查异常: ${error.message}`);
            }
        }

        async function testDashboard() {
            try {
                updateTestResult('dashboard', null, '🔍 正在测试仪表板数据...');
                const response = await makeRequest('/dashboard');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('dashboard', true, `✅ 仪表板数据获取成功！\n用户数: ${data.data.stats.totalUsers}\n小说数: ${data.data.stats.totalNovels}\n会员数: ${data.data.stats.memberUsers}`);
                } else {
                    updateTestResult('dashboard', false, `❌ 仪表板数据获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('dashboard', false, `❌ 仪表板数据异常: ${error.message}`);
            }
        }

        async function testUsers() {
            try {
                updateTestResult('users', null, '🔍 正在测试用户列表...');
                const response = await makeRequest('/users?page=1&size=10');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('users', true, `✅ 用户列表获取成功！\n用户数量: ${data.data.users.length}\n总数: ${data.data.total}`);
                } else {
                    updateTestResult('users', false, `❌ 用户列表获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('users', false, `❌ 用户列表异常: ${error.message}`);
            }
        }

        async function testMembersStats() {
            try {
                updateTestResult('members-stats', null, '🔍 正在测试会员统计...');
                const response = await makeRequest('/members/stats');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('members-stats', true, `✅ 会员统计获取成功！\n总用户: ${data.data.totalUsers}\n会员数: ${data.data.totalMembers}\n会员率: ${data.data.membershipRate}%`);
                } else {
                    updateTestResult('members-stats', false, `❌ 会员统计获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('members-stats', false, `❌ 会员统计异常: ${error.message}`);
            }
        }

        async function testNovels() {
            try {
                updateTestResult('novels', null, '🔍 正在测试小说列表...');
                const response = await makeRequest('/novels?page=1&size=10');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('novels', true, `✅ 小说列表获取成功！\n小说数量: ${data.data.length}\n总数: ${data.pagination.total}`);
                } else {
                    updateTestResult('novels', false, `❌ 小说列表获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('novels', false, `❌ 小说列表异常: ${error.message}`);
            }
        }

        async function testNovelsStats() {
            try {
                updateTestResult('novels-stats', null, '🔍 正在测试小说统计...');
                const response = await makeRequest('/novels/stats');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('novels-stats', true, `✅ 小说统计获取成功！\n总小说: ${data.data.totalNovels}\n总字数: ${data.data.totalWords}\n总章节: ${data.data.totalChapters}`);
                } else {
                    updateTestResult('novels-stats', false, `❌ 小说统计获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('novels-stats', false, `❌ 小说统计异常: ${error.message}`);
            }
        }

        async function testMemberCodes() {
            try {
                updateTestResult('member-codes', null, '🔍 正在测试会员码列表...');
                const response = await makeRequest('/member-codes?page=1&size=10');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('member-codes', true, `✅ 会员码列表获取成功！\n会员码数量: ${data.data.length}\n总数: ${data.pagination.total}`);
                } else {
                    updateTestResult('member-codes', false, `❌ 会员码列表获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('member-codes', false, `❌ 会员码列表异常: ${error.message}`);
            }
        }

        async function testSyncStats() {
            try {
                updateTestResult('sync-stats', null, '🔍 正在测试同步统计...');
                const response = await makeRequest('/sync/stats');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('sync-stats', true, `✅ 同步统计获取成功！\n总同步记录: ${data.data.totalSyncRecords}\n今日同步: ${data.data.todaySyncs}\n总数据大小: ${data.data.totalDataSize}`);
                } else {
                    updateTestResult('sync-stats', false, `❌ 同步统计获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('sync-stats', false, `❌ 同步统计异常: ${error.message}`);
            }
        }

        async function testSyncRecords() {
            try {
                updateTestResult('sync-records', null, '🔍 正在测试同步记录...');
                const response = await makeRequest('/sync/records?page=1&size=10');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('sync-records', true, `✅ 同步记录获取成功！\n记录数量: ${data.data.length}\n总数: ${data.pagination.total}`);
                } else {
                    updateTestResult('sync-records', false, `❌ 同步记录获取失败\n${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                updateTestResult('sync-records', false, `❌ 同步记录异常: ${error.message}`);
            }
        }

        async function testAdminLogin() {
            try {
                updateTestResult('admin-login', null, '🔍 正在测试管理员登录...');
                const response = await makeRequest('/admin/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                
                if (response.ok && data.success) {
                    updateTestResult('admin-login', true, `✅ 管理员登录成功！\n用户: ${data.data.user.username}\n角色: ${data.data.user.role}`);
                } else {
                    updateTestResult('admin-login', false, `❌ 管理员登录失败\n${data.message}`);
                }
            } catch (error) {
                updateTestResult('admin-login', false, `❌ 管理员登录异常: ${error.message}`);
            }
        }

        async function runAllTests() {
            const tests = [
                testHealthCheck,
                testDashboard,
                testUsers,
                testMembersStats,
                testNovels,
                testNovelsStats,
                testMemberCodes,
                testSyncStats,
                testSyncRecords,
                testAdminLogin
            ];

            for (let i = 0; i < tests.length; i++) {
                await tests[i]();
                if (i < tests.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
        }

        function clearAllLogs() {
            const logs = document.querySelectorAll('.log');
            const tests = document.querySelectorAll('.test-item');
            
            logs.forEach(log => log.textContent = '等待测试...');
            tests.forEach(test => test.className = 'test-item pending');
        }

        // 页面加载时显示配置信息
        window.onload = function() {
            console.log('🔧 后台管理系统完整API测试');
            console.log('API地址:', API_BASE_URL);
        };
    </script>
</body>
</html>
