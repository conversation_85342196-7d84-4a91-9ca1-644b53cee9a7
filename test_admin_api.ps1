# 测试后台管理系统API

Write-Host "🔍 测试后台管理系统API..." -ForegroundColor Green

# 测试仪表板API
Write-Host "`n📊 测试仪表板API..."
try {
    $response = Invoke-WebRequest -Uri "https://api.dznovel.top/api/dashboard?_api_path=dashboard" -Method GET -TimeoutSec 10
    Write-Host "✅ 仪表板API成功: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应长度: $($response.Content.Length) 字符"
} catch {
    Write-Host "❌ 仪表板API失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试用户列表API
Write-Host "`n👥 测试用户列表API..."
try {
    $response = Invoke-WebRequest -Uri "https://api.dznovel.top/api/users?_api_path=users" -Method GET -TimeoutSec 10
    Write-Host "✅ 用户列表API成功: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应长度: $($response.Content.Length) 字符"
} catch {
    Write-Host "❌ 用户列表API失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试管理员登录API
Write-Host "`n🔐 测试管理员登录API..."
try {
    $body = '{"username":"admin","password":"admin123"}'
    $response = Invoke-WebRequest -Uri "https://api.dznovel.top/api/admin/login?_api_path=admin/login" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10
    Write-Host "✅ 管理员登录API成功: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "响应: $($response.Content)"
} catch {
    Write-Host "❌ 管理员登录API失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API测试完成！" -ForegroundColor Green
