import 'dart:io';
import 'dart:convert';

/// 测试API连接的简单脚本
void main() async {
  print('🚀 测试岱宗文脉API连接...\n');

  final client = HttpClient();
  
  // 测试用例
  final tests = [
    {
      'name': '健康检查接口',
      'url': 'https://api.dznovel.top/api/health?_api_path=health',
      'method': 'GET',
    },
    {
      'name': '套餐接口',
      'url': 'https://api.dznovel.top/api/packages?_api_path=packages',
      'method': 'GET',
    },
    {
      'name': '根路径',
      'url': 'https://api.dznovel.top/api/',
      'method': 'GET',
    },
  ];

  int successCount = 0;
  int totalCount = tests.length;

  for (final test in tests) {
    print('🔍 测试: ${test['name']}');
    print('   URL: ${test['url']}');
    
    try {
      final request = await client.getUrl(Uri.parse(test['url'] as String));
      request.headers.set('Accept', 'application/json');
      request.headers.set('User-Agent', 'DzNovel-Flutter-Test/1.0');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        print('   ✅ 成功 - 状态码: ${response.statusCode}');
        
        try {
          final jsonData = json.decode(responseBody);
          if (jsonData is Map) {
            if (jsonData.containsKey('status')) {
              print('   📄 健康状态: ${jsonData['status']}');
            } else if (jsonData.containsKey('data') && jsonData['data'] is List) {
              final packages = jsonData['data'] as List;
              print('   📄 套餐数量: ${packages.length}');
              if (packages.isNotEmpty) {
                print('   📄 第一个套餐: ${packages[0]['name']}');
              }
            } else if (jsonData.containsKey('message')) {
              print('   📄 消息: ${jsonData['message']}');
            }
          }
        } catch (e) {
          print('   📄 响应: ${responseBody.substring(0, 100)}...');
        }
        
        successCount++;
      } else {
        print('   ❌ 失败 - 状态码: ${response.statusCode}');
        print('   📄 错误: ${responseBody.substring(0, 100)}...');
      }
    } catch (e) {
      print('   ❌ 网络错误: $e');
    }
    
    print('');
  }

  client.close();

  // 生成测试报告
  print('📊 测试结果汇总:');
  print('=' * 50);
  print('✅ 成功: $successCount/$totalCount');
  print('❌ 失败: ${totalCount - successCount}/$totalCount');
  
  if (successCount == totalCount) {
    print('\n🎉 所有API测试通过！Flutter应用可以正常连接到后端服务。');
  } else {
    print('\n⚠️  部分API测试失败，请检查网络连接和服务器状态。');
  }
  
  print('\n💡 Flutter应用配置:');
  print('   - 生产环境API地址: https://api.dznovel.top/api');
  print('   - 查询参数方式: ?_api_path=<endpoint>');
  print('   - 所有API端点都已更新为使用查询参数格式');
}
