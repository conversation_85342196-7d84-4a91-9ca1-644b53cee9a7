import{_ as fe}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                   *//* empty css                      *//* empty css                             *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                 *//* empty css                   *//* empty css                *//* empty css               */import{d as ge,r as x,a as q,o as he,c as W,b as o,e,w as a,W as ye,f as be,m as we,a2 as Ce,q as xe,A as ke,a4 as Se,H as ze,a5 as Ee,a6 as Ve,v as m,Z as De,i as V,X as $e,E as Te,B as g,t as n,a7 as Be,h as d,_ as Ue,$ as Ae,aa as Me,a9 as Ie,I as Ne,ab as Re,ac as Pe,a0 as D,u as je,U as qe}from"./index-BQm3CBcS.js";const We={class:"novels-container"},Fe={class:"stat-card"},Le={class:"stat-icon"},Ye={class:"stat-content"},He={class:"stat-value"},Oe={class:"stat-card"},Ke={class:"stat-icon"},Ge={class:"stat-content"},Je={class:"stat-value"},Xe={class:"stat-card"},Ze={class:"stat-icon"},Qe={class:"stat-content"},et={class:"stat-value"},tt={class:"stat-card"},at={class:"stat-icon"},st={class:"stat-content"},ot={class:"stat-value"},lt={class:"dashboard-card"},nt={class:"search-bar"},dt={class:"search-left"},it={class:"search-right"},rt={class:"dashboard-card"},ct={class:"novel-title"},ut={class:"pagination-container"},pt={key:0},_t={style:{"margin-top":"20px"}},mt={class:"analysis-item"},vt={class:"analysis-value"},ft={class:"analysis-item"},gt={class:"analysis-value"},ht={class:"analysis-item"},yt={class:"analysis-value"},bt=ge({__name:"index",setup(wt){const $=je(),S=x(!1),h=x([]),z=x(!1),c=x(null),p=q({keyword:"",genre:"",status:""}),u=q({page:1,size:20,total:0}),k=x({total:0,totalWords:0,todayCreated:0,avgWords:0}),T=x([]),F=s=>({科幻:"primary",历史:"success",都市:"warning",玄幻:"danger",其他:"info"})[s]||"info",L=s=>({ongoing:"success",completed:"primary",paused:"warning"})[s]||"info",Y=s=>({ongoing:"连载中",completed:"已完结",paused:"暂停"})[s]||"未知",E=s=>!s||s===0?"0":s>=1e4?(s/1e4).toFixed(1)+"万":s.toString(),B=s=>D(s).format("YYYY-MM-DD HH:mm"),H=s=>{const t=U(s),r=(s.chapterCount||1)/t;return r>=1?`${r.toFixed(1)}章/天`:`${(1/r).toFixed(1)}天/章`},U=s=>{const t=D(s.createdAt);return D(s.updatedAt).diff(t,"day")+1},A=()=>{u.page=1,y()},O=()=>{p.keyword="",p.genre="",p.status="",u.page=1,y()},K=s=>{h.value=s},G=async()=>{if(h.value.length===0){m.warning("请选择要删除的小说");return}try{await qe.confirm(`确定要删除选中的 ${h.value.length} 部小说吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const s=h.value.map(r=>r.id),t=await fetch("/api/novels/batch-delete",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({novelIds:s})}),i=await t.json();t.ok&&i.success?(m.success(i.message),h.value=[],y()):m.error(i.message||"批量删除失败")}catch(s){s.message&&(console.error("批量删除失败:",s),m.error("批量删除失败"))}},J=()=>{m.info("导出功能开发中...")},M=s=>{$.push(`/novels/${s.id}`)},X=s=>{$.push(`/users/${s}`)},Z=s=>{c.value=s,z.value=!0},Q=async s=>{try{const t=await fetch(`/api/novels/${s.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}}),i=await t.json();t.ok&&i.success?(m.success(i.message),y()):m.error(i.message||"删除失败")}catch(t){console.error("删除小说失败:",t),m.error("删除失败")}},ee=s=>{u.size=s,u.page=1,y()},te=s=>{u.page=s,y()},ae=async()=>{try{const s=await fetch("/api/novels/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(s.ok){const t=await s.json();t.success&&(k.value=t.data)}}catch(s){console.error("加载小说统计失败:",s)}},y=async()=>{S.value=!0;try{const s=new URLSearchParams({page:u.page.toString(),size:u.size.toString(),keyword:p.keyword,genre:p.genre,status:p.status}),t=await fetch(`/api/novels?${s}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(t.ok){const i=await t.json();i.success?(T.value=i.data.novels||[],u.total=i.data.total||0):m.error(i.message||"加载小说列表失败")}else m.error("网络请求失败")}catch(s){console.error("加载小说列表失败:",s),m.error("加载小说列表失败")}finally{S.value=!1}};return he(()=>{ae(),y()}),(s,t)=>{const i=g("Reading"),r=Te,b=$e,se=g("EditPen"),oe=g("Star"),le=g("TrendCharts"),I=ye,N=g("Search"),ne=we,v=Be,R=Ce,f=xe,de=g("Refresh"),ie=g("Delete"),re=g("Download"),_=Ue,P=Ae,j=Me,ce=Ie,ue=De,pe=Ee,w=Pe,_e=Re,me=Ve,ve=Se;return V(),W("div",We,[t[21]||(t[21]=o("div",{class:"page-header"},[o("h1",{class:"page-title"},"小说管理"),o("p",{class:"page-subtitle"},"管理系统中的所有小说内容")],-1)),e(I,{gutter:20,class:"stats-row"},{default:a(()=>[e(b,{xs:24,sm:6},{default:a(()=>[o("div",Fe,[o("div",Le,[e(r,{size:"32",color:"#1890ff"},{default:a(()=>[e(i)]),_:1})]),o("div",Ye,[o("div",He,n(k.value.total),1),t[7]||(t[7]=o("div",{class:"stat-label"},"小说总数",-1))])])]),_:1}),e(b,{xs:24,sm:6},{default:a(()=>[o("div",Oe,[o("div",Ke,[e(r,{size:"32",color:"#52c41a"},{default:a(()=>[e(se)]),_:1})]),o("div",Ge,[o("div",Je,n(E(k.value.totalWords)),1),t[8]||(t[8]=o("div",{class:"stat-label"},"总字数",-1))])])]),_:1}),e(b,{xs:24,sm:6},{default:a(()=>[o("div",Xe,[o("div",Ze,[e(r,{size:"32",color:"#faad14"},{default:a(()=>[e(oe)]),_:1})]),o("div",Qe,[o("div",et,n(k.value.todayCreated),1),t[9]||(t[9]=o("div",{class:"stat-label"},"今日新增",-1))])])]),_:1}),e(b,{xs:24,sm:6},{default:a(()=>[o("div",tt,[o("div",at,[e(r,{size:"32",color:"#f5222d"},{default:a(()=>[e(le)]),_:1})]),o("div",st,[o("div",ot,n(k.value.avgWords),1),t[10]||(t[10]=o("div",{class:"stat-label"},"平均字数",-1))])])]),_:1})]),_:1}),o("div",lt,[o("div",nt,[o("div",dt,[e(ne,{modelValue:p.keyword,"onUpdate:modelValue":t[0]||(t[0]=l=>p.keyword=l),placeholder:"搜索小说标题、作者",style:{width:"300px"},clearable:"",onKeyup:be(A,["enter"])},{prefix:a(()=>[e(r,null,{default:a(()=>[e(N)]),_:1})]),_:1},8,["modelValue"]),e(R,{modelValue:p.genre,"onUpdate:modelValue":t[1]||(t[1]=l=>p.genre=l),placeholder:"小说类型",style:{width:"120px"},clearable:""},{default:a(()=>[e(v,{label:"全部",value:""}),e(v,{label:"都市",value:"都市"}),e(v,{label:"玄幻",value:"玄幻"}),e(v,{label:"科幻",value:"科幻"}),e(v,{label:"历史",value:"历史"}),e(v,{label:"其他",value:"其他"})]),_:1},8,["modelValue"]),e(R,{modelValue:p.status,"onUpdate:modelValue":t[2]||(t[2]=l=>p.status=l),placeholder:"状态筛选",style:{width:"120px"},clearable:""},{default:a(()=>[e(v,{label:"全部",value:""}),e(v,{label:"连载中",value:"ongoing"}),e(v,{label:"已完结",value:"completed"}),e(v,{label:"暂停",value:"paused"})]),_:1},8,["modelValue"]),e(f,{type:"primary",onClick:A},{default:a(()=>[e(r,null,{default:a(()=>[e(N)]),_:1}),t[11]||(t[11]=d(" 搜索 "))]),_:1,__:[11]}),e(f,{onClick:O},{default:a(()=>[e(r,null,{default:a(()=>[e(de)]),_:1}),t[12]||(t[12]=d(" 重置 "))]),_:1,__:[12]})]),o("div",it,[e(f,{type:"danger",disabled:h.value.length===0,onClick:G},{default:a(()=>[e(r,null,{default:a(()=>[e(ie)]),_:1}),d(" 批量删除 ("+n(h.value.length)+") ",1)]),_:1},8,["disabled"]),e(f,{onClick:J},{default:a(()=>[e(r,null,{default:a(()=>[e(re)]),_:1}),t[13]||(t[13]=d(" 导出数据 "))]),_:1,__:[13]})])])]),o("div",rt,[ke((V(),ze(ue,{data:T.value,onSelectionChange:K,style:{width:"100%"}},{default:a(()=>[e(_,{type:"selection",width:"55"}),e(_,{prop:"title",label:"小说标题",width:"200","show-overflow-tooltip":""},{default:a(({row:l})=>[o("div",ct,[e(f,{type:"text",onClick:C=>M(l)},{default:a(()=>[d(n(l.title),1)]),_:2},1032,["onClick"])])]),_:1}),e(_,{prop:"author",label:"作者",width:"120"},{default:a(({row:l})=>[e(f,{type:"text",onClick:C=>X(l.userId)},{default:a(()=>[d(n(l.author),1)]),_:2},1032,["onClick"])]),_:1}),e(_,{prop:"genre",label:"类型",width:"100"},{default:a(({row:l})=>[e(P,{size:"small",type:F(l.genre)},{default:a(()=>[d(n(l.genre),1)]),_:2},1032,["type"])]),_:1}),e(_,{label:"字数",width:"100"},{default:a(({row:l})=>[d(n(E(l.wordCount)),1)]),_:1}),e(_,{label:"章节数",width:"80"},{default:a(({row:l})=>[d(n(l.chapterCount||0),1)]),_:1}),e(_,{label:"状态",width:"100"},{default:a(({row:l})=>[e(P,{size:"small",type:L(l.status)},{default:a(()=>[d(n(Y(l.status)),1)]),_:2},1032,["type"])]),_:1}),e(_,{label:"质量评分",width:"120"},{default:a(({row:l})=>[e(j,{modelValue:l.qualityScore,"onUpdate:modelValue":C=>l.qualityScore=C,max:5,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),e(_,{prop:"createdAt",label:"创建时间",width:"160"},{default:a(({row:l})=>[d(n(B(l.createdAt)),1)]),_:1}),e(_,{prop:"updatedAt",label:"最后更新",width:"160"},{default:a(({row:l})=>[d(n(B(l.updatedAt)),1)]),_:1}),e(_,{label:"操作",width:"200",fixed:"right"},{default:a(({row:l})=>[e(f,{type:"primary",size:"small",onClick:C=>M(l)},{default:a(()=>t[14]||(t[14]=[d(" 详情 ")])),_:2,__:[14]},1032,["onClick"]),e(f,{type:"warning",size:"small",onClick:C=>Z(l)},{default:a(()=>t[15]||(t[15]=[d(" 分析 ")])),_:2,__:[15]},1032,["onClick"]),e(ce,{title:"确定要删除这部小说吗？",onConfirm:C=>Q(l)},{reference:a(()=>[e(f,{type:"danger",size:"small"},{default:a(()=>t[16]||(t[16]=[d(" 删除 ")])),_:1,__:[16]})]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[ve,S.value]]),o("div",ut,[e(pe,{"current-page":u.page,"onUpdate:currentPage":t[3]||(t[3]=l=>u.page=l),"page-size":u.size,"onUpdate:pageSize":t[4]||(t[4]=l=>u.size=l),"page-sizes":[10,20,50,100],total:u.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ee,onCurrentChange:te},null,8,["current-page","page-size","total"])])]),e(me,{modelValue:z.value,"onUpdate:modelValue":t[6]||(t[6]=l=>z.value=l),title:"小说质量分析",width:"800px"},{default:a(()=>[c.value?(V(),W("div",pt,[e(_e,{column:2,border:""},{default:a(()=>[e(w,{label:"小说标题"},{default:a(()=>[d(n(c.value.title),1)]),_:1}),e(w,{label:"作者"},{default:a(()=>[d(n(c.value.author),1)]),_:1}),e(w,{label:"类型"},{default:a(()=>[d(n(c.value.genre),1)]),_:1}),e(w,{label:"字数"},{default:a(()=>[d(n(E(c.value.wordCount)),1)]),_:1}),e(w,{label:"章节数"},{default:a(()=>[d(n(c.value.chapterCount||0),1)]),_:1}),e(w,{label:"质量评分"},{default:a(()=>[e(j,{modelValue:c.value.qualityScore,"onUpdate:modelValue":t[5]||(t[5]=l=>c.value.qualityScore=l),disabled:"","show-score":""},null,8,["modelValue"])]),_:1})]),_:1}),o("div",_t,[t[20]||(t[20]=o("h4",null,"内容分析",-1)),e(I,{gutter:20},{default:a(()=>[e(b,{span:8},{default:a(()=>[o("div",mt,[t[17]||(t[17]=o("div",{class:"analysis-label"},"平均章节字数",-1)),o("div",vt,n(Math.round(c.value.wordCount/(c.value.chapterCount||1))),1)])]),_:1}),e(b,{span:8},{default:a(()=>[o("div",ft,[t[18]||(t[18]=o("div",{class:"analysis-label"},"更新频率",-1)),o("div",gt,n(H(c.value)),1)])]),_:1}),e(b,{span:8},{default:a(()=>[o("div",ht,[t[19]||(t[19]=o("div",{class:"analysis-label"},"创作天数",-1)),o("div",yt,n(U(c.value))+"天",1)])]),_:1})]),_:1})])])):Ne("",!0)]),_:1},8,["modelValue"])])}}}),At=fe(bt,[["__scopeId","data-v-15a3198a"]]);export{At as default};
