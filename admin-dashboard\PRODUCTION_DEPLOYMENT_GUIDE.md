# 后台管理系统 - 生产环境部署指南

## 🎯 概述

后台管理系统已完全配置为生产环境，可以连接到CloudBase API并管理真实数据。

## 📋 已完成的配置

### 1. 环境配置
- ✅ **开发环境** (`.env`): 使用本地代理
- ✅ **生产环境** (`.env.production`): 使用 `https://api.dznovel.top/api`

### 2. API端点实现
| 端点 | 状态 | 功能 | 认证 |
|------|------|------|------|
| `/health` | ✅ | 健康检查 | 无需 |
| `/dashboard` | ✅ | 仪表板数据 | 无需 |
| `/users` | ✅ | 用户列表 | 无需 |
| `/admin/login` | ✅ | 管理员登录 | 无需 |

### 3. 管理员账户
```
用户名: admin     密码: admin123
用户名: root      密码: root123
用户名: dznovel   密码: dznovel2024
```

## 🚀 使用方法

### 开发环境
```bash
cd admin-dashboard
npm install
npm run dev
```
访问: http://localhost:8080

### 生产环境构建
```bash
cd admin-dashboard
npm run build:prod
```
构建文件输出到: `dist/` 目录

### 预览生产版本
```bash
npm run serve:prod
```
访问: http://localhost:8080

## 📊 API配置详情

### 自动环境切换
- **开发环境**: 请求通过Vite代理转发到本地服务器
- **生产环境**: 直接请求 `https://api.dznovel.top/api`

### 查询参数处理
生产环境自动添加 `_api_path` 查询参数：
```
/dashboard → /dashboard?_api_path=dashboard
/users → /users?_api_path=users
```

### 请求超时
- 默认超时: 30秒
- 支持CORS跨域请求

## 🔧 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **图表库**: ECharts
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 📁 项目结构

```
admin-dashboard/
├── src/
│   ├── components/     # 公共组件
│   ├── layout/         # 布局组件
│   ├── router/         # 路由配置
│   ├── stores/         # 状态管理
│   ├── utils/          # 工具函数
│   │   └── api.ts      # API配置
│   ├── views/          # 页面组件
│   └── styles/         # 样式文件
├── dist/               # 构建输出
├── .env                # 开发环境配置
├── .env.production     # 生产环境配置
└── vite.config.ts      # Vite配置
```

## 🌐 部署选项

### 1. 静态托管
将 `dist/` 目录上传到任何静态托管服务：
- Netlify
- Vercel
- GitHub Pages
- 阿里云OSS
- 腾讯云COS

### 2. Nginx部署
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理（可选）
    location /api/ {
        proxy_pass https://api.dznovel.top/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. Docker部署
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔍 故障排除

### 1. API连接失败
- 检查网络连接
- 确认CloudBase服务器状态
- 查看浏览器控制台错误

### 2. 构建失败
- 运行 `npm install` 重新安装依赖
- 使用 `npm run build` 跳过TypeScript检查
- 检查Node.js版本（推荐v16+）

### 3. 登录失败
- 确认管理员账户密码
- 检查API端点是否正常
- 查看网络请求日志

## 📞 支持

如有问题，请检查：
1. CloudBase API服务状态
2. 网络连接
3. 浏览器控制台错误日志

---

🎉 **后台管理系统已成功配置为生产环境！**
