import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import '../config/api_config.dart';

/// CloudBase真实数据同步服务
/// 使用CloudBase云函数API实现真正的数据同步
class CloudBaseSyncService {
  static CloudBaseSyncService? _instance;
  static CloudBaseSyncService get instance {
    // 强制重新创建实例以确保使用最新配置
    _instance = CloudBaseSyncService._();
    return _instance!;
  }

  CloudBaseSyncService._() {
    print('🔧 CloudBaseSyncService 初始化');
    print('🔧 配置的baseUrl: ${ApiConfig.baseUrl}');
    print('🔧 配置的envId: $_envId');
    print('🔧 当前时间: ${DateTime.now()}');
  }

  // CloudBase配置 - 使用ApiConfig动态配置
  String get _baseUrl => ApiConfig.baseUrl;
  final String _envId = 'novel-app-2gywkgnn15cbd6a8';

  // 调试：确保使用正确的URL
  String get debugBaseUrl {
    print('🔍 DEBUG: _baseUrl = $_baseUrl');
    return _baseUrl;
  }

  /// 上传数据到CloudBase
  Future<bool> uploadData(Map<String, dynamic> data, String authToken) async {
    try {
      print('🚀 开始真实CloudBase数据上传...');
      
      // 分析数据大小
      final dataString = jsonEncode(data);
      final dataSizeMB = dataString.length / (1024 * 1024);
      print('📊 数据大小: ${dataSizeMB.toStringAsFixed(2)} MB');

      // 如果数据大于1MB，使用分批上传（CloudBase限制较严格）
      if (dataSizeMB > 1.0) {
        print('📦 数据较大，使用分批上传...');
        return await _uploadInBatches(data, authToken);
      } else {
        print('✅ 数据大小适中，直接上传');
        return await _uploadDirectly(data, authToken);
      }

    } catch (e) {
      print('❌ CloudBase数据上传异常: $e');
      return false;
    }
  }

  /// 直接上传数据
  Future<bool> _uploadDirectly(Map<String, dynamic> data, String authToken) async {
    try {
      print('📤 开始直接上传到CloudBase...');
      print('🔍 上传URL: $_baseUrl/sync/upload');
      print('🔍 认证Token: ${authToken.length > 20 ? '${authToken.substring(0, 20)}...' : authToken}');

      final response = await http.post(
        Uri.parse('$_baseUrl/sync/upload'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'data': data,
          'timestamp': DateTime.now().toIso8601String(),
          'envId': _envId,
        }),
      ).timeout(Duration(seconds: 60));

      print('🔍 上传响应状态: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          print('✅ CloudBase数据上传成功');
          print('   数据大小: ${(jsonEncode(data).length / 1024).toStringAsFixed(2)} KB');
          return true;
        } else {
          print('❌ CloudBase API返回错误: ${responseData['message']}');
          return false;
        }
      } else {
        print('❌ CloudBase上传失败: ${response.statusCode}');
        print('   响应内容: ${response.body}');
        return false;
      }

    } catch (e) {
      print('❌ CloudBase直接上传异常: $e');
      return false;
    }
  }

  /// 分批上传数据
  Future<bool> _uploadInBatches(Map<String, dynamic> data, String authToken) async {
    try {
      print('📦 开始分批上传到CloudBase...');

      // 提取小说数据进行分批
      final novels = data['novels'] as List? ?? [];
      if (novels.isEmpty) {
        print('❌ 没有小说数据需要上传');
        return false;
      }

      // 按每批3本小说分批（确保每批小于5MB）
      const batchSize = 3;
      final totalBatches = (novels.length / batchSize).ceil();
      
      print('📦 分为 $totalBatches 批上传（每批$batchSize本小说）');

      for (int i = 0; i < totalBatches; i++) {
        final startIndex = i * batchSize;
        final endIndex = (startIndex + batchSize < novels.length) ? startIndex + batchSize : novels.length;
        final batchNovels = novels.sublist(startIndex, endIndex);
        
        final batchData = {
          'novels': batchNovels,
          'batchInfo': {
            'batchIndex': i + 1,
            'totalBatches': totalBatches,
            'isLastBatch': i == totalBatches - 1,
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

        // 如果是最后一批，包含其他数据
        if (i == totalBatches - 1) {
          batchData['knowledgeDocuments'] = data['knowledgeDocuments'] ?? [];
          batchData['characterCards'] = data['characterCards'] ?? [];
          batchData['characterTypes'] = data['characterTypes'] ?? [];
          batchData['userSettings'] = data['userSettings'] ?? {};
        }

        print('📤 上传批次 ${i + 1}/$totalBatches (${batchNovels.length}本小说)...');
        
        final success = await _uploadDirectly(batchData, authToken);
        if (!success) {
          print('❌ 批次 ${i + 1} 上传失败');
          return false;
        }

        print('✅ 批次 ${i + 1} 上传成功');
        
        // 批次间延迟
        if (i < totalBatches - 1) {
          await Future.delayed(Duration(milliseconds: 1000));
        }
      }

      print('🎉 所有批次上传完成！');
      return true;

    } catch (e) {
      print('❌ 分批上传异常: $e');
      return false;
    }
  }

  /// 从CloudBase下载数据
  Future<Map<String, dynamic>?> downloadData(String authToken) async {
    try {
      print('📥 开始从CloudBase下载数据...');

      final response = await http.get(
        Uri.parse('$_baseUrl/sync/download'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
      ).timeout(Duration(seconds: 30));

      print('🔍 下载响应状态: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          print('✅ CloudBase数据下载成功');
          final data = responseData['data'] as Map<String, dynamic>?;
          if (data != null) {
            print('   下载数据大小: ${(response.body.length / 1024).toStringAsFixed(2)} KB');
            return data;
          } else {
            print('ℹ️ 云端暂无数据');
            return null;
          }
        } else {
          print('❌ CloudBase API返回错误: ${responseData['message']}');
          return null;
        }
      } else {
        print('❌ CloudBase下载失败: ${response.statusCode}');
        print('   响应内容: ${response.body}');
        return null;
      }

    } catch (e) {
      print('❌ CloudBase下载异常: $e');
      return null;
    }
  }

  /// 检查CloudBase连接状态
  Future<bool> checkConnection() async {
    try {
      print('🔍 检查CloudBase连接状态...');
      print('🔍 连接地址: $_baseUrl/health?_api_path=health');

      final response = await http.get(
        Uri.parse('$_baseUrl/health?_api_path=health'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(Duration(seconds: 10));

      print('🔍 响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('📊 服务器响应: $data');
        // 检查正确的状态字段
        if (data['status'] == 'healthy') {
          print('✅ CloudBase连接正常');
          print('📊 服务器环境: ${data['environment']}');
          print('📊 服务器消息: ${data['message']}');
          return true;
        }
      }

      print('❌ CloudBase连接异常: 状态码 ${response.statusCode}');
      print('📄 响应内容: ${response.body}');
      return false;

    } catch (e) {
      print('❌ CloudBase连接检查失败: $e');
      print('🔍 请检查服务器是否运行在 $_baseUrl');
      return false;
    }
  }

  /// 显示成功提示
  void showSuccessMessage(String message) {
    Get.snackbar(
      '同步成功',
      message,
      backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
      colorText: Get.theme.primaryColor,
      duration: Duration(seconds: 3),
    );
  }

  /// 显示错误提示
  void showErrorMessage(String message) {
    Get.snackbar(
      '同步失败',
      message,
      backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
      colorText: Get.theme.colorScheme.error,
      duration: Duration(seconds: 5),
    );
  }
}
