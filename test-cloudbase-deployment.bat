@echo off
setlocal enabledelayedexpansion

REM 测试腾讯云CloudBase部署
REM 环境ID: novel-app-2gywkgnn15cbd6a8

echo ========================================
echo 测试腾讯云CloudBase部署
echo 环境ID: novel-app-2gywkgnn15cbd6a8
echo ========================================
echo.

set API_BASE=https://api.dznovel.top

echo [INFO] 开始测试API接口...

REM 1. 测试健康检查
echo [TEST 1] 健康检查接口...
curl -s -o nul -w "HTTP状态码: %%{http_code}" "%API_BASE%/health"
echo.
if %errorlevel% equ 0 (
    echo [✓] 健康检查接口正常
) else (
    echo [✗] 健康检查接口异常
)
echo.

REM 2. 测试管理员登录接口
echo [TEST 2] 管理员登录接口...
curl -s -X POST -H "Content-Type: application/json" -d "{\"username\":\"admin\",\"password\":\"admin123\"}" -o temp_response.json "%API_BASE%/admin/login"
if %errorlevel% equ 0 (
    echo [✓] 管理员登录接口响应正常
    type temp_response.json
    echo.
) else (
    echo [✗] 管理员登录接口异常
)
if exist temp_response.json del temp_response.json
echo.

REM 3. 测试用户注册接口
echo [TEST 3] 用户注册接口结构...
curl -s -X POST -H "Content-Type: application/json" -d "{}" -o temp_response.json "%API_BASE%/auth/register"
if %errorlevel% equ 0 (
    echo [✓] 用户注册接口响应正常
    type temp_response.json
    echo.
) else (
    echo [✗] 用户注册接口异常
)
if exist temp_response.json del temp_response.json
echo.

REM 4. 测试套餐接口
echo [TEST 4] 套餐接口...
curl -s -o temp_response.json "%API_BASE%/packages"
if %errorlevel% equ 0 (
    echo [✓] 套餐接口响应正常
    type temp_response.json
    echo.
) else (
    echo [✗] 套餐接口异常
)
if exist temp_response.json del temp_response.json
echo.

REM 5. 测试CORS配置
echo [TEST 5] CORS配置...
curl -s -H "Origin: http://localhost:8080" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: Content-Type" -X OPTIONS -o nul -w "HTTP状态码: %%{http_code}" "%API_BASE%/auth/login"
echo.
if %errorlevel% equ 0 (
    echo [✓] CORS配置正常
) else (
    echo [✗] CORS配置可能有问题
)
echo.

REM 显示测试结果
echo ==========================================
echo 测试完成！
echo.
echo 📊 测试结果总结:
echo • API基础地址: %API_BASE%
echo • 健康检查: 已测试
echo • 管理员登录: 已测试
echo • 用户注册: 已测试
echo • 套餐接口: 已测试
echo • CORS配置: 已测试
echo.
echo 🔗 重要链接:
echo • CloudBase控制台: https://console.cloud.tencent.com/tcb/env/index?envId=novel-app-2gywkgnn15cbd6a8
echo • API文档: %API_BASE%/health
echo • 管理后台: https://api.dznovel.top/admin
echo.
echo 📝 如果测试失败，请检查:
echo 1. 云函数是否部署成功
echo 2. HTTP触发器是否配置正确
echo 3. 网络连接是否正常
echo 4. CloudBase环境是否正常运行
echo ==========================================

pause
